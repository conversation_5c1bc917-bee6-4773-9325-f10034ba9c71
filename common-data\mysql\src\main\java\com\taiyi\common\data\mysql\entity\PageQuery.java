package com.taiyi.common.data.mysql.entity;

/**
 * <AUTHOR>
 * @Description: 分页查询参数实体
 * <p>
 * 条件优先级从低到高
 * 低 -> 高
 * 正则模糊 -> 大于、小于 -> 相等
 */
public class PageQuery<T> {

    private int pageNo = 1;

    private int pageSize = 10;

    private T eqEntity;

    private T gteEntity;

    private T lteEntity;

    /**
     * 查询 正则表达式
     */
    private T regEntity;

    /**
     * 排序字段名
     */
    private String sortField;

    /**
     * 排序方式，asc，desc
     */
    private String sortType;

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public T getEqEntity() {
        return eqEntity;
    }

    public void setEqEntity(T eqEntity) {
        this.eqEntity = eqEntity;
    }

    public T getGteEntity() {
        return gteEntity;
    }

    public void setGteEntity(T gteEntity) {
        this.gteEntity = gteEntity;
    }

    public T getLteEntity() {
        return lteEntity;
    }

    public void setLteEntity(T lteEntity) {
        this.lteEntity = lteEntity;
    }

    public T getRegEntity() {
        return regEntity;
    }

    public void setRegEntity(T regEntity) {
        this.regEntity = regEntity;
    }

    public String getSortField() {
        return sortField;
    }

    public void setSortField(String sortField) {
        this.sortField = sortField;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }
}
