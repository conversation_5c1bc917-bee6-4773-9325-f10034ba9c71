package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * 台账权限关联表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw_auth_foreign")
public class RawAuthForeign extends CommonMySqlEntity {
    /**
     * 关联账户字段
     */
    private Integer authAgent;

    /**
     * 权限表账户字段
     */
    private String authColumn;

    private Boolean ifDeleted;

    private String rawAuthId;

    /**
     * 关联字段
     */
    private String rawColumn;

    /**
     * 权限表关联字段
     */
    private String tableColumn;

}