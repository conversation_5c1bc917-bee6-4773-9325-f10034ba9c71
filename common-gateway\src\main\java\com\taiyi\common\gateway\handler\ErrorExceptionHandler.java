package com.taiyi.common.gateway.handler;

import com.taiyi.common.gateway.common.GatewayConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.web.ErrorProperties;
import org.springframework.boot.autoconfigure.web.ResourceProperties;
import org.springframework.boot.autoconfigure.web.reactive.error.DefaultErrorWebExceptionHandler;
import org.springframework.boot.web.error.ErrorAttributeOptions;
import org.springframework.boot.web.reactive.error.ErrorAttributes;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.server.ServerRequest;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ErrorExceptionHandler extends DefaultErrorWebExceptionHandler {

    public static final String REQUEST_ID = "Request-Id";

    Logger logger = LoggerFactory.getLogger(getClass());

    public ErrorExceptionHandler(ErrorAttributes errorAttributes, ResourceProperties resourceProperties,
                                 ErrorProperties errorProperties, ApplicationContext applicationContext) {
        super(errorAttributes, resourceProperties, errorProperties, applicationContext);
    }

    @Override
    protected Map<String, Object> getErrorAttributes(ServerRequest request, ErrorAttributeOptions options) {

        String requestId = request.headers().firstHeader(REQUEST_ID);

        Throwable error = super.getError(request);
        logger.info("request :{}, 请求处理出错，错误信息为：{}", requestId, error.getMessage());

        Map<String, Object> map = new LinkedHashMap<>();
        map.put("code", "C100");
        map.put("message", GatewayConstant.SERVER_ERROR);
        map.put("tips", GatewayConstant.SERVER_ERROR);
        map.put("data", error.getMessage());

        return map;
    }

    @Override
    protected int getHttpStatus(Map<String, Object> errorAttributes) {
        return HttpStatus.INTERNAL_SERVER_ERROR.value();
    }

}
