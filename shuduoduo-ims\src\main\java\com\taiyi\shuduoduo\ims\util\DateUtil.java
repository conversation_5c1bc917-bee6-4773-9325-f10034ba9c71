package com.taiyi.shuduoduo.ims.util;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;

public class DateUtil {

    /**
     * 获取上个月的同一天或最后一天
     *
     * @param date 传入的日期
     * @return 上个月的相同日期或最后一天
     */
    public static LocalDate getSameDayOrLastDayOfPreviousMonth(LocalDate date) {
        // 获取上个月的日期
        LocalDate previousMonthDate = date.minusMonths(1);

        // 获取上个月的最后一天
        LocalDate lastDayOfPreviousMonth = previousMonthDate.with(TemporalAdjusters.lastDayOfMonth());

        // 如果上个月的日期超出了该月的最后一天，则返回最后一天
        if (date.getDayOfMonth() > lastDayOfPreviousMonth.getDayOfMonth()) {
            return lastDayOfPreviousMonth;
        } else {
            // 否则返回上个月的同一天
            return previousMonthDate.withDayOfMonth(date.getDayOfMonth());
        }
    }

    /**
     * 获取上一年的同一天或最后一天
     *
     * @param date 传入的日期
     * @return 上一年的相同日期或最后一天
     */
    public static LocalDate getSameDayOrLastDayOfPreviousYear(LocalDate date) {
        // 获取上一年的日期
        LocalDate previousYearDate = date.minusYears(1);

        // 获取上一年的最后一天
        LocalDate lastDayOfPreviousYearMonth = previousYearDate.with(TemporalAdjusters.lastDayOfMonth());

        // 如果上一年的日期超出了该月的最后一天，则返回最后一天
        if (date.getDayOfMonth() > lastDayOfPreviousYearMonth.getDayOfMonth()) {
            return lastDayOfPreviousYearMonth;
        } else {
            // 否则返回上一年的同一天
            return previousYearDate.withDayOfMonth(date.getDayOfMonth());
        }
    }

    public static String getPreviousQuarter(LocalDate date, int quartersBack) {
        // 获取当前年份和季度
        int year = date.getYear();
        int currentQuarter = (date.getMonthValue() - 1) / 3 + 1;
        // 计算目标季度和年份
        int targetQuarter = currentQuarter - quartersBack;
        while (targetQuarter <= 0) {
            year -= 1;
            targetQuarter += 4;
        }
        // 格式化为 yyyyqq 格式
        return String.format("%d%02d", year, targetQuarter);
    }

    public static String getPreviousHalfYear(LocalDate date, int halfYearsBack) {
        // 获取当前年份和半年期
        int year = date.getYear();
        int currentHalf = (date.getMonthValue() <= 6) ? 1 : 2;  // 判断当前是上半年 (1) 还是下半年 (2)
        // 计算目标半年期和年份
        int targetHalf = currentHalf - halfYearsBack;
        while (targetHalf <= 0) {
            year -= 1;
            targetHalf += 2;  // 每年有两个半年期，回退时将半年期加2
        }
        // 格式化为 yyyyhh 格式
        return String.format("%d%02d", year, targetHalf);
    }

    public static void main(String[] args) {
        LocalDate date = LocalDate.of(2024, 8, 31);
        /*LocalDate resultDate = getSameDayOrLastDayOfPreviousMonth(date);
        System.out.println("上个月的日期: " + resultDate);  // 输出: 2024-02-29 (因为2024年是闰年)

        // 测试 2024 年 2 月 29 日
        LocalDate date1 = LocalDate.of(2024, 2, 29);
        LocalDate resultDate1 = getSameDayOrLastDayOfPreviousYear(date1);
        System.out.println("上一年的日期: " + resultDate1);  // 输出: 2023-02-28*/
        String quarter = getPreviousHalfYear(date, 0);
        String quarter1 = getPreviousHalfYear(date, 2);
        String quarter2 = getPreviousHalfYear(date, 3);
        String quarter3 = getPreviousHalfYear(date, 4);
        String quarter4 = getPreviousHalfYear(date, 5);
        String quarter5 = getPreviousHalfYear(date, 6);

        System.out.println(quarter);

    }
}

