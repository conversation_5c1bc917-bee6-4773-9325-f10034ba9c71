package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.lang.Assert;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import com.taiyi.shuduoduo.common.connector.api.service.DbRpcService;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class PlateServiceTest {

    @Autowired
    PlateService service;

    @Autowired
    DbRpcService dbRpcService;

    @Test
    void syncData() {
        Assert.isTrue(service.syncPlate("77454437c4624f099903dbfb969c5ce7"));
    }

    @Test
    void queryData() {
        DbDto.DbQueryBySql dbDto = new DbDto.DbQueryBySql();
        dbDto.setHost("*************");
        dbDto.setPort(19048);
        dbDto.setDatabase("zao_702");
        dbDto.setType("DREMIO");
        dbDto.setUsername("");
        dbDto.setPassword("!");
        dbDto.setSql("SELECT 垃圾桶.经度 AS \"垃圾桶.经度\"," +
                "垃圾桶.纬度 AS \"垃圾桶.纬度\"," +
                "垃圾桶.异常告警 AS \"垃圾桶.异常告警\"," +
                "垃圾桶.告警时间 AS \"垃圾桶.告警时间\"," +
                "垃圾桶.sync_code AS \"垃圾桶.sync_code\"," +
                "垃圾桶.告警地点 AS \"垃圾桶.告警地点\"," +
                "垃圾桶.作业进度 AS \"垃圾桶.作业进度\"," +
                "垃圾桶.id AS \"垃圾桶.id\"," +
                "垃圾桶.最近清桶时间 AS \"垃圾桶.最近清桶时间\"," +
                "垃圾桶.告警内容 AS \"垃圾桶.告警内容\"," +
                "垃圾桶.名称 AS \"垃圾桶.名称\"," +
                "垃圾桶.comm_name AS \"垃圾桶.comm_name\"," +
                "垃圾桶.近30日道路保洁投诉量 AS \"垃圾桶.近30日道路保洁投诉量\"," +
                "垃圾桶.comm_id AS \"垃圾桶.comm_id\" " +
                "FROM ads.垃圾桶 LIMIT 200 OFFSET 0");
        List<Map<String, Object>> maps = dbRpcService.queryBySql(dbDto);
        maps.forEach(System.out::println);

    }
}