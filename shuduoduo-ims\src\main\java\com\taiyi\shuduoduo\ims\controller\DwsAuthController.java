package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.entity.DwsAuth;
import com.taiyi.shuduoduo.ims.service.DwsAuthService;
import com.taiyi.shuduoduo.ims.vo.DwsAuthVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 指标权限功能
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/dws/auth")
@Validated
public class DwsAuthController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DwsAuthService dwsAuthService;

    /**
     * 新增授权数据
     *
     * @param t 新增对象
     * @return Boolean
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated DwsAuthVo.InsertRequest t) {
        boolean f;
        try {
            f = dwsAuthService.saveBatchDwsAuth(t);
        } catch (Exception e) {
            logger.error("保存指标权限失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据角色id 查询指标列表
     *
     * @param roleId 角色id
     * @return List
     */
    @GetMapping("/list/{roleId}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("roleId") String roleId) {
        try {
            List<DwsAuth> t = dwsAuthService.getAuthListByRoleId(roleId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

}
