# 系统概览

## 模块说明

### common
- 功能：公共模块，包含基础配置和工具类。
- 配置文件：application-lishang.properties。
- 日志配置：logback-spring-local.xml。

### common-gateway
- 功能：网关模块，负责请求路由拦截和负载均衡。
- 配置文件：application-lishang.yml。

### common-auth
- 功能：认证模块，处理白名单，用户身份验证和权限控制。
- 配置文件：application-lishang.yml。

### common-connector
- 功能：连接器模块，提供与数据湖的连接和数据查询功能。
- 配置文件：application-lishang.yml。

### common-data
- 功能：数据模块，包含数据库和缓存相关的配置和操作。
  - 子模块：
    - mysql：MySQL 数据库配置和操作。
    - redis：Redis 缓存配置和操作。

### shuduoduo-ims (indicator manager service)
- 功能：数据指标模块，实现数据规划配置,台账配置,指标配置,数据查询,数据可视化功能。
- 配置文件：application-lishang.yml。

### shuduoduo-sms (system manager service)
- 功能：系统配置模块，包含数据字典，数据源类型，日志，附件等系统配置功能。
- 配置文件：application-lishang.yml。
- 
### shuduoduo-ums (user manager service)
- 功能：用户管理系统模块，包含用户登录功能，管理企业信息，用户信息，用户角色，菜单，权限等。
- 配置文件：application-lishang.yml。

### shuduoduo-nocodb
- 功能：数据权限校验模块，连接到sqlite数据库,提供数据权限校验功能。
- 配置文件：application-lishang.yml。

### shuduoduo-api
- 功能：API 接口模块，定义各个子系统的 API 接口。
  - 子模块：
    - shuduoduo-common-connector-api：通用连接器 API。
    - shuduoduo-ims-api：数据指标模块 API。
    - shuduoduo-nocodb-api：NocoDB 相关 API。
    - shuduoduo-sms-api：系统配置模块 API。
    - shuduoduo-ums-api：用户管理系统 API。

## 总结
以上是项目的各个模块及其功能描述，每个模块都有其特定的职责和配置文件。