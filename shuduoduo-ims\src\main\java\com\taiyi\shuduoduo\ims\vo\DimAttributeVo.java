package com.taiyi.shuduoduo.ims.vo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.MyQuery;
import com.taiyi.shuduoduo.ims.entity.DimAttribute;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class DimAttributeVo {
    @Data
    public static class AddDimAttribute {
        private String id;
        @NotNull
        private String dimId;
        private String dwsId;
        private String name;
        private String code;
        private String description;
    }

    @Data
    public static class DimAttrList{
        private String plateId;
        private String tableName;
        /**
         * 主维度编码
         */
        private String columnName;
    }

    @Data
    public static class DimAttributeList {
        /**
         * 当前页
         */
        @NotNull
        private Integer pageNo;
        /**
         * 每一页条数
         */
        @NotNull
        private Integer pageSize;

        public DimAttribute dimAttribute;

        public MyQuery getPage(){
            Page<DimAttribute> page = new Page<>();
            page.setSize(pageSize);
            page.setCurrent(pageNo);

            QueryWrapper<DimAttribute> queryWrapper=new QueryWrapper<>();
            queryWrapper.setEntity(dimAttribute);

            MyQuery myQuery = new MyQuery();
            myQuery.setPage(page);
            myQuery.setWrapper(queryWrapper);

            return myQuery;
        }
    }
}
