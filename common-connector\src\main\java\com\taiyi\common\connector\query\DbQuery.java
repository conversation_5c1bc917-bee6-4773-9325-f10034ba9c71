package com.taiyi.common.connector.query;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 数据库处理
 *
 * <AUTHOR>
 */
public interface DbQuery {

    /**
     * 获取数据库连接
     *
     * @return boolean
     */
    boolean getConnection();

    /**
     * 关闭结果集
     *
     * @throws SQLException e
     */
    void close() throws SQLException;

    /**
     * 获取数据库表
     *
     * @return List
     * @throws SQLException e
     */
    List<String> getTables() throws SQLException;

    /**
     * 获取数据库表字段
     *
     * @param tableName 表
     * @return List
     * @throws SQLException e
     */
    List<String> getColumns(String tableName) throws SQLException;

    /**
     * 条件查询
     *
     * @param tableName   表名
     * @param queryColumn 查询列
     * @param queryParam  查询条件
     * @param sortParam   排序条件
     * @return list
     * @throws SQLException e
     */
    List<Map<String, Object>> query(String tableName, List<String> queryColumn, List<Map<String, Object>> queryParam, List<Map<String, Object>> sortParam) throws SQLException;

    /**
     * sql查询
     *
     * @param sql sql语句
     * @return list
     * @throws SQLException e
     */
    List<Map<String, Object>> querySql(String sql) throws SQLException;

    /**
     * 查询表字段及类型
     *
     * @param tableName 表名
     * @return list
     * @throws SQLException e
     */
    List<Map<String, Object>> getColumnsAndType(String tableName) throws SQLException;
}
