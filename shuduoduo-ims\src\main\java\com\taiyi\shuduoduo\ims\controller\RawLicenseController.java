package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.Raw;
import com.taiyi.shuduoduo.ims.entity.RawLicense;
import com.taiyi.shuduoduo.ims.service.RawLicenseService;
import com.taiyi.shuduoduo.ims.service.RawService;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/raw/license")
@Validated
public class RawLicenseController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private RawLicenseService rawLicenseService;

    @Autowired
    private RawService rawService;

    /**
     * 认证
     *
     * @param t 认证信息
     * @return T
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated RawLicense t) {
        boolean f;
        CurrentUserUtil.CurrentUser user = CurrentUserUtil.get();
        try {
            t.setUserId(user.getId());
            t.setUsername(user.getRealName());
            f = rawLicenseService.save(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @GetMapping("/info/{rawId}")
    public ResponseEntity<ResponseVo.ResponseBean> getInfoByRawId(@PathVariable String rawId) {
        Raw raw = rawService.getById(rawId);
        if (null == raw) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, rawLicenseService.getByRawIdAndUserId(rawId, null));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


    /**
     * 删除认证
     *
     * @param id id
     * @return T
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = rawLicenseService.logicDeleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 取消认证
     *
     * @return T
     */
    @PatchMapping("/cancel/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> patch(@PathVariable String id) {
        boolean f;
        try {
            f = rawLicenseService.cancelLicense(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
