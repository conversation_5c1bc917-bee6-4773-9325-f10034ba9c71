package com.taiyi.shuduoduo.ims.dremio.vo;

import lombok.Data;

/**
 * 数据湖返回参数
 */
@Data
public class DremioVo {

    @Data
    public static class DremioLoginResponseVo {
        /**
         * 用户身份令牌
         */
        private String token;

        /**
         * 用户名
         */
        private String userName;

        /**
         * 用户名中的公司名部分
         */
        private String firstName;

        /**
         * 用户名中的个人名部分
         */
        private String lastName;

        /**
         * 令牌过期时间戳（毫秒）
         */
        private Long expires;

        /**
         * 用户邮箱地址
         */
        private String email;

        /**
         * 用户唯一标识
         */
        private String userId;

        /**
         * 是否为管理员
         */
        private Boolean admin;

        /**
         * 所属集群 ID
         */
        private String clusterId;

        /**
         * 集群创建时间戳（毫秒）
         */
        private Long clusterCreatedAt;

        /**
         * 系统版本号
         */
        private String version;

        /**
         * 用户权限信息
         */
        private Permissions permissions;

        /**
         * 用户创建时间戳（毫秒）
         */
        private Long userCreatedAt;
    }

    /**
     * 权限信息内部类
     */
    @Data
    public static class Permissions {

        /**
         * 是否允许上传用户资料
         */
        private Boolean canUploadProfiles;

        /**
         * 是否允许下载用户资料
         */
        private Boolean canDownloadProfiles;

        /**
         * 是否允许通过邮件联系支持
         */
        private Boolean canEmailForSupport;

        /**
         * 是否允许通过聊天联系支持
         */
        private Boolean canChatForSupport;

        /**
         * 是否可以查看所有任务
         */
        private Boolean canViewAllJobs;

        /**
         * 是否可以创建用户
         */
        private Boolean canCreateUser;

        /**
         * 是否可以创建角色
         */
        private Boolean canCreateRole;

        /**
         * 是否可以创建数据源
         */
        private Boolean canCreateSource;

        /**
         * 是否可以上传文件
         */
        private Boolean canUploadFile;

        /**
         * 是否可以管理节点活动
         */
        private Boolean canManageNodeActivity;

        /**
         * 是否可以管理系统引擎
         */
        private Boolean canManageEngines;

        /**
         * 是否可以管理系统队列
         */
        private Boolean canManageQueues;

        /**
         * 是否可以管理引擎路由
         */
        private Boolean canManageEngineRouting;

        /**
         * 是否可以管理系统支持设置
         */
        private Boolean canManageSupportSettings;

        /**
         * 是否可以配置安全设置
         */
        private Boolean canConfigureSecurity;
    }

    /**
     * 数据源返回参数
     */
    @Data
    public static class DremioSourceResponse {
        private String id;
        private String name;
        private String type;
        private String status;
        private String createdAt;
    }
}
