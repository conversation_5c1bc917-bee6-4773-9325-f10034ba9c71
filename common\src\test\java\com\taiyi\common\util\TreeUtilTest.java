package com.taiyi.common.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import javax.swing.tree.TreeNode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

class TreeUtilTest {

    @Test
    void judgeLegal() {
        Assertions.assertTrue(TreeUtil.judgeLegal(TestTree.class));
        Assertions.assertTrue(TreeUtil.judgeLegal(TestTree1.class));
        Assertions.assertTrue(TreeUtil.judgeLegal(TestTree2.class));
        Assertions.assertFalse(TreeUtil.judgeLegal(TestNotTree1.class));
        Assertions.assertFalse(TreeUtil.judgeLegal(TestNotTree2.class));
        Assertions.assertFalse(TreeUtil.judgeLegal(TestNotTree3.class));
        Assertions.assertFalse(TreeUtil.judgeLegal(TestNotTree4.class));
    }

    @Test
    void build() {

        TestTree p100 = new TestTree("100", "0");
        TestTree p110 = new TestTree("110", "100");
        TestTree p111 = new TestTree("111", "110");
        TestTree p120 = new TestTree("120", "100");

        TestTree p200 = new TestTree("200", "0");

        TestTree p310 = new TestTree("310", "300");

        List<TestTree> list = new ArrayList<>();
        list.add(p100);
        list.add(p110);
        list.add(p111);
        list.add(p120);

        list.add(p200);

        list.add(p310);

        List<TreeNode> treeNodes = TreeUtil.build(list, TestTree.class, "0");
        System.out.println(treeNodes);
        Assertions.assertTrue(treeNodes.size() == 2);
    }

}

class TestTree {
    String id;
    String pid;
    List<TestTree> children;

    public TestTree() {
    }


    public TestTree(String id, String pid) {
        this.id = id;
        this.pid = pid;
    }

    @Override
    public String toString() {
        return "TestTree{" +
                "id='" + id + '\'' +
                ", pid='" + pid + '\'' +
                ", children=" + children +
                '}';
    }
}

class TestTree1 {
    String id;
    String pid;
    Collection<TestTree> children;
}

class TestTree2 {
    Long id;
    Long pid;
    Collection<TestTree> children;
}

class TestNotTree1 {
    String id;
    String pid;
}

class TestNotTree2 {
    String id;
    String pid;
    List<TestTree> zz;
}

class TestNotTree3 {
    String id;
    String pid;
    Map children;
}

class TestNotTree4 {
    String id;
    Long pid;
    Collection<TestTree> children;
}