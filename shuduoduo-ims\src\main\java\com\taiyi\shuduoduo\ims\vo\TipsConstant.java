package com.taiyi.shuduoduo.ims.vo;

/**
 * 返回值提示常量
 *
 * <AUTHOR>
 */
public interface TipsConstant {

    String POSTGRE_SCHEMA_NOT_NULL = "Postgre数仓schema不能为空";

    String DATABASE_CONNECT_SUCCEED = "数仓连接成功";

    String DATABASE_CONNECT_FAILED = "数仓连接失败,请检查连接参数";

    String DUPLICATE_SUBJECT_NAME = "主题名称重复";
    String DATA_ENGINE_FAIL = "数仓连接失败";
    String DATA_ENGINE_NULL = "数仓未配置";

    String USER_NOT_FOUND = "未找到用户";

    String NAME_DUPLICATE = "名称重复，请重新输入";

    String CODE_DUPLICATE = "编码已存在";

    String RAW_DETAIL_NOT_EXIST = "台账不存在";

    String DWS_DETAIL_NOT_EXIST = "指标不存在";

    String DWS_UNION_INFO_NOT_FOUND = "未找到指标挂接数据";

    String PLATE_NOT_FOUND = "板块不存在";

    String PLATE_LAYER_NOT_FOUND = "数仓分层不存在";

    String DIMENSION_DETAIL_NOT_EXIST = "维度/事实表不存在";

    String DIM_ATTR_NOT_EXIST = "查询字段不存在";

    String DATA_MODEL_NOT_EXIST = "自助模型不存在";

    String DREMIO_VIEW_NOT_EXIST = "数据湖表不存在";

    String RAW_TABLE_DETAIL_NOT_EXIST = "台账表不存在";

    String TOPIC_NOT_FOUND = "自定义报表未找到";

    String DATA_SOURCE_NOT_FOUND = "暂无数据源信息";

    String QUERY_FILTER_COLUMN_NOT_FOUND = "筛选列不存在";

    String RAW_BELONGS_PLATE_NOT_FOUND = "未找到台账所属板块信息";

    String DWS_BELONGS_PLATE_NOT_FOUND = "未找到指标所属板块信息";

    String FILTER_PARAM_NOT_ALLOWED = "请求参数中有违反安全规则元素存在，拒绝访问!";

    String SELECTED_FIELD_NOT_ALLOWED = "所选字段列不支持排序";

    String DIGITAL_JOB_NOT_EXIST = "任务不存在";

    String PAUSE_DIGITAL_JOB_FIRST = "请先暂停任务";

    String HAS_DATA_FIELD_LIST_IN_THIS_PLATE = "该板块下存在数据域信息,不可直接删除!";

    String HAS_BUSINESS_PROCESS_LIST_IN_THIS_DATA_FIELD = "该数据域下存在业务过程信息,不可直接删除!";

    String HAS_DIMENSION_LIST_IN_THIS_PLATE = "该板块下存在维度信息,不可直接删除!";

    String HAS_METRIC_LIST_IN_THIS_PLATE = "该板块下存在指标信息,不可直接删除!";

    String HAS_RAW_LIST_IN_THIS_PLATE = "该板块下存在台账信息,不可直接删除!";

    String HAS_DIM_LIST_IN_THIS_DWS = "该指标已存在数据挂接,不可直接删除!";

    String AI_NOT_FOUND = "抱歉，我无法找到相关数据，请详细描述您的问题。";

    String THIS_PROCESS_HAS_BIND_USER = "该流程已有用户绑定.";

    String EXECUTE_FLOW_FAILED = "任务执行失败:";

    String EXECUTE_FLOW_SUCCEED = "任务执行成功!";

    String FILE_DATA_FORMAT_ERROR = "文件数据格式错误";

    String HAS_USED_IN_TOPIC = "台账已创建自定义报表";
}
