package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_business_process")
public class BusinessProcess extends CommonMySqlEntity {

    /**
     * 企业id
     */
    private String companyId;
    private String name;
    private String code;
    private String description;
    private String dataFieldId;
    private boolean ifDeleted;

    /**
     * 类型 1、新建 2、开通
     */
    private Integer type;

    private Long orderBy;
}