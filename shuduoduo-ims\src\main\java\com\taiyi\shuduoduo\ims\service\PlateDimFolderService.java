package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.PlateDimFolderDao;
import com.taiyi.shuduoduo.ims.entity.PlateDimFolder;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PlateDimFolderService extends CommonMysqlService<PlateDimFolderDao, PlateDimFolder> {
    @Override
    public Class<PlateDimFolder> getEntityClass() {
        return PlateDimFolder.class;
    }

    public boolean saveByPlateId(String plateId, String companyId) {
        PlateDimFolder dimFolder = new PlateDimFolder();
        dimFolder.setCompanyId(companyId);
        dimFolder.setPlateId(plateId);
        dimFolder.setName("维度");
        return super.save(dimFolder);
    }

    /**
     * 根据板块ID 删除维度文件夹信息 物理删除
     *
     * @param plateId 板块ID
     * @return bool
     */
    public boolean logicDeleteByPlateId(String plateId) {
        List<PlateDimFolder> list = this.listByPlateId(plateId);
        if (list.isEmpty()) {
            return true;
        }
        for (PlateDimFolder plateDimFolder : list) {
            plateDimFolder.setIfDeleted(true);
        }
        return super.updateBatchById(list);
    }

    public List<PlateDimFolder> listByPlateId(String plateId) {
        QueryWrapper<PlateDimFolder> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("plate_id", plateId).orderByDesc("create_time");
        return super.list(wrapper);
    }
}