package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.util.IdUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * 文件服务器
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的上传和下载
 */
@RestController
@RequestMapping("/file")
@Validated
public class FileSystemController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 允许上传的图片类型的后缀集合
     */
    static final Set<String> imageSuffix = Collections.unmodifiableSet(
            new HashSet<>(Arrays.asList("jpg", "jpeg", "gif", "png", "webp")));

    /**
     * 允许上传的文档类型的后缀集合
     */
    static final Set<String> documentSuffix = Collections.unmodifiableSet(
            new HashSet<>(Arrays.asList("doc", "xls", "pdf", "txt", "md")));

    /**
     * 场景工厂默认logo存储路径
     */
    static final String defaultLogoPath = "/home/<USER>/factory_logos/";

    @Value(value = "${factory.logo.path:" + defaultLogoPath + "}")
    private String logoPath;

    /**
     * 场景工厂默认logo存储路径
     */
    static final String defaultDocumentPath = "/home/<USER>/factory_documents/";

    @Value(value = "${factory.document.path:" + defaultDocumentPath + "}")
    private String documentPath;


    /**
     * 上传场景工厂logo
     *
     * @param file logo 文件
     * @return 文件名
     * @throws IOException
     */
    @PostMapping("/image/upload")
    public ResponseEntity<ResponseVo.ResponseBean> uploadImage(@RequestParam("file") MultipartFile file) throws IOException {
        // 文件的原始名称
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "文件名称不能为空");
        }
        // 检查文件大小
        long fileSize = file.getSize();
        if (fileSize > 5 * 1024 * 1024) { // 5MB
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "文件大小不能超过5MB");
        }
        // 解析出文件后缀
        int index = fileName.lastIndexOf(".");
        if (index == -1) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "文件后缀不能为空");
        }
        String suffix = fileName.substring(index + 1);
        if (!imageSuffix.contains(suffix.trim().toLowerCase())) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "非法的文件类型");
        }
        Path dir = Paths.get(logoPath);
        if (!Files.isDirectory(dir)) {
            // 创建目录
            Files.createDirectories(dir);
        }
        String uuidStr = IdUtil.fastSimpleUUID();
        fileName = fileName.substring(0, fileName.lastIndexOf(".")) + uuidStr + "." + suffix;
        try {
            file.transferTo(dir.resolve(fileName));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return ResponseVo.response(MessageCode.SUCCESS, null, fileName);
    }

    /**
     * 上传文档文件  (50Mb以内)
     *
     * @param file 文档文件
     * @return 文件名
     * @throws IOException
     */
    @PostMapping("/document/upload")
    public ResponseEntity<ResponseVo.ResponseBean> uploadDocument(@RequestParam("file") MultipartFile file) throws IOException {
        // 文件的原始名称
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "文件名称不能为空");
        }
        // 检查文件大小
        long fileSize = file.getSize();
        if (fileSize > 50 * 1024 * 1024) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "文件大小不能超过50MB");
        }
        // 解析出文件后缀
        int index = fileName.lastIndexOf(".");
        if (index == -1) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "文件后缀不能为空");
        }
        String suffix = fileName.substring(index + 1);
        if (!documentSuffix.contains(suffix.trim().toLowerCase())) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "非法的文件类型");
        }
        Path dir = Paths.get(logoPath);
        if (!Files.isDirectory(dir)) {
            // 创建目录
            Files.createDirectories(dir);
        }
        String uuidStr = IdUtil.fastSimpleUUID();
        fileName = fileName.substring(0, fileName.lastIndexOf(".")) + uuidStr + "." + suffix;
        try {
            file.transferTo(dir.resolve(fileName));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return ResponseVo.response(MessageCode.SUCCESS, null, fileName);
    }

    /**
     * 获取场景工厂logo
     *
     * @param response response对象
     * @param filename 文件名称
     */
    @GetMapping("/image/{filename}")
    public ResponseEntity<Void> getImage(HttpServletResponse response, @PathVariable("filename") String filename) {
        // 输入验证，防止路径遍历攻击
        if (filename == null || filename.contains("..")) {
            logger.warn("Invalid filename: {}", filename);
            return ResponseEntity.badRequest().build();
        }
        String filePath = logoPath + "/" + filename;
        File imageFile = new File(filePath);
        if (!imageFile.exists()) {
            logger.warn("File not found: {}", filePath);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
        try (FileInputStream fis = new FileInputStream(imageFile);
             OutputStream os = response.getOutputStream()) {
            byte[] buffer = new byte[1024 * 8];
            int count;
            while ((count = fis.read(buffer)) != -1) {
                os.write(buffer, 0, count);
                os.flush();
            }
        } catch (IOException e) {
            logger.error("Error reading file: {}", filePath, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
        return ResponseEntity.ok().contentType(MediaType.IMAGE_JPEG).build();
    }
}
