package com.taiyi.shuduoduo.ims.service;

import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.TopicShareForeignDao;
import com.taiyi.shuduoduo.ims.entity.AuthAgentEnum;
import com.taiyi.shuduoduo.ims.entity.TopicShare;
import com.taiyi.shuduoduo.ims.entity.TopicShareForeign;
import com.taiyi.shuduoduo.ims.vo.TopicShareVo;
import com.taiyi.shuduoduo.ums.api.service.CompanyRpcService;
import com.taiyi.shuduoduo.ums.api.service.RoleRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TopicShareForeignService extends CommonMysqlService<TopicShareForeignDao, TopicShareForeign> {
    @Override
    public Class<TopicShareForeign> getEntityClass() {
        return TopicShareForeign.class;
    }

    @Autowired
    private TopicShareService topicShareService;

    @Autowired
    private RoleRpcService roleRpcService;

    @Autowired
    private CompanyRpcService companyRpcService;

    /**
     * 分享
     *
     * @param t 参数与
     * @return bool
     */
    public boolean saveShare(TopicShareVo.ShareParam t) {
        CurrentUserUtil.CurrentUser currentUser = CurrentUserUtil.get();
        List<TopicShareForeign> foreignList = new ArrayList<>(t.getToUsers().size());
        for (String s : t.getToUsers()) {
            TopicShareForeign topicShareForeign = new TopicShareForeign();
            topicShareForeign.setFromUser(currentUser.getId());
            topicShareForeign.setUserType(t.getUserType());
            topicShareForeign.setToUser(s);
            foreignList.add(topicShareForeign);
        }
        boolean f = super.saveBatch(foreignList);
        if (f) {
            // 添加实际分享内容
            List<String> users = new ArrayList<>();
            if (t.getUserType().equals(AuthAgentEnum.USER.getType())) {
                users = t.getToUsers();
            } else if (t.getUserType().equals(AuthAgentEnum.DEPT.getType())) {
                // 根据部门查找部门下人员
                for (String deptId : t.getToUsers()) {
                    List<String> deptUsers = companyRpcService.getUsersByDeptId(currentUser.getCompanyId(), deptId);
                    users.addAll(deptUsers);
                }
            } else {
                // 查询角色下所有人员
                for (String roleId : t.getToUsers()) {
                    List<String> roleUsers = roleRpcService.getUsersByRoleId(currentUser.getCompanyId(), roleId);
                    users.addAll(roleUsers);
                }
            }
            List<TopicShare> shareList = new ArrayList<>(users.size());
            for (String user : users) {
                TopicShare topicShare = new TopicShare();
                topicShare.setFromUser(currentUser.getId());
                topicShare.setTopicId(t.getTopicId());
                topicShare.setToUser(user);
                shareList.add(topicShare);
            }
            return topicShareService.saveBatch(shareList);
        }
        return false;
    }

    /**
     * 取消分享
     *
     * @param t t
     * @return bool
     */
    public boolean cancelShare(TopicShareVo.CancelShareParam t) {
        return topicShareService.cancelShare(t, CurrentUserUtil.get().getId());
    }

}