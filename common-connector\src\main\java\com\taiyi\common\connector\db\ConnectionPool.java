package com.taiyi.common.connector.db;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.connector.entity.DbEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据库连接池
 *
 * <AUTHOR>
 */
public class ConnectionPool<E extends DbEntity> implements DbConnection<E> {

    private final Logger logger = LoggerFactory.getLogger(ConnectionPool.class);

    private static final int MIN_SIZE = 1;

    private static final int MAX_SIZE = 50;

    /**
     * 连接池
     */
    public static final List<Connection> CONNECTION_POOL = new ArrayList<>();

    DbHandler<DbEntity> dbHandler = new DbHandler<>();

    @Override
    public void init(E e) {
        Connection conn = null;
        try {
            for (int i = 0; i < MIN_SIZE; i++) {
                conn = dbHandler.buildConnection(e);
                CONNECTION_POOL.add(conn);
            }
        } catch (Exception ex) {
            logger.error("初始化数据库连接池失败{}", ExceptionUtil.stacktraceToString(ex));
        }
    }

    @Override
    public synchronized Connection getConnection(E e) {
        if (CONNECTION_POOL.size() >= MAX_SIZE) {
            close(CONNECTION_POOL.get(0));
        }
        Connection conn = dbHandler.buildConnection(e);
        if (null != conn) {
            CONNECTION_POOL.add(conn);
        }
        return conn;
    }

    @Override
    public void close(Connection connection) {
        dbHandler.closeConnection(connection);
        CONNECTION_POOL.remove(connection);
    }
}
