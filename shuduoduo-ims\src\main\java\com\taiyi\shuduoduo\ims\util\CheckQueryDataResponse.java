package com.taiyi.shuduoduo.ims.util;

import java.util.List;
import java.util.Map;

public class CheckQueryDataResponse {

    /**
     * 捕获异常信息
     *
     * @param data 返回数据
     * @return 异常
     */
    public static String getErrorValues(List<Map<String, Object>> data) {
        for (Map<String, Object> map : data) {
            if (map.containsKey("error")) {
                return map.get("error").toString();
            }
        }
        return null;
    }
}
