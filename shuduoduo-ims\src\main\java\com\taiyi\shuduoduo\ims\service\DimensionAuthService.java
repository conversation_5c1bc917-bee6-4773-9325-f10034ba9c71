package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.DimensionAuthDao;
import com.taiyi.shuduoduo.ims.entity.DimensionAuth;
import com.taiyi.shuduoduo.ims.vo.DimAuthVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 维度权限服务
 *
 * <AUTHOR>
 */
@Service
public class DimensionAuthService extends CommonMysqlService<DimensionAuthDao, DimensionAuth> {
    @Override
    public Class<DimensionAuth> getEntityClass() {
        return DimensionAuth.class;
    }

    @Autowired
    private DimensionAuthDao dimensionAuthDao;

    /**
     * 检查是否存在
     *
     * @param dimId       维度ID
     * @param authTableId 权限表ID
     * @return bool
     */
    public boolean isExist(String dimId, String authTableId) {
        QueryWrapper<DimensionAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("dim_id", dimId).eq("auth_table_id", authTableId).last("LIMIT 1");
        return null == super.getOne(wrapper);
    }

    /**
     * 分页查询
     *
     * @param pageVo 分页参数
     * @return PageResult
     */
    public PageResult<DimAuthVo.PageResult> selectPage(DimAuthVo.PageVo pageVo) {
        Page<DimAuthVo.PageResult> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        QueryWrapper<DimAuthVo.PageResult> wrapper = new QueryWrapper<>();
        wrapper.eq("a.dim_id", pageVo.getDimId()).eq("a.if_deleted", false).orderByDesc("a.status", "a.auth_table_id");
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.like("b.auth_table", pageVo.getKeyWord());
        }
        IPage<DimAuthVo.PageResult> iPage = dimensionAuthDao.getPage(page, wrapper);
        PageResult<DimAuthVo.PageResult> result = new PageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotal(iPage.getTotal());
        result.setList(iPage.getRecords());
        return result;
    }

    /**
     * 根据权限设置更新状态
     *
     * @param dimAuthId 维度权限ID
     * @return bool
     */
    public boolean updateBySetting(String dimAuthId, Boolean status, String operator) {
        DimensionAuth rawAuth = super.getById(dimAuthId);
        rawAuth.setStatus(status);
        rawAuth.setOperator(operator);
        return super.updateById(dimAuthId, rawAuth);
    }

    /**
     * 根据维度ID查询已开启权限的权限集
     *
     * @param dimId 维度ID
     * @return 权限集
     */
    public List<DimensionAuth> getListByDimId(String dimId) {
        QueryWrapper<DimensionAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("dim_id", dimId).eq("if_deleted", false).eq("status", true);
        return super.list(wrapper);
    }
}