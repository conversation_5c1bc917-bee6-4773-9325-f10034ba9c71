package com.taiyi.shuduoduo.ims.dremio.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taiyi.shuduoduo.ims.dremio.vo.DremioVo;
import com.taiyi.shuduoduo.ims.dremio.api.DremioApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Dremio API 业务层
 *
 * <AUTHOR>
 */
@Service
public class DremioApiService {


    @Autowired
    private DremioApi dremioApi;

    /**
     * 获取token
     *
     * @param username dremio连接信息
     * @param password dremio连接信息
     * @param baseUri  dremio连接信息
     * @return token
     */
    public DremioVo.DremioLoginResponseVo getToken(String username, String password, String baseUri) {
        return dremioApi.getToken(username, password, baseUri);
    }

    /**
     * 创建用户
     * @param username 用户名
     * @param password 密码
     * @param baseUri dremio连接信息
     * @param name 用户名
     * @param userPassword 密码
     * @param realName 真实姓名
     * @return 用户id
     */
    public boolean createUser(String username, String password, String baseUri, String name, String userPassword, String realName) {
        return dremioApi.createUser(username, password, baseUri, name, userPassword, realName);
    }

    /**
     * 获取空间信息
     *
     * @param username dremio连接信息
     * @param password dremio连接信息
     * @param baseUri  dremio连接信息
     * @param spaceId  空间id
     * @return json
     */
    public String getSpace(String username, String password, String baseUri, String spaceId) {
        return dremioApi.getSpaceById(username, password, baseUri, spaceId);
    }

    /**
     * 创建space
     *
     * @param username   dremio连接信息
     * @param password   dremio连接信息
     * @param baseUri    dremio连接信息
     * @param entityType 创建类型
     * @param name       创建名称
     * @return space ID
     */
    public String createSpace(String username, String password, String baseUri, String entityType, String name) {
        return dremioApi.createSpace(username, password, baseUri, entityType, name);
    }

    /**
     * 创建文件夹
     *
     * @param username   dremio连接信息
     * @param password   dremio连接信息
     * @param baseUri    dremio连接信息
     * @param entityType 创建类型
     * @param name       创建名称
     * @param spaceName  空间名称
     * @return 文件夹ID
     */
    public String createFolder(String username, String password, String baseUri, String entityType, String name, String spaceName, List<String> path) {
        return dremioApi.createFolder(username, password, baseUri, entityType, name, spaceName, path);
    }

    /**
     * 创建数据集
     *
     * @param username   dremio连接信息
     * @param password   dremio连接信息
     * @param baseUri    dremio连接信息
     * @param entityType 创建类型
     * @param name       创建名称
     * @param spaceName  空间名称
     * @param folderName 文件夹名称
     * @param sql        创建sql语句
     * @return 数据集ID
     */
    public String createDataSet(String username, String password, String baseUri, String entityType, String name, String spaceName, String folderName, String sql) {
        return dremioApi.createDataSet(username, password, baseUri, entityType, name, spaceName, folderName, sql);
    }

    /**
     * 更新视图SQL
     *
     * @param username   用户名
     * @param password   密码
     * @param baseUri    接口地址
     * @param id         datasetId
     * @param entityType 固定值-folder/space/dataset
     * @param name       文件名
     * @param folderName 文件夹名
     * @param spaceName  空间名
     * @param sql        SQL
     */
    public boolean updateDataSet(String username, String password, String baseUri, String id, String entityType, String name, String spaceName, String folderName, String sql) {
        return dremioApi.updateDataSet(username, password, baseUri, id, entityType, name, spaceName, folderName, sql);
    }

    /**
     * 重命名数据集
     *
     * @param username   用户名
     * @param password   密码
     * @param baseUri    接口地址
     * @param spaceName  空间名
     * @param folderName 文件夹名
     * @param oldName    旧名称
     * @param newName    新名称
     * @return 返回 true/false
     */
    public boolean renameDataSet(String username, String password, String baseUri, String spaceName, String folderName, String oldName, String newName) {
        return dremioApi.renameDataSet(username, password, baseUri, spaceName, folderName, oldName, newName);
    }

    /**
     * 获取数据源列表
     *
     * @param username dremio连接信息
     * @param password dremio连接信息
     * @param baseUri  dremio连接信息
     * @return 数据源列表
     */
    public List<DremioVo.DremioSourceResponse> getSourceList(String username, String password, String baseUri) {
        List<DremioVo.DremioSourceResponse> resultList = new ArrayList<>();
        JSONArray sourceList = dremioApi.getSourceList(username, password, baseUri);
        for (Object source : sourceList) {
            JSONObject sourceJson = (JSONObject) source;

            DremioVo.DremioSourceResponse sourceResponse = new DremioVo.DremioSourceResponse();
            sourceResponse.setId(sourceJson.getString("id"));
            sourceResponse.setName(sourceJson.getString("name"));
            sourceResponse.setType(sourceJson.getString("type"));
            sourceResponse.setCreatedAt(sourceJson.getString("createdAt"));

            // 处理嵌套字段：state.status
            JSONObject state = sourceJson.getJSONObject("state");
            if (state != null && state.containsKey("status")) {
                sourceResponse.setStatus(state.getString("status"));
            }

            resultList.add(sourceResponse);
        }
        return resultList;
    }

    /**
     * 获取表字段
     *
     * @param username  用户名
     * @param password  密码
     * @param baseUri   接口地址
     * @param dataSetId 表ID
     * @return 字段列表
     */
    public List<Map> getViewFields(String username, String password, String baseUri, String dataSetId) {
        return dremioApi.getDataSetDetail(username, password, baseUri, dataSetId).toJavaList(Map.class);
    }

    /**
     * 根据路径获取视图信息
     *
     * @param username 用户名
     * @param password 密码
     * @param baseUri  接口地址
     * @param path     表ID
     * @return 字段列表
     */
    public List<Map> getDataSetByPath(String username, String password, String baseUri, String path) {
        return dremioApi.getDataSetByPath(username, password, baseUri, path).toJavaList(Map.class);
    }

    public String getViewSql(String username, String password, String baseUri, String id) {
        return dremioApi.getDataSetSql(username, password, baseUri, id);
    }

    /**
     * 删除空间
     *
     * @param username dremio连接信息
     * @param password dremio连接信息
     * @param baseUri  dremio连接信息
     * @param spaceId  空间 ID
     * @return Boolean
     */
    public boolean deleteSpace(String username, String password, String baseUri, String spaceId) {
        return dremioApi.deleteSpaceById(username, password, baseUri, spaceId);
    }

    /**
     * 删除文件夹
     *
     * @param username dremio连接信息
     * @param password dremio连接信息
     * @param baseUri  dremio连接信息
     * @param folderId 文件夹ID
     * @return Boolean
     */
    public boolean deleteFolder(String username, String password, String baseUri, String folderId) {
        return dremioApi.deleteFolderById(username, password, baseUri, folderId);
    }

    /**
     * 删除数据集
     *
     * @param username  dremio连接信息
     * @param password  dremio连接信息
     * @param baseUri   dremio连接信息
     * @param datasetId 数据集ID
     * @return Boolean
     */
    public boolean deleteDataSet(String username, String password, String baseUri, String datasetId) {
        return dremioApi.deleteDataSetById(username, password, baseUri, datasetId);
    }
}
