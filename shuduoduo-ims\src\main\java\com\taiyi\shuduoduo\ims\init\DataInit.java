package com.taiyi.shuduoduo.ims.init;


import cn.hutool.core.exceptions.ExceptionUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 初始化数据
 *
 * <AUTHOR>
 */
@Component
public class DataInit implements ApplicationRunner {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Value("${spring.profiles.active}")
    private String springProfile;

    @Override
    public void run(ApplicationArguments args) throws Exception {

        //判断当前环境，本地环境不初始化
        if ("local".equals(springProfile)) {
            return;
        }

        logger.info("IMS 启动成功，初始化数据开始。");

        long start = System.currentTimeMillis();

        try {
        } catch (Exception e) {
            logger.warn("IMS 启动初始化数据发生异常：{}", ExceptionUtil.stacktraceToString(e));
        }

        long second = (System.currentTimeMillis() - start) / 1000;
        logger.info("IMS 启动成功，初始化数据完成。花费时间：{}秒。", second);
    }
}
