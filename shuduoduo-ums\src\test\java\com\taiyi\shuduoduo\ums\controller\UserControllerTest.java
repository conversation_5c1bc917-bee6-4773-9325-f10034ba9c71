package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.taiyi.shuduoduo.ums.entity.User;
import com.taiyi.shuduoduo.ums.vo.UserPageVo;
import com.taiyi.shuduoduo.ums.vo.UserVo;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Date;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class UserControllerTest {
    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;
    private static String json_utf8 = "application/json;charset=UTF-8";

    private static String uuid = IdUtil.simpleUUID();

    @BeforeEach
    public void env() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    @Order(10)
    void save() throws Exception {
        UserVo.AddUserVo addUserVo= new UserVo.AddUserVo();
//        addUserVo.setId(uuid);
        addUserVo.setRole_ifLock(false);
        addUserVo.setRole_roleId("00000");
        addUserVo.setRole_userId(uuid);

        addUserVo.setCompanyId("1");
        addUserVo.setDepId("2");
        addUserVo.setUserId(uuid);

        addUserVo.setBirthday(new Date(2020, 9, 20));
        addUserVo.setGender(true);
        addUserVo.setIdCard("*********");
        addUserVo.setIfAdmin(true);
        addUserVo.setIfBackend(true);
        addUserVo.setIfLock(true);
        addUserVo.setMobile("*********");
        addUserVo.setNickname("zhangsannick");
        addUserVo.setPwdDigest("zhangsanpwd");
        addUserVo.setRealName("zhangsan");
        String r = mockMvc.perform(MockMvcRequestBuilders.post("/user")
                .content(JSON.toJSONString(addUserVo))
                .characterEncoding("utf8")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(20)
    void updateById() throws Exception {
        User user = new User();
        user.setBirthday(new Date(2021, 9, 20));
        user.setGender(true);
        user.setIdCard("*********");
        user.setIfAdmin(true);
        user.setIfBackend(true);
        user.setIfLock(true);
        user.setMobile("*********");
        user.setNickname("zhangsannick");
        user.setPwdDigest("zhangsanpwd");
        user.setRealName("zhangsan");
        String r = mockMvc.perform(MockMvcRequestBuilders.put("/user/" + uuid)
                .content(JSON.toJSONString(user))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(30)
    void ifLock() throws Exception {
        UserVo.lockstatus userlock = new UserVo.lockstatus();
        userlock.setIfLock(false);
        String r = mockMvc.perform(MockMvcRequestBuilders.post("/user/lock/"+uuid)
                .content(JSON.toJSONString(userlock))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(40)
    void modify() throws Exception{
        UserVo.changepwd userchangepwd=new UserVo.changepwd();
        userchangepwd.setPwdDigest("9876543");
        String r = mockMvc.perform(MockMvcRequestBuilders.post("/user/pwd/modify/" + uuid)
                .content(JSON.toJSONString(userchangepwd))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(50)
    void reset() throws Exception{
        UserVo.changepwd userchangepwd=new UserVo.changepwd();
        userchangepwd.setPwdDigest("999999999");
        String r = mockMvc.perform(MockMvcRequestBuilders.post("/user/pwd/reset/"+uuid)
                .content(JSON.toJSONString(userchangepwd))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(60)
    void get_user_info() throws Exception{
        String r = mockMvc.perform(MockMvcRequestBuilders.get("/user/" + uuid)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(70)
    void page() throws Exception {
        UserPageVo userPageVo=new UserPageVo();
        userPageVo.setPageNo(0);
        userPageVo.setPageSize(10);
        userPageVo.setIf_backend(true);
        String r = mockMvc.perform(MockMvcRequestBuilders.post("/user/page")
                .content(JSON.toJSONString(userPageVo))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(80)
    void backend_page() throws Exception {
        UserPageVo userPageVo=new UserPageVo();
        userPageVo.setPageNo(0);
        userPageVo.setPageSize(10);
        userPageVo.setIf_backend(false);
        String r = mockMvc.perform(MockMvcRequestBuilders.post("/user/backend/page")
                .content(JSON.toJSONString(userPageVo))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }
}