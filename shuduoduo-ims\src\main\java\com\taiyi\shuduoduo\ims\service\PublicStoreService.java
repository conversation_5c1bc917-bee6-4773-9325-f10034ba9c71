package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.PublicStoreDao;
import com.taiyi.shuduoduo.ims.entity.PublicStore;
import com.taiyi.shuduoduo.ims.nocodb.service.NocoDbApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * NocoDb 信息库
 *
 * <AUTHOR>
 */
@Service
public class PublicStoreService extends CommonMysqlService<PublicStoreDao, PublicStore> {
    @Override
    public Class<PublicStore> getEntityClass() {
        return PublicStore.class;
    }

    @Autowired
    private NocoDbApiService nocoDbApiService;

    public void savePublicStoreInfo() {
        List<Map> projects = nocoDbApiService.getProjects();
        for (Map map : projects) {
            PublicStore store = new PublicStore();
            store.setProjectId(map.get("id").toString());
            store.setProjectName(map.get("title").toString());
            store.setProjectPrefix(map.get("prefix").toString());
            if ("早见商城".equals(map.get("title").toString())) {
                store.setIfPublic(true);
            } else {
                store.setIfPublic(false);
            }
            super.save(store);
        }
    }

    /**
     * 获取商城nocodb信息
     *
     * @return T
     */
    public PublicStore getPublicNocoDb() {
        QueryWrapper<PublicStore> wrapper = new QueryWrapper<>();
        wrapper.eq("if_public", true).eq("if_deleted", false).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 根据项目ID获取nocodb信息
     *
     * @param projectId 项目ID
     * @return T
     */
    public PublicStore getByProjectId(String projectId) {
        QueryWrapper<PublicStore> wrapper = new QueryWrapper<>();
        wrapper.eq("project_id", projectId).eq("if_deleted", false).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public Map<String, Object> getUserNocoInfo(String companyId) {
        QueryWrapper<PublicStore> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId).eq("if_public", false).eq("if_deleted", false).last("LIMIT 1");
        PublicStore store = super.getOne(wrapper);
        Map<String, Object> map = new HashMap<>();
        if (null == store) {
            return map;
        }
        map.put("id", store.getProjectId());
        map.put("baseId", store.getBaseId());

        return map;
    }
}