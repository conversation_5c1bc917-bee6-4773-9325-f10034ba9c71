<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.taiyi.shuduoduo.ims.dao.QueryTopicDao">
    <resultMap id="BaseResultMap" type="com.taiyi.shuduoduo.ims.entity.QueryTopic">
        <id column="id" jdbcType="VARCHAR" property="id"/>

        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="company_id" jdbcType="VARCHAR" property="companyId"/>
        <result column="raw_id" jdbcType="VARCHAR" property="rawId"/>
        <result column="subject" jdbcType="VARCHAR" property="subject"/>

        <result column="selected_attr" jdbcType="JAVA_OBJECT" property="selectedAttr"
                javaType="com.alibaba.fastjson.JSONArray"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="selected_filter" jdbcType="JAVA_OBJECT" property="selectedFilter"
                javaType="com.alibaba.fastjson.JSONArray"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="selected_order" jdbcType="JAVA_OBJECT" property="selectedOrder"
                javaType="com.alibaba.fastjson.JSONArray"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>

        <result column="if_deleted" jdbcType="TINYINT" property="ifDeleted"/>
        <result column="version" jdbcType="VARCHAR" property="version"/>
        <result column="create_time" jdbcType="DATE" property="createTime"/>
        <result column="update_time" jdbcType="DATE" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id,company_id,raw_id,subject,selected_attr, selected_filter,selected_order, if_deleted, version, create_time, update_time
    </sql>

</mapper>
