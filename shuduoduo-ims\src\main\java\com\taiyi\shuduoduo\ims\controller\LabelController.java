package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.Label;
import com.taiyi.shuduoduo.ims.service.LabelService;
import com.taiyi.shuduoduo.ims.vo.LabelVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/label")
@Validated
public class LabelController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private LabelService labelService;

    /**
     * 新增数据
     *
     * @param t t
     * @return vo
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated LabelVo t) {
        Label label = BeanUtil.copy(t, Label.class);
        label.setCompanyId(CurrentUserUtil.get().getCompanyId());
        label.setUpdatedBy(CurrentUserUtil.get().getId());
        if (StringUtils.isBlank(t.getId())) {
            label.setCreatedBy(CurrentUserUtil.get().getId());
            label.setOrderBy(labelService.getMaxOrder());
        }
        if (StringUtils.isNotBlank(t.getId()) ? labelService.exists(t.getId(), label) : labelService.exists(label)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.NAME_DUPLICATE);
        }
        boolean f;
        try {
            f = labelService.saveOrUpdate(label);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 详情数据
     *
     * @param id id
     * @return T
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            LabelVo t = labelService.getDetailById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 标签删除
     *
     * @param id id
     * @return bool
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = labelService.logicDeleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 标签-分页查询
     *
     * @param query 筛选条件
     * @return T
     */
    @PostMapping("/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody LabelVo.LabelPageVo query) {
        if (query.getType() == null) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR);
        }
        try {
            PageResult page = labelService.getPage(query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 标签-列表查询
     *
     * @param query 筛选条件
     * @return T
     */
    @PostMapping("/list")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> list(@RequestBody LabelVo.LabelListVo query) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, labelService.getList(query));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据标签查询关联的指标列表
     *
     * @param id 标签id
     * @return T
     */
    @GetMapping("/dws/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getDwsListByLabelId(@PathVariable("id") String id) {
        if (null == labelService.getById(id)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, labelService.getDwsListByLabelId(id));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 上移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/up/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> up(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (labelService.sequence(id, orderBy, Label.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 下移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/down/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> down(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (labelService.sequence(id, orderBy, Label.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

}
