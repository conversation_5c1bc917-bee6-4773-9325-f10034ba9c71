package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * 板块分层
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_plate_layer")
public class PlateLayer extends CommonMySqlEntity {

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 编码
     */
    private String code;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

    /**
     * 排序字段
     */
    private Long orderBy;

    /**
     * 板块ID
     */
    private String plateId;

    /**
     * dremio 文件夹ID
     */
    private String folderId;

}