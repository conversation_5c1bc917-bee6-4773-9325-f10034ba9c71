package com.taiyi.shuduoduo.ums.api.service;

import com.taiyi.common.entity.MicroServer;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 角色层RPC
 *
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoUms.SERVER_NAME)
public interface RoleRpcService {

    /**
     * 根据用户id获取角色名称列表
     *
     * @param userId 用户id
     * @return 角色列表
     */
    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/role/listByUserId/{userId}")
    List<String> getRoleListByUserId(@PathVariable("userId") String userId);

    /**
     * 根据用户id获取角色列表
     *
     * @param userId 用户id
     * @return 角色列表
     */
    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/role/listIdsByUserId/{userId}")
    List<String> getRoleIdListByUserId(@PathVariable("userId") String userId);

    /**
     * 根据角色id获取用户列表
     *
     * @param companyId 公司id
     * @param roleId    角色ID
     * @return 角色列表
     */
    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/role/users/{roleId}")
    List<String> getUsersByRoleId(@RequestParam("companyId") String companyId, @PathVariable("roleId") String roleId);
}
