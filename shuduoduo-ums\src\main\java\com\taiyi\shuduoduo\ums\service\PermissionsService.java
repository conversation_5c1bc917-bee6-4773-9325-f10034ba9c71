package com.taiyi.shuduoduo.ums.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ums.dao.PermissionsDao;
import com.taiyi.shuduoduo.ums.entity.Permissions;
import com.taiyi.shuduoduo.ums.vo.PermissionsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 权限服务
 *
 * <AUTHOR>
 */
@Service
public class PermissionsService extends CommonMysqlService<PermissionsDao, Permissions> {
    @Override
    public Class<Permissions> getEntityClass() {
        return Permissions.class;
    }

    @Autowired
    private MenusService menusService;

    /**
     * 编辑权限
     *
     * @param t 参数
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean savePermission(PermissionsVo.AddParam t) {
        List<Permissions> permissionsList = new ArrayList<>();
        // 根据角色Id获取所有权限
        List<Permissions> permissions = getPermissionListByRoleId(t.getCompanyId(), t.getRoleId());
        // 删除旧权限
        boolean b = this.removePermissions(permissions);
        if (b) {
            for (String s : t.getMenuIds().split(StrUtil.COMMA)) {
                Permissions pm = new Permissions();
                pm.setRoleId(t.getRoleId());
                pm.setCompanyId(t.getCompanyId());
                pm.setMenuId(s);
                permissionsList.add(pm);
            }
            return super.saveBatch(permissionsList);
        }
        return false;
    }

    /**
     * 根据角色ID和权限ID集合删除权限
     *
     * @param roleId      角色ID
     * @param permissions 权限集合
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean removePermissions(String roleId, List<Permissions> permissions) {
        List<String> list = permissions.stream()
                .filter(obj -> !Objects.equals(obj.getMenuId(), "1") && !Objects.equals(obj.getMenuId(), "10") && !Objects.equals(obj.getMenuId(), "11"))
                .map(Permissions::getMenuId).collect(Collectors.toList());
        if (!list.isEmpty()) {
            UpdateWrapper<Permissions> wrapper = new UpdateWrapper<>();
            wrapper.set("if_deleted", true).eq("company_id", CurrentUserUtil.get().getCompanyId()).eq("role_id", roleId)
                    .in("menu_id", list);
            return super.update(wrapper);
        }
        return true;
    }

    /**
     * 根据权限ID集合删除权限
     *
     * @param permissions 权限集合
     * @return bool
     */
    public boolean removePermissions(List<Permissions> permissions) {
        List<String> list = permissions.stream()
                .filter(obj -> !Objects.equals(obj.getMenuId(), "1") && !Objects.equals(obj.getMenuId(), "10") && !Objects.equals(obj.getMenuId(), "11"))
                .map(Permissions::getId).collect(Collectors.toList());
        if (list.size() > 0) {
            UpdateWrapper<Permissions> wrapper = new UpdateWrapper<>();
            wrapper.set("if_deleted", true).in("id", list);
            return super.update(wrapper);
        }
        return true;
    }

    public List<Permissions> getPermissionListByRoleId(String companyId, String roleId) {
        QueryWrapper<Permissions> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId).eq("role_id", roleId).eq("if_deleted", false);
        List<Permissions> permissions = super.list(wrapper);
        List<Permissions> permissionsList = this.defaultPermissions(companyId, roleId);
        if (null != permissions) {
            permissionsList.addAll(permissions);
        }
        return permissionsList;
    }


    /**
     * 默认权限
     *
     * @param companyId 企业ID
     * @param roleId    角色ID
     * @return list
     */
    public List<Permissions> defaultPermissions(String companyId, String roleId) {
        List<Permissions> list = new ArrayList<>();

        Permissions permissions = new Permissions();
        permissions.setMenuId("1");
        permissions.setCompanyId(companyId);
        permissions.setRoleId(roleId);
        list.add(permissions);

        Permissions p1 = new Permissions();
        p1.setMenuId("10");
        p1.setCompanyId(companyId);
        p1.setRoleId(roleId);
        list.add(p1);

        Permissions p2 = new Permissions();
        p2.setMenuId("11");
        p2.setCompanyId(companyId);
        p2.setRoleId(roleId);
        list.add(p2);

        return list;
    }

}