package com.taiyi.shuduoduo.ums.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.Md5Utils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.redis.util.RedisRepository;
import com.taiyi.common.util.AesUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.JwtUtil;
import com.taiyi.shuduoduo.ums.entity.*;
import com.taiyi.shuduoduo.ums.lishang.util.LiShang;
import com.taiyi.shuduoduo.ums.vo.LoginVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * 登录
 *
 * <AUTHOR>
 */
@Service
public class LoginService {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 用户token前缀
     */
    public static final String TOKEN_KEY_PREFIX = "USER_TOKEN_";

    public static final long TOKEN_EXPIRE_TIME = 60 * 24 * 30;

    @Value("${lishang.clientId:''}")
    private String clientId;

    @Value("${lishang.username:''}")
    private String username;

    @Value("${lishang.password:''}")
    private String password;

    @Value("${lishang.host:''}")
    private String host;

    @Autowired
    private RedisRepository redisRepository;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private UserService userService;

    @Autowired
    private UserCompanyService userCompanyService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private PermissionsService permissionsService;

    @Autowired
    private MenusService menusService;


    /**
     * 管理员登录获取token
     *
     * @param respondJson
     * @param accessToken
     * @param userInfo
     * @param companyId
     * @param userId
     * @return
     */
    private String getToken(JSONObject respondJson, String accessToken, JSONObject userInfo, String companyId, String userId, Boolean ifBackend) {
        String avatar_big = userInfo.getString("avatar_big");
        String avatar_middle = userInfo.getString("avatar_middle");
        String avatar_thumb = userInfo.getString("avatar_thumb");
        String avatar_url = userInfo.getString("avatar_url");
        String tenant_key = userInfo.getString("tenant_key");
        String realName = userInfo.getString("realName");
        String nickName = userInfo.getString("nickName");
        String thirdUserId = userInfo.getString("thirdUserId");
        List<String> roles = new ArrayList<>();
        Long expires_in = respondJson.getLong("expires_in");
        String refresh_token = respondJson.getString("refresh_token");
        Long refresh_expires_in = respondJson.getLong("refresh_expires_in");
        String token_type = respondJson.getString("token_type");
        //生成token
        CurrentUserUtil.CurrentUser currentUser = new CurrentUserUtil.CurrentUser();
        currentUser.setAvatarBig(StringUtils.isNotEmpty(avatar_big) ? avatar_big : "");
        currentUser.setAvatarMiddle(StringUtils.isNotEmpty(avatar_middle) ? avatar_middle : "");
        currentUser.setAvatarThumb(StringUtils.isNotEmpty(avatar_thumb) ? avatar_thumb : "");
        currentUser.setAvatarUrl(StringUtils.isNotEmpty(avatar_url) ? avatar_url : "");
        currentUser.setOpenId(StringUtils.isNotEmpty(userId) ? userId : "");
        currentUser.setId(StringUtils.isNotEmpty(userId) ? userId : "");
        currentUser.setTenantKey(StringUtils.isNotEmpty(tenant_key) ? tenant_key : "");
        currentUser.setRealName(StringUtils.isNotEmpty(realName) ? realName : "");
        currentUser.setNickName(StringUtils.isNotEmpty(nickName) ? nickName : "");
        currentUser.setThirdUserId(StringUtils.isNotBlank(thirdUserId) ? thirdUserId : "");
        currentUser.setCompanyId(StringUtils.isNotEmpty(companyId) ? companyId : "");
        currentUser.setIfBackend(ifBackend);
        currentUser.setRoles(ObjectUtil.isNotEmpty(roles) ? roles : new ArrayList<>());
        currentUser.setAccessToken(StringUtils.isNotEmpty(accessToken) ? accessToken : "");
        // 过期时间一月
        currentUser.setExpiresIn(expires_in != null ? expires_in : TOKEN_EXPIRE_TIME);
        currentUser.setRefreshToken(StringUtils.isNotEmpty(refresh_token) ? refresh_token : "");
        currentUser.setRefreshExpiresIn(refresh_expires_in != null ? refresh_expires_in : 0);
        currentUser.setTokenType(StringUtils.isNotEmpty(token_type) ? token_type : "");
        return JwtUtil.getToken(currentUser);
    }

    /**
     * 登出
     *
     * @param userId 用户ID
     * @return bool
     */
    public boolean logOut(String userId) {
        return redisRepository.del(TOKEN_KEY_PREFIX + userId);
    }

    /**
     * 管理员登录
     *
     * @param adminVo 管理员登陆信息
     * @return JSONObject
     */
    public JSONObject adminLogin(LoginVo.AdminVo adminVo) {
        CurrentUserUtil.CurrentUser currentUser = CurrentUserUtil.get();
        currentUser.setCompanyId(adminVo.getCompanyId());
        CurrentUserUtil.put(currentUser);

        JSONObject returnJson = new JSONObject();
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("company_id", currentUser.getCompanyId()).eq("nickname", adminVo.getName());
        User user = userService.getOne(userQueryWrapper);

        if (ObjectUtil.isEmpty(user)) {
            returnJson.put("error", "用户名不存在");
            return returnJson;
        }
        // 解密密码
        String pwd = AesUtil.decrypt(user.getPwdDigest(), AesUtil.USER_PASSWORD_KEY_PREFIX);
        assert pwd != null;
        String encrypt = Md5Utils.getMD5(pwd.getBytes());
        if (StringUtils.isBlank(encrypt) || !encrypt.equals(adminVo.getPwd())) {
            returnJson.put("error", "用户名或密码错误");
            return returnJson;
        }
        String token;
        boolean f = true;

        // 校验token是否存在
        Object o = redisRepository.get(TOKEN_KEY_PREFIX + user.getId());
        if (null != o) {
            token = o.toString();
            f = false;
        } else {
            JSONObject userJson = new JSONObject();
            userJson.put("realName", user.getRealName());
            userJson.put("nickName", user.getNickname());
            userJson.put("thirdUserId", user.getThirdUserId());
            //生成token
            JSONObject respondJson = new JSONObject();
            respondJson.put("expires_in", TOKEN_EXPIRE_TIME);
            respondJson.put("refresh_token", "");
            respondJson.put("refresh_expires_in", "");
            respondJson.put("token_type", "");
            token = getToken(respondJson, null, userJson, adminVo.getCompanyId(), user.getId(), user.getIfAdmin());
        }
        LoginVo.AdminUserVo userVo = new LoginVo.AdminUserVo();
        userVo.setUserId(user.getId());
        userVo.setCompanyId(adminVo.getCompanyId());
        userVo.setRealName(user.getRealName());
        userVo.setThirdUserId(user.getThirdUserId());
        userVo.setToken(token);
        userVo.setIfAdmin(user.getIfAdmin());
        userVo.setExpiresIn(60 * TOKEN_EXPIRE_TIME);
        // 获取用户权限
        List<String> list = getPermissionListByUserId(user.getId(), adminVo.getCompanyId());
        userVo.setPermissions(list.size() > 0 ? list : menusService.getDefaultMenus());
        returnJson.put("user", userVo);
        if (f) {
            //存储token到redis
            try {
                redisRepository.setExpire(TOKEN_KEY_PREFIX + user.getId(), token, 60 * TOKEN_EXPIRE_TIME);
            } catch (Exception e) {
                logger.error("redis保存token失败");
                returnJson.put("error", "登陆失败");
            }
        }
        return returnJson;
    }

    public List<String> getPermissionListByUserId(String userId, String companyId) {
        List<String> res = new ArrayList<>();
        // 获取用户的权限
        // TODO: 2024/3/12 012   获取用户的权限
        // 获取用户所在的角色权限
        List<UserRole> roles = userRoleService.getRoleListByUserId(userId);
        for (UserRole userRole : roles) {
            List<Permissions> permissionsList = permissionsService.getPermissionListByRoleId(companyId, userRole.getRoleId());
            for (Permissions permissions : permissionsList) {
                Menus menus = menusService.getById(permissions.getMenuId());
                res.add(menus.getWebId());
            }
        }
        // 获取用户所在的部门权限
// TODO: 2024/3/12 012  获取用户所在的部门权限
        return res;
    }


    /**
     * 登录生成JWT token
     *
     * @param user      用户信息
     * @param companyId 公司信息
     * @return token
     */
    public String getJwtToken(User user, String companyId) {
        //生成token
        CurrentUserUtil.CurrentUser currentUser = new CurrentUserUtil.CurrentUser();
        currentUser.setId(user.getId());
        currentUser.setCompanyId(companyId);
        currentUser.setOpenId(user.getId());
        currentUser.setRealName(user.getRealName());
        currentUser.setNickName(user.getNickname());
        currentUser.setThirdUserId(user.getThirdUserId());
        currentUser.setIfBackend(user.getIfAdmin());
        currentUser.setRoles(new ArrayList<>());
        currentUser.setAccessToken("");
        currentUser.setExpiresIn(TOKEN_EXPIRE_TIME);
        currentUser.setRefreshExpiresIn(0L);
        return JwtUtil.getToken(currentUser);
    }

    /**
     * 丽尚国潮登录
     *
     * @param ticket      ticket
     * @param callbackUri service地址
     * @return 用户信息
     */
    public JSONObject liShangLogin(String ticket, String callbackUri) {
        Company company = companyService.getById(CompanyConstantsParam.COMPANY_LISHANG_ID);
        LiShang liShang = new LiShang(host, clientId, username, password);
        String userId = liShang.getLoginUserInfo(ticket, callbackUri);
        if (null == userId) {
            JSONObject json = new JSONObject();
            json.put("error", "获取登陆用户失败.");
            return json;
        }
        User user = userService.getUserInfo(company.getId(), userId);
        if (user == null) {
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("error", "用户未同步");
            return jsonObject1;
        }
        String jwtToken = getJwtToken(user, company.getId());
        //存储token到redis
        redisRepository.setExpire(TOKEN_KEY_PREFIX + user.getId(), jwtToken, 60 * TOKEN_EXPIRE_TIME);
        LoginVo.UserVo userVo = new LoginVo.UserVo();
        userVo.setCompanyId(company.getId());
        userVo.setUserId(user.getId());
        userVo.setNickName(user.getNickname());
        userVo.setRealName(user.getRealName());
        userVo.setThirdUserId(user.getThirdUserId());
        userVo.setToken(jwtToken);
        userVo.setIfAdmin(user.getIfAdmin());
        JSONObject returnJson = new JSONObject();
        // 获取用户权限
        List<String> list = getPermissionListByUserId(user.getId(), company.getId());
        userVo.setPermissions(list.size() > 0 ? list : menusService.getDefaultMenus());
        returnJson.put("user", userVo);
        return returnJson;
    }

    public String getTokenByOpenId(String companyId, String openId) {
        Object o = redisRepository.get(TOKEN_KEY_PREFIX + openId);
        if (null == o) {
            User user = userService.getUserInfo(companyId, openId);
            if (user == null) {
                return null;
            }
            String jwtToken = getJwtToken(user, companyId);
            //存储token到redis
            redisRepository.setExpire(TOKEN_KEY_PREFIX + user.getThirdUserId(), jwtToken, 60 * TOKEN_EXPIRE_TIME);
            return jwtToken;
        }
        return o.toString();
    }

    public JSONObject getLoginInfo(String token) {
        CurrentUserUtil.CurrentUser user = JwtUtil.decodeToken(token);
        LoginVo.UserVo userVo = new LoginVo.UserVo();
        userVo.setCompanyId(user.getCompanyId());
        userVo.setUserId(user.getId());
        userVo.setNickName(user.getRealName());
        userVo.setRealName(user.getRealName());
        userVo.setToken(token);
        userVo.setIfAdmin(user.getIfBackend());
        userVo.setAvatarUrl(user.getAvatarUrl());
        JSONObject returnJson = new JSONObject();
        // 获取用户权限
        List<String> list = getPermissionListByUserId(user.getId(), user.getCompanyId());
        userVo.setPermissions(list.size() > 0 ? list : menusService.getDefaultMenus());
        returnJson.put("user", userVo);
        return returnJson;
    }
}
