package com.taiyi.shuduoduo.ums.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ums.entity.UserRole;
import com.taiyi.shuduoduo.ums.vo.UserRoleVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface UserRoleDao extends CommonMysqlMapper<UserRole> {


    /**
     * 分页查询
     *
     * @param page
     * @param wrapper
     * @return
     */
    @Select("select uur.id,uur.role_id,ur.name from ums_user_role uur left join ums_role ur on uur.role_id=ur.id where ${ew.sqlSegment}")
    IPage<UserRoleVo.PageResponse> selectRolePage(Page<UserRole> page, @Param("ew") Wrapper<UserRoleVo.PageResponse> wrapper);

    /**
     * 分页查询
     *
     * @param page
     * @param wrapper
     * @return
     */
    @Select("select uur.id,uur.user_id,uu.real_name,uu.nickname,uu.third_user_id from ums_user_role uur left join ums_user uu on uur.user_id=uu.id where ${ew.sqlSegment}")
    IPage<UserRoleVo.PageResponse> selectUserPage(Page<UserRole> page, @Param("ew") Wrapper<UserRoleVo.PageResponse> wrapper);
}