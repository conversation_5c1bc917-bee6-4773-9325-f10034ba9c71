package com.taiyi.shuduoduo.nocodb.api.servie;

import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.nocodb.api.dto.NcProjectDTO;
import com.taiyi.shuduoduo.nocodb.api.dto.SqliteMasterDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(value = MicroServer.ShuduoduoNocodb.SERVER_NAME)
public interface NcProjectRpcService {

    /**
     * 获取公司noco 信息
     *
     * @param title 公司名称
     * @return noco 信息
     */
    @GetMapping(value = MicroServer.ShuduoduoNocodb.SERVER_PREFIX + "/rpc/nc/project/{title}")
    NcProjectDTO getNcProjectInfo(@PathVariable("title") String title);

    /**
     * 根据公司项目前缀获取权限表
     *
     * @param prefix 前缀
     * @return 权限表
     */
    @GetMapping(value = MicroServer.ShuduoduoNocodb.SERVER_PREFIX + "/rpc/nc/project/showTables/{prefix}")
    List<SqliteMasterDTO> showTablesWithCompanyPrefix(@PathVariable("prefix") String prefix);

    @GetMapping(value = MicroServer.ShuduoduoNocodb.SERVER_PREFIX + "/rpc/nc/project/showColumns/{table}")
    List<String> showColumns(@PathVariable("table") String table);

    @PostMapping(value = MicroServer.ShuduoduoNocodb.SERVER_PREFIX + "/rpc/nc/project/showDataMap")
    List<Map<String, Object>> showDataMap(@RequestBody SqliteMasterDTO.QueryVo.QueryParam queryParam);
}
