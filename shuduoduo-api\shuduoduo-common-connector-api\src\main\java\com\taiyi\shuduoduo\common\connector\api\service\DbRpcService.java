package com.taiyi.shuduoduo.common.connector.api.service;


import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 连接器
 *
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.CommonConnector.SERVER_NAME)
public interface DbRpcService {

    /**
     * 测试数据库连接
     *
     * @param dbDto 连接参数
     * @return bool
     */
    @PostMapping(MicroServer.CommonConnector.SERVER_PREFIX + "/rpc/engine/db/connect/test")
    boolean testConnect(@RequestBody DbDto dbDto);

    /**
     * 数据库连接
     *
     * @param dbDto 连接参数
     * @return bool
     */
    @PostMapping(MicroServer.CommonConnector.SERVER_PREFIX + "/rpc/engine/db/connect")
    boolean connect(@RequestBody DbDto dbDto);

    /**
     * 查询数据库表
     *
     * @param dbDto 数据库连接参数
     * @return list
     */
    @PostMapping(MicroServer.CommonConnector.SERVER_PREFIX + "/rpc/engine/db/getTables")
    List<String> getTables(@RequestBody DbDto dbDto);

    /**
     * 查询数据库表字段
     *
     * @param dbDto 数据库连接参数
     * @return list
     */
    @PostMapping(MicroServer.CommonConnector.SERVER_PREFIX + "/rpc/engine/db/getColumns")
    List<String> getColumns(@RequestBody DbDto.DbQuery dbDto);

    /**
     * 查询数据库表字段及字段类型
     *
     * @param dbDto 数据库连接参数
     * @return list
     */
    @PostMapping(MicroServer.CommonConnector.SERVER_PREFIX + "/rpc/engine/db/getColumnsAndType")
    List<Map<String, Object>> getColumnsAndType(@RequestBody DbDto.DbQuery dbDto);

    /**
     * 查询数据库
     *
     * @param dbDto 数据库连接参数
     * @return list
     */
    @PostMapping(MicroServer.CommonConnector.SERVER_PREFIX + "/rpc/engine/db/query")
    List<Map<String, Object>> query(@RequestBody DbDto.DbQuery dbDto);

    /**
     * 查询数据库
     *
     * @param dbQueryBySql 数据库连接参数
     * @return list
     */
    @PostMapping(MicroServer.CommonConnector.SERVER_PREFIX + "/rpc/engine/db/queryBySql")
    List<Map<String, Object>> queryBySql(@RequestBody DbDto.DbQueryBySql dbQueryBySql);


    /**
     * 数据导出
     *
     * @param dbExport 参数
     * @return url
     */
    @PostMapping(MicroServer.CommonConnector.SERVER_PREFIX + "/rpc/engine/db/export")
    String exportExcel(@RequestBody DbDto.DbExport dbExport);
}
