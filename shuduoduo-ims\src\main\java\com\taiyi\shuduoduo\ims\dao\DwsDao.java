package com.taiyi.shuduoduo.ims.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.Dws;
import com.taiyi.shuduoduo.ims.vo.DwsVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface DwsDao extends CommonMysqlMapper<Dws> {
    @Select("\tselect\n" +
            "\tt.*,\n" +
            "\tt1.status as basic_auth_status,\n" +
            "\tt1.authed_by as basic_auth_authed_by,\n" +
            "\tt1.create_time as basic_create_time,\n" +
            "\tt2.status as data_auth_status,\n" +
            "\tt2.authed_by as data_auth_authed_by,\n" +
            "\tt2.create_time as data_create_time\n" +
            "from\n" +
            "\tims_dws t\n" +
            "left join (\n" +
            "\tselect\n" +
            "\t\tc.dws_id,\n" +
            "\t\tc.status,\n" +
            "\t\tc.authed_by,\n" +
            "\t\tc.create_time\n" +
            "\tfrom\n" +
            "\t\tims_dws_basic_auth as c\n" +
            "\twhere\n" +
            "\t\tc.create_time = (\n" +
            "\t\tselect\n" +
            "\t\t\tMAX(create_time)\n" +
            "\t\tfrom\n" +
            "\t\t\tims_dws_basic_auth as e\n" +
            "\t\twhere\n" +
            "\t\t\te.dws_id = c.dws_id\n" +
            "    )\n" +
            ") t1 on\n" +
            "\tt.id = t1.dws_id\n" +
            "left join (\n" +
            "\tselect\n" +
            "\t\tq.dws_id,\n" +
            "\t\tq.status,\n" +
            "\t\tq.authed_by,\n" +
            "\t\tq.create_time\n" +
            "\tfrom\n" +
            "\t\tims_dws_data_auth as q\n" +
            "\twhere\n" +
            "\t\tq.create_time = (\n" +
            "\t\tselect\n" +
            "\t\t\tMAX(w.create_time)\n" +
            "\t\tfrom\n" +
            "\t\t\tims_dws_data_auth as w\n" +
            "\t\twhere\n" +
            "\t\t\tw.dws_id = q.dws_id\n" +
            "    )\n" +
            ") t2 on\n" +
            "\tt.id = t2.dws_id " +
            "${ew.customSqlSegment}")
    IPage<DwsVo.PageResponseVo> authPage(Page<DwsVo.PageResponseVo> page, @Param("ew") QueryWrapper<Dws> wrapper);

    @Select("SELECT t.*\n" +
            "FROM ims_dws  as t\n" +
            "${ew.customSqlSegment}")
    IPage<Dws> wikiPage(Page<Dws> page, @Param("ew") QueryWrapper<Dws> wrapper);

    @Select("SELECT * FROM (" +
            " SELECT t.*,count(1) as total\n" +
            " FROM ims_dws  as t\n" +
            " LEFT JOIN ims_label_union t1 ON t.id = t1.dws_id\n" +
            " LEFT JOIN ims_label t2 ON t1.label_id = t2.id and  t2.if_deleted =0 " +
            " ${ew.customSqlSegment} " +
            ") AS tt " +
            "WHERE tt.total = ${total}")
    IPage<Dws> wikiPageByLabels(Page<Dws> page, @Param("ew") QueryWrapper<Dws> wrapper, @Param("total") Integer total);
}