package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.IdUtil;
import com.taiyi.shuduoduo.ims.entity.DimAttribute;
import com.taiyi.shuduoduo.ims.vo.DimAttributeVo;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DimAttributeServiceTest {

    @Autowired
    private DimAttributeService dimAttributeService;

    private DimAttributeVo.AddDimAttribute addDimAttribute;
    private static final String uuid = IdUtil.simpleUUID();
    private DimAttribute dimAttribute;
    private final String dimID = "3838b640ef834f0cb70fdefd62b29c9d";

    public DimAttributeServiceTest() {
        dimAttribute=new DimAttribute();
        dimAttribute.setId(uuid);
        dimAttribute.setCode("1111");
        dimAttribute.setName("ssss");

        addDimAttribute=new DimAttributeVo.AddDimAttribute();
        addDimAttribute.setDimId(dimID);
    }

    @Test
    void addDimAttribute() {
        try {
            dimAttributeService.addDimAttribute(addDimAttribute);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    void listByCompanyId() {
    }
}