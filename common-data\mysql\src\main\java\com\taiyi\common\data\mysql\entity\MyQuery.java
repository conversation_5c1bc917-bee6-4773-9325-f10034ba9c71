package com.taiyi.common.data.mysql.entity;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MyQuery implements Query {

    private QueryWrapper wrapper;
    private Page page;

    @Override
    public QueryWrapper getQueryWrapper() {
        return wrapper;
    }

    @Override
    public Page getPage() {
        return page;
    }
}