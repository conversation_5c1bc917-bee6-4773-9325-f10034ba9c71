package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw_license")
public class RawLicense extends CommonMySqlEntity {
    private Boolean ifCancel;

    private Boolean ifDeleted;

    private String rawId;

    private String userId;

    private String username;

}