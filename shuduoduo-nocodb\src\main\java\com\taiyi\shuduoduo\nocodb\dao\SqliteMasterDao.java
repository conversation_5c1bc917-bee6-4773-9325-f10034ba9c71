package com.taiyi.shuduoduo.nocodb.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.nocodb.entity.SqliteMaster;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * SqliteMaster Dao
 *
 * <AUTHOR>
 */
@Component
@DS("sqlite")
public interface SqliteMasterDao extends CommonMysqlMapper<SqliteMaster> {

    @Select("select name from sqlite_master ${ew.customSqlSegment}")
    List<SqliteMaster> showCompanyTableWithPrefix(@Param("ew") Wrapper<SqliteMaster> wrapper);

    @Select("PRAGMA  table_info(\"${table}\")")
    List<Map<String, Object>> showColumns(@Param("table") String table);

    @Select("select ${column} from \"${table}\" ${ew.customSqlSegment}")
    List<Map<String, Object>> showDataMap(@Param("table") String table, @Param("column") String column, @Param("ew") Wrapper<Object> wrapper);

}