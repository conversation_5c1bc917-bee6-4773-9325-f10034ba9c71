package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.DataField;
import com.taiyi.shuduoduo.ims.dao.DataFieldDao;
import com.taiyi.shuduoduo.ims.entity.Plate;
import com.taiyi.shuduoduo.ims.vo.DataFieldVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class DataFieldService extends CommonMysqlService<DataFieldDao, DataField> {


    @Autowired
    private DataFieldDao dataFieldDao;
    @Autowired
    private PlateService plateService;
    @Autowired
    private BusinessProcessService businessProcessService;

    @Autowired
    private DwsService dwsService;

    @Override
    public Class<DataField> getEntityClass() {
        return DataField.class;
    }

    /**
     * 保存或者更新数据域
     *
     * @param dataField 数据域实体
     * @return 布尔值
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addDataField(DataField dataField) {
        dataField.setCompanyId(CurrentUserUtil.get().getCompanyId());
        if (StringUtils.isNotEmpty(dataField.getId())) {
            return super.updateById(dataField.getId(), dataField);
        }
        dataField.setType(1);
        dataField.setOrderBy(this.getMaxOrder());
        return super.save(dataField);
    }

    public Long getMaxOrder() {
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).orderByDesc("order_by").last("LIMIT 1");
        DataField dataField = super.getOne(wrapper);
        return null == dataField ? 1L : dataField.getOrderBy() + 1;
    }

    /**
     * 分页查询
     *
     * @param pageVo 分页
     * @return 分页结果
     */
    public PageResult<DataFieldVo.AddDataFieldList> myPage(DataFieldVo.DataFieldPageVo pageVo) {
        Page<DataField> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        page.setOptimizeCountSql(false);
        List<String> plateIds = new ArrayList<>();
        if (StrUtil.isNotBlank(pageVo.getPlateId())) {
            plateIds.add(pageVo.getPlateId());
        } else {
            //根据公司ID查询板块列表
            List<Plate> plateList = plateService.getListByCompanyId(CurrentUserUtil.get().getCompanyId());
            if (ObjectUtil.isEmpty(plateList)) {
                PageResult<DataFieldVo.AddDataFieldList> pageResult = new PageResult<>();
                pageResult.setPageNo(page.getCurrent());
                pageResult.setPageSize(page.getSize());
                pageResult.setTotal(page.getTotal());
                pageResult.setList(new ArrayList<>());
                return pageResult;
            }
            plateList.forEach(plate -> plateIds.add(plate.getId()));
        }
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.eq("d.if_deleted", false)
                .in("d.plate_id", plateIds);
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.and(queryWrapper -> queryWrapper.like("d.name", pageVo.getKeyWord())
                    .or()
                    .like("d.code", pageVo.getKeyWord())
                    .or()
                    .like("p.name", pageVo.getKeyWord()));
        }
        if (StringUtils.isNotBlank(pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getSortType())) {
            wrapper.last("order by " + pageVo.getColumn() + " " + pageVo.getSortType());
        } else {
            wrapper.orderByAsc("d.order_by");
        }
        IPage<DataFieldVo.AddDataFieldList> iPage = dataFieldDao.selectPage(page, wrapper);
        PageResult<DataFieldVo.AddDataFieldList> pageResult = new PageResult<>();
        pageResult.setPageNo(iPage.getCurrent());
        pageResult.setPageSize(iPage.getSize());
        pageResult.setTotal(iPage.getTotal());
        pageResult.setList(iPage.getRecords());
        return pageResult;
    }

    /**
     * 通过板块id查询其下所有的数据域
     *
     * @param plateId 板块id
     * @return list
     */
    public List<Map<String, String>> queryDataField(String plateId) {
        List<Map<String, String>> resList = new ArrayList<>();
        List<String> dataFieldIds = businessProcessService.getDataFieldIds(plateId);
        List<DataField> list = this.listByIds(dataFieldIds);
        for (DataField dataField : list) {
            Map<String, String> map = new HashMap<>();
            map.put("id", dataField.id);
            map.put("name", dataField.getName());
            resList.add(map);
        }
        return resList;
    }

    /**
     * 校验数据域是否存在
     *
     * @param dataField 参数
     * @return boolean
     */
    public boolean isNameExists(DataField dataField) {
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.eq("name", dataField.getName())
                .eq("plate_id", dataField.getPlateId())
                .eq("if_deleted", false)
                .ne(StringUtils.isNotBlank(dataField.getId()), "id", dataField.getId());
        return this.getOne(wrapper) != null;
    }

    /**
     * 校验数据域是否存在
     *
     * @param dataField 参数
     * @return boolean
     */
    public boolean isCodeExists(DataField dataField) {
        QueryWrapper<DataField> dataFieldQueryWrapper = new QueryWrapper<>();
        dataFieldQueryWrapper.eq("code", dataField.getCode())
                .eq("plate_id", dataField.getPlateId())
                .eq("if_deleted", false)
                .ne(StringUtils.isNotBlank(dataField.getId()), "id", dataField.getId());
        return this.getOne(dataFieldQueryWrapper) != null;
    }

    /**
     * 根据板块ID查询数据域列表
     *
     * @param plateId 板块ID
     * @return 数据域列表
     */
    public List<DataFieldVo> getListByPlatedId(String plateId, String companyId) {
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0).eq("company_id", companyId);
        if (StringUtils.isBlank(plateId)) {
            //根据公司ID查询板块列表
            List<Plate> plateList = plateService.getListByCompanyId(companyId);
            List<String> plateIds = new ArrayList<>();
            if (!plateList.isEmpty()) {
                plateList.forEach(plate -> plateIds.add(plate.getId()));
                wrapper.in("plate_id", plateIds);
            }
        } else {
            wrapper.eq("plate_id", plateId);
        }
        wrapper.orderByAsc("order_by");
        List<DataField> dataFields = dataFieldDao.selectList(wrapper);
        return BeanUtil.copyList(dataFields, DataFieldVo.class);
    }

    public List<DataFieldVo> getListAndDwsCountByPlatedId(String plateId) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0).eq("company_id", companyId);
        if (StringUtils.isBlank(plateId)) {
            //根据公司ID查询板块列表
            List<Plate> plateList = plateService.getListByCompanyId(companyId);
            List<String> plateIds = new ArrayList<>();
            if (!plateList.isEmpty()) {
                plateList.forEach(plate -> plateIds.add(plate.getId()));
                wrapper.in("plate_id", plateIds);
            }
        } else {
            wrapper.eq("plate_id", plateId);
        }
        wrapper.orderByAsc("order_by");
        List<DataField> dataFields = dataFieldDao.selectList(wrapper);
        List<DataFieldVo> res = BeanUtil.copyList(dataFields, DataFieldVo.class);
        for (DataFieldVo dataFieldVo : res) {
            dataFieldVo.setTotal(dwsService.getCountByRequiredId(dataFieldVo.getId(), 2));
        }
        return res;
    }

    /**
     * 根据板块id获取数据域列表
     *
     * @param plateId 板块id
     * @return 数据域列表
     */
    public List<DataField> getDataFieldListByPlateId(String plateId) {
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.eq("plate_id", plateId);
        wrapper.eq("if_deleted", 0);
        return this.list(wrapper);
    }

    /**
     * 根据给定的信息判断DataField存不存在
     *
     * @param dataField 数据域信息
     * @return 数据域
     */
    public DataField isDataFieldExist(DataField dataField) {
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.setEntity(dataField)
                .last("LIMIT 1");
        return this.getOne(wrapper);
    }

    public List<DataField> getListByPlatedIds(List<String> plateIds) {
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.in("plate_id", plateIds);
        wrapper.eq("if_deleted", 0);
        return this.list(wrapper);
    }

    public DataField getDataFieldByPlateAndCode(String plateId, String code) {
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.eq("plate_id", plateId).eq("if_deleted", 0).eq("code", code).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 查询数据总条数
     *
     * @return 总条数
     */
    public int selectTotal() {
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("company_id", CurrentUserUtil.get().getCompanyId());
        return super.count(wrapper);
    }

    public List<String> getIdsByPlateIds(List<String> plateIds) {
        List<String> res = new ArrayList<>();
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).in("plate_id", plateIds);
        List<DataField> dataFieldList = super.list(wrapper);
        dataFieldList.forEach(dataField -> res.add(dataField.getId()));
        return res;
    }

    public DataField getByNameAndPlateId(String dataFieldName, String plateId) {
        QueryWrapper<DataField> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("plate_id", plateId).eq("name", dataFieldName).last("LIMIT 1");
        return super.getOne(wrapper);
    }
}