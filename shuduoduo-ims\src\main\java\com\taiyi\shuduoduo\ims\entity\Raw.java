package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw")
public class Raw extends CommonMySqlEntity {

    private String companyId;

    /**
     * 板块ID
     */
    private String plateId;

    /**
     * 维度文件夹ID
     */
    private String dimFolderId;

    /**
     * 数据域ID
     */
    private String dataFieldId;

    /**
     * 业务过程ID
     */
    private String busProcessId;

    /**
     * 台账名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 类型：1、新建2、开通，默认新建
     */
    private Integer type;

    /**
     * 状态：1、未同步2、已同步，默认未同步
     */
    private Integer status;

    private Long orderBy;

    private String createBy;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

    /**
     * 最近使用时间
     */
    private Date lastTime;

    private Long total;

    private Boolean ifAuth;

}