<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>shuduoduo-v2</artifactId>
        <groupId>com.taiyi</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>shuduoduo-ums</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>common</artifactId>
            <version>${shuduoduov2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>common-data-mysql</artifactId>
            <version>${shuduoduov2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>common-data-taskManager</artifactId>
            <version>${shuduoduov2.version}</version>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-ums-api</artifactId>
            <version>${shuduoduov2.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-ims-api</artifactId>
            <version>${shuduoduov2.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>common-data-redis</artifactId>
            <version>${shuduoduov2.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-sms-api</artifactId>
            <version>${shuduoduov2.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.vaadin.external.google</groupId>
            <artifactId>android-json</artifactId>
            <version>0.0.20131108.vaadin1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-nocodb-api</artifactId>
            <version>${shuduoduov2.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>docker-maven-plugin</artifactId>-->
<!--                <version>${docker-maven-plugin.version}</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>build-images</id>-->
<!--                        <phase>install</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <imageName>shuduoduo-v2/${project.artifactId}:${project.version}</imageName>-->
<!--                    <dockerHost>http://127.0.0.1:2375</dockerHost>-->
<!--                    <dockerDirectory>${basedir}</dockerDirectory>-->
<!--                    <resources>-->
<!--                        <resource>-->
<!--                            <targetPath>/</targetPath>-->
<!--                            <directory>${project.build.directory}</directory>-->
<!--                            <include>${project.build.finalName}.jar</include>-->
<!--                        </resource>-->
<!--                    </resources>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>

</project>
