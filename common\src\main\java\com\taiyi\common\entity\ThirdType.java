package com.taiyi.common.entity;

/**
 * 1.企业微信 2.钉钉 3.飞书 4.其他
 *
 * <AUTHOR>
 */
public enum ThirdType {
    //企业微信
    WX(1, "企业微信"),
    //飞书
    FEISHU(2, "飞书"),
    //腾讯文档
    TXWORD(3, "腾讯文档"),
    //飞书文档
    FSWORD(4, "飞书文档"),
    //钉钉
    DING(5, "钉钉"),
    //其他
    OTHER(6, "其他");

    private Integer type;
    private String name;

    ThirdType(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static ThirdType getThirdTypeByType(Integer type) {
        for (ThirdType thirdType : ThirdType.values()) {
            if (thirdType.getType().equals(type)) {
                return thirdType;
            }
        }
        return null;
    }
}
