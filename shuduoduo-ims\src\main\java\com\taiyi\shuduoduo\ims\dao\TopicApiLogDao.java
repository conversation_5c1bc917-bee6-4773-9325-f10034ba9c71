package com.taiyi.shuduoduo.ims.dao;

import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.TopicApiLog;
import com.taiyi.shuduoduo.ims.vo.TopicApiLogVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface TopicApiLogDao extends CommonMysqlMapper<TopicApiLog> {


    @Select("SELECT DATE(create_time) AS time, COUNT(*) AS count " +
            "FROM ims_topic_api_log " +
            "WHERE create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE(create_time) " +
            "ORDER BY time")
    List<TopicApiLogVo.StatisticsVo> getDailyApiCallCount(@Param("days") int days);

}