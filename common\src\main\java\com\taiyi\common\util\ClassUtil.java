package com.taiyi.common.util;


import cn.hutool.core.util.ReflectUtil;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 */
public class ClassUtil {

    public static Field[] getField(Class clz) {
        return ReflectUtil.getFields(clz);
    }

    public static boolean containsField(Class clz, String fieldName) {
        Field[] fields = ReflectUtil.getFields(clz);
        for (Field field : fields) {
            if (field.getName().equals(fieldName)) {
                return true;
            }
        }
        return false;
    }

}
