package com.taiyi.shuduoduo.ims.nocodb.api;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taiyi.common.data.redis.util.RedisRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Nocodb-Api
 *
 * <AUTHOR>
 */
@Component
public class NocoDbApi {

    protected final Logger logger = LoggerFactory.getLogger(NocoDbApi.class);

    private static final String NOCODB_KEY = "NOCODB_TOKEN_";

    @Autowired
    private RedisRepository redisRepository;

    /**
     * login
     */
    private static final String GET_TOKEN_URI = "/api/v1/auth/user/signin";

    /**
     * get user projects
     */
    private static final String GET_USER_PROJECTS_URI = "/api/v1/db/meta/projects";

    /**
     * get project table list
     * full uri = /api/v1/db/meta/projects/{project_id}/tables
     */
    private static final String GET_PROJECT_TABLE_LIST_URI = "/api/v1/db/meta/projects/";

    /**
     * get table data list
     * full uri = /api/v1/db/data/{orgs}/{projectName}/{tableName}
     */
    private static final String GET_TABLE_DATA_URI = "/api/v1/db/data/null/";

    /**
     * 创建表/获取列表
     */
    public static final String CREATE_TABLE = "/api/v1/db/meta/projects/";

    /**
     * 修改和删除表
     */
    public static final String UPDATE_TABLE = "/api/v1/db/meta/tables/";

    /**
     * 添加数据
     */
    public static final String INSERT_DATA = "/api/v1/db/data/v1/";


    /**
     * API地址
     */
    @Value("${nocodb.host:null}")
    String baseUri;

    /**
     * 用户名
     */
    @Value("${nocodb.email:null}")
    String email;

    /**
     * 密码
     */
    @Value("${nocodb.password:null}")
    String password;


    /**
     * 构建请求头
     *
     * @return 请求头
     */
    public Map<String, String> getTokenHeader() {
        Map<String, String> heardMap = new HashMap<>();
        heardMap.put("xc-auth", getToken());
        return heardMap;
    }

    /**
     * 获取token
     *
     * @return token
     */
    public String getToken() {
        String o = (String) redisRepository.get(NOCODB_KEY + email);
        if (null != o) {
            return o;
        }
        Map<String, Object> map = new HashMap<>();
        map.put("email", email);
        map.put("password", password);
        String respond = HttpRequest.post(baseUri + GET_TOKEN_URI).body(JSONObject.toJSONString(map)).execute().body();
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("token")) {
            String token = json.getString("token");
            // 默认10小时
            redisRepository.setExpire(NOCODB_KEY + email, token, 60 * 60 * 10);
            return token;
        }
        logger.error("Nocodb获取token，{}", respond);
        return null;
    }

    /**
     * 获取项目列表
     *
     * @return 项目列表
     */
    public JSONArray getUserProjects() {
        String xc_oauth = getToken();
        if (null == xc_oauth) {
            return new JSONArray();
        }
        String respond = HttpRequest.get(baseUri + GET_USER_PROJECTS_URI).addHeaders(getTokenHeader()).execute().body();
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("list")) {
            return json.getJSONArray("list");
        } else {
            logger.error("Nocodb获取项目列表，{}", respond);
            return new JSONArray();
        }
    }

    /**
     * 获取项目下表列表
     *
     * @param projectId 项目ID
     * @return 表列表
     */
    public JSONArray getProjectTableList(String projectId) {
        String xc_oauth = getToken();
        if (null == xc_oauth) {
            return new JSONArray();
        }
        String respond = HttpRequest.get(baseUri + GET_PROJECT_TABLE_LIST_URI + projectId + "/tables").addHeaders(getTokenHeader()).execute().body();
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("list")) {
            return json.getJSONArray("list");
        } else {
            logger.error("Nocodb获取项目下表列表，{}", respond);
            return new JSONArray();
        }
    }

    /**
     * 获取表数据
     *
     * @param projectId 项目ID
     * @param tableName 表名
     * @return 表数据
     */
    public String getTableDataList(String projectId, String tableName, Integer offset) {
        String xc_oauth = getToken();
        if (null == xc_oauth) {
            return null;
        }
        return HttpRequest.get(baseUri + GET_TABLE_DATA_URI + projectId + "/" + tableName + "?offset=" + offset).addHeaders(getTokenHeader()).execute().body();
    }

    /**
     * 获取表数据
     *
     * @param projectId 项目ID
     * @param tableName 表名
     * @return 表数据
     */
    public String getTableDataListByOption(String projectId, String tableName, Integer offset, String option) {
        String xc_oauth = getToken();
        if (null == xc_oauth) {
            return null;
        }
        return HttpRequest.get(baseUri + GET_TABLE_DATA_URI + projectId + "/" + tableName + "?offset=" + offset + "&where=" + option).addHeaders(getTokenHeader()).execute().body();
    }

    /**
     * 创建表
     * {
     * "columns": [
     * {
     * "ai": false,
     * "altered": 1,
     * "cdf": "CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP",
     * "ck": false,
     * "clen": 45,
     * "column_name": "updated_at",
     * "ct": "varchar(45)",
     * "dt": "timestamp",
     * "dtx": "specificType",
     * "dtxp": "",
     * "dtxs": "",
     * "np": null,
     * "nrqd": true,
     * "ns": null,
     * "pk": false,
     * "rqd": false,
     * "title": "UpdatedAt",
     * "uicn": "",
     * "uidt": "DateTime",
     * "uip": "",
     * "un": false
     * },
     * {
     * "ai": false,
     * "altered": 1,
     * "cdf": "CURRENT_TIMESTAMP",
     * "ck": false,
     * "clen": 45,
     * "column_name": "created_at",
     * "ct": "varchar(45)",
     * "dt": "timestamp",
     * "dtx": "specificType",
     * "dtxp": "",
     * "dtxs": "",
     * "np": null,
     * "nrqd": true,
     * "ns": null,
     * "pk": false,
     * "rqd": false,
     * "title": "CreatedAt",
     * "uicn": "",
     * "uidt": "DateTime",
     * "uip": "",
     * "un": false
     * },
     * {
     * "ai": false,
     * "altered": 1,
     * "cdf": null,
     * "ck": false,
     * "clen": 45,
     * "column_name": "title",
     * "ct": "varchar(45)",
     * "dt": "varchar",
     * "dtx": "specificType",
     * "dtxp": "45",
     * "dtxs": "",
     * "np": null,
     * "nrqd": true,
     * "ns": null,
     * "pk": false,
     * "rqd": false,
     * "title": "Title",
     * "uicn": "",
     * "uidt": "SingleLineText",
     * "uip": "",
     * "un": false
     * },
     * {
     * "ai": true,
     * "altered": 1,
     * "cdf": null,
     * "ck": false,
     * "clen": null,
     * "column_name": "id",
     * "ct": "int(11)",
     * "dt": "int",
     * "dtx": "integer",
     * "dtxp": "11",
     * "dtxs": "",
     * "np": 11,
     * "nrqd": false,
     * "ns": 0,
     * "pk": true,
     * "rqd": true,
     * "title": "Id",
     * "uicn": "",
     * "uidt": "ID",
     * "uip": "",
     * "un": true
     * }
     * ],
     * "table_name": "Sheet-1",
     * "title": "Sheet-1"
     * }
     *
     * @param projectId 项目ID
     * @param body      表信息
     * @return 表ID
     */
    public String createTable(String projectId, String baseId, Map<String, Object> body) {
        String xc_oauth = getToken();
        if (null == xc_oauth) {
            return null;
        }
        String respond = HttpRequest.post(baseUri + CREATE_TABLE + projectId + "/" + baseId + "/tables").addHeaders(getTokenHeader()).body(JSONObject.toJSONString(body)).execute().body();
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("id")) {
            return json.getString("id");
        }
        logger.error("Nocodb创建表失败，{}", respond);
        return null;
    }

    /**
     * 删除表
     *
     * @param tableId 表ID
     * @return bool
     */
    public boolean deleteTable(String tableId) {
        String xc_oauth = getToken();
        if (null == xc_oauth) {
            return false;
        }
        String respond = HttpRequest.delete(baseUri + UPDATE_TABLE + tableId).addHeaders(getTokenHeader()).execute().body();
        if (respond.equalsIgnoreCase("true")) {
            return true;
        }
        logger.error("Nocodb删除表失败，{}", respond);
        return false;
    }

    /**
     * 修改表
     * {
     * "table_name": "users",
     * "title": "Users",
     * "project_id": "p_124hhlkbeasewh",
     * "meta": null
     * }
     *
     * @param tableId 表ID
     * @param body    表信息
     * @return bool
     */
    public boolean updateTable(String tableId, Map<String, Object> body) {
        String xc_oauth = getToken();
        if (null == xc_oauth) {
            return false;
        }
        String respond = HttpRequest.patch(baseUri + UPDATE_TABLE + tableId).addHeaders(getTokenHeader()).body(JSONObject.toJSONString(body)).execute().body();
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("msg")) {
            return json.getString("msg").contains("success");
        }
        logger.error("Nocodb修改表失败，{}", respond);
        return false;
    }

    /**
     * 新增表数据
     * {
     * "集团代码": "ccc",
     * "用户账号": "liurr",
     * "用户名称": "刘芮瑞111",
     * "项目名称": "集团项目111111"
     * }
     *
     * @param projectName 项目名称
     * @param tableName   表名
     * @param body        表数据
     * @return bool
     */
    public boolean insertData(String projectName, String tableName, String body) {
        String xc_oauth = getToken();
        if (null == xc_oauth) {
            return false;
        }
        String respond = HttpRequest.post(baseUri + INSERT_DATA + projectName + "/" + tableName).addHeaders(getTokenHeader()).body(body).execute().body();
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("Id")) {
            return true;
        }
        logger.error("Nocodb添加表数据失败，{}", respond);
        return false;
    }

}
