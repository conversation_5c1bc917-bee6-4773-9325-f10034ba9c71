package com.taiyi.shuduoduo.ums.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ums.dao.UserDao;
import com.taiyi.shuduoduo.ums.entity.Role;
import com.taiyi.shuduoduo.ums.entity.User;
import com.taiyi.shuduoduo.ums.api.dto.UserAuthDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户授权相关的服务
 *
 * <AUTHOR>
 */
@Service
public class UserAuthService extends CommonMysqlService<UserDao, User> {

    @Autowired
    private UserService userService;

    @Autowired
    private UserRoleService userRoleService;


    /**
     * 获取用户UserAuth 信息
     * <p>
     * 根据用户ID
     *
     * @param id
     * @return
     */
    public UserAuthDTO getUserAuthById(String id) {
        User user = userService.getById(id);
        return getUserAuth(user);
    }

    private UserAuthDTO getUserAuth(User user) {
        if (user == null) {
            return null;
        }
        UserAuthDTO userAuthDTO = BeanUtil.copy(user, UserAuthDTO.class);
        //todo..  查询角色权限
        return userAuthDTO;
    }

    private UserAuthDTO getUserAuth(User user, List<Role> roleList) {
        if (user == null) {
            return null;
        }
        UserAuthDTO userAuthDTO = BeanUtil.copy(user, UserAuthDTO.class);


        List<String> roles = roleList == null ? new ArrayList<>() :
                roleList.stream().map(role -> role.getName()).collect(Collectors.toList());

        userAuthDTO.setRoles(new HashSet<>(roles));
        return userAuthDTO;
    }

    @Override
    public Class<User> getEntityClass() {
        return User.class;
    }
}
