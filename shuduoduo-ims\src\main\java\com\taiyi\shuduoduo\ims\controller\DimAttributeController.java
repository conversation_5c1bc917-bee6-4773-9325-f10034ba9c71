package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.DimAttribute;
import com.taiyi.shuduoduo.ims.entity.Dimension;
import com.taiyi.shuduoduo.ims.service.DimAttributeService;
import com.taiyi.shuduoduo.ims.service.DimensionService;
import com.taiyi.shuduoduo.ims.vo.DimAttributeVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 维度属性控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dimension/attr")
@Validated
public class DimAttributeController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DimAttributeService dimAttributeService;
    @Autowired
    private DimensionService dimensionService;


    /**
     * 维度属性-新增
     *
     * @param t 维度属性实体
     * @return ResponseEntity<ResponseVo.ResponseBean>
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated DimAttributeVo.AddDimAttribute t) {
        //校验维度是否存在
        Dimension dimension = new Dimension();
        dimension.setId(t.getDimId());
        Dimension dimensionExist = dimensionService.isDimensionExist(dimension);
        if (ObjectUtil.isEmpty(dimensionExist)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "维度不存在");
        }

        try {
            if (dimAttributeService.addDimAttribute(t)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, "维度属性新增失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


    /**
     * 维度属性-删除
     *
     * @param id 维度属性id
     * @return ResponseEntity<ResponseVo.ResponseBean>
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        try {
            if (dimAttributeService.logicDeleteById(id)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 维度属性-维度属性分页查询
     *
     * @param dimAttributeList 分页查询参数
     * @return 返回数据集合
     */
    @PostMapping("/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody @Validated DimAttributeVo.DimAttributeList dimAttributeList) {
        try {
            PageResult<?> page = dimAttributeService.page(dimAttributeList.getPage());
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 维度属性列表查询--根据维度ID 查询
     *
     * @param dimId 维度ID
     * @return 返回数据集合
     */
    @GetMapping("/listByDimId/{dimId}")
    public ResponseEntity<ResponseVo.ResponseBean> listByDimId(@PathVariable @Validated String dimId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimAttributeService.getColumnList(dimId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 上移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/up/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> up(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (dimAttributeService.sequence(id, orderBy, DimAttribute.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 下移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/down/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> down(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (dimAttributeService.sequence(id, orderBy, DimAttribute.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 查询维度主键列表
     *
     * @param dimId 维度ID
     * @return T
     */
    @GetMapping("/primary/list/{dimId}")
    public ResponseEntity<ResponseVo.ResponseBean> getPrimaryList(@PathVariable("dimId") String dimId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimAttributeService.getPrimaryList(dimId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 查询维度字段列表
     *
     * @param dimId 维度ID
     * @return T
     */
    @GetMapping("/list/{dimId}")
    public ResponseEntity<ResponseVo.ResponseBean> getListByDimId(@PathVariable("dimId") String dimId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimAttributeService.getListByDimId(dimId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    @GetMapping("/all/fields")
    public ResponseEntity<ResponseVo.ResponseBean> getAllAttrs(@RequestParam("keyWord") String keyWord) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimAttributeService.getAllAttrs(keyWord));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    @GetMapping("/query")
    public ResponseEntity<ResponseVo.ResponseBean> queryNames(@RequestParam("names") String names) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimAttributeService.queryCodeByNames(Arrays.asList(names.split(StrUtil.COMMA))));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }
}
