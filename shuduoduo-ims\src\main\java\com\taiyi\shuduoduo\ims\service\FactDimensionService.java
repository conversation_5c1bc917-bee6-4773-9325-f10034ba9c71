package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ims.dao.FactDimensionDao;
import com.taiyi.shuduoduo.ims.entity.DimAttribute;
import com.taiyi.shuduoduo.ims.entity.Dimension;
import com.taiyi.shuduoduo.ims.entity.FactDimension;
import com.taiyi.shuduoduo.ims.vo.FactDimensionObjectVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FactDimensionService extends CommonMysqlService<FactDimensionDao, FactDimension> {
    @Override
    public Class<FactDimension> getEntityClass() {
        return FactDimension.class;
    }

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private DimAttributeService dimAttributeService;

    /**
     * 新增事实表与维度关系
     *
     * @param companyId 企业ID
     * @param list      关联列表
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveFactDimension(String companyId, List<FactDimensionObjectVo> list) {
        if (list.isEmpty()) {
            return true;
        }
        List<FactDimension> res = new ArrayList<>(list.size());
        for (FactDimensionObjectVo vo : list) {
            FactDimension factDimension = BeanUtil.copy(vo, FactDimension.class);
            factDimension.setCompanyId(companyId);
            res.add(factDimension);
        }
        return super.saveBatch(res);
    }

    /**
     * 根据事实表ID获取关联列表
     *
     * @param companyId 企业ID
     * @param factId    事实表ID
     * @return list
     */
    public List<FactDimensionObjectVo.ListResponse> getListByFactId(String companyId, String factId) {
        QueryWrapper<FactDimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("fact_id", factId)
                .eq("if_deleted", false)
                .orderByAsc("order_by");
        List<FactDimensionObjectVo.ListResponse> res = BeanUtil.copyList(super.list(wrapper), FactDimensionObjectVo.ListResponse.class);
        for (FactDimensionObjectVo.ListResponse response : res) {
            Dimension fact = dimensionService.getById(response.getFactId());
            DimAttribute factAttr = dimAttributeService.getById(response.getFactAttrId());
            Dimension dimension = dimensionService.getById(response.getDimId());
            DimAttribute dimensionAttr = dimAttributeService.getById(response.getDimAttrId());
            response.setFactName(fact.getName());
            response.setFactCode(fact.getCode());
            response.setFactAttrName(factAttr.getName());
            response.setFactAttrCode(factAttr.getCode());
            response.setDimName(dimension.getName());
            response.setDimCode(dimension.getCode());
            response.setDimAttrName(dimensionAttr.getName());
            response.setDimAttrCode(dimensionAttr.getCode());
        }
        return res;
    }

    /**
     * 根据事实表ID和统计周期ID获取事实表关联的统计周期数据
     *
     * @param companyId   企业ID
     * @param factId      事实表ID
     * @param periodValue 统计周期ID
     * @return
     */
    public FactDimension geDimPeriodByFactIdAndPeriodId(String companyId, String factId, String periodValue) {
        QueryWrapper<FactDimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("if_deleted", false)
                .eq("fact_id", factId)
                .eq("dim_id", periodValue)
                .eq("dim_type", 2)
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public FactDimension getDimensionByFactId(String companyId, String factId, String dimId) {
        QueryWrapper<FactDimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("if_deleted", false)
                .eq("fact_id", factId)
                .eq("dim_id", dimId)
                .eq("dim_type", 1)
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public List<FactDimension> getFactDimensionsByFactId(String companyId, String factId) {
        QueryWrapper<FactDimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("if_deleted", false)
                .eq("fact_id", factId)
                .orderByAsc("dim_type");
        return super.list(wrapper);
    }

    public List<FactDimension> getFactDimensionsByFactIds(String companyId, List<String> factIds) {
        QueryWrapper<FactDimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("if_deleted", false)
                .in("fact_id", factIds)
                .orderByAsc("dim_type");
        return super.list(wrapper);
    }

    // 在FactDimensionService中新增方法（保持原接口兼容）
    public Map<String, List<FactDimension>> getFactDimensionsMapByFactIds(String companyId, List<String> factIds) {
        List<FactDimension> list = this.getFactDimensionsByFactIds(companyId, factIds);
        return list.stream().collect(Collectors.groupingBy(FactDimension::getFactId));
    }
}