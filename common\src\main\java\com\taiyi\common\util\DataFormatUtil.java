package com.taiyi.common.util;


import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

/**
 * 数据处理格式化工具类
 *
 * <AUTHOR>
 */
public class DataFormatUtil {

    private static NumberFormat format;

    /**
     * 转百分比格式
     *
     * @param value 原数据
     * @param scale 小数点后保留几位
     * @return res
     */
    public static String toPercentString(double value, int scale) {
        format = NumberFormat.getPercentInstance(new Locale("zh", "CN"));
        format.setMaximumFractionDigits(scale);
        return format.format(value);
    }

    /**
     * 转BigDecimal
     * @param value 原数据
     * @param scale 保留位数
     * @param round 是否四舍五入
     * @return BigDecimal
     */
    public static BigDecimal toBigDecimal(String value,int scale,boolean round) {
        DecimalFormat df = null;
        if (value.indexOf(".")>0){
            df = new DecimalFormat();
        }
        return null;
    }


    public static void main(String[] args) {
        System.out.println(toPercentString(0, 2));
    }

}
