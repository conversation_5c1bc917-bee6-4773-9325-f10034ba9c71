package com.taiyi.common.handler;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.taiyi.common.entity.HttpHeaderConstant;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;

/**
 * 全局异常处理类
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@Order(value = Ordered.HIGHEST_PRECEDENCE)
public class GlobalValidationExceptionHandler {

    Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 统一参数校验异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(value = {ConstraintViolationException.class, MethodArgumentNotValidException.class})
    public ResponseEntity handleValidationException(Exception e) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String requestId = request.getHeader(HttpHeaderConstant.REQUEST_ID);
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException ex = (MethodArgumentNotValidException) e;
            Object target = ex.getBindingResult().getTarget();

            logger.info("统一参数校验错误 api request :{}, path: {}, m request params :{}, body:{}, 错误信息: {}",
                    requestId, request.getContextPath() + request.getServletPath(),
                    request.getQueryString(), JSON.toJSON(target),
                    ExceptionUtil.stacktraceToString(e));

        } else {
            logger.info("统一参数校验错误 api request :{}, path: {}, m request params :{}, 错误信息: {}",
                    requestId, request.getContextPath() + request.getServletPath(),
                    request.getQueryString(), ExceptionUtil.stacktraceToString(e));
        }

        return ResponseVo.response(MessageCode.PARAMETER_ERROR);
    }

}
