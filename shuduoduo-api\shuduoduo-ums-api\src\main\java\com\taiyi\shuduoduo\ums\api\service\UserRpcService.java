package com.taiyi.shuduoduo.ums.api.service;

import com.alibaba.fastjson.JSONObject;
import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoUms.SERVER_NAME)
public interface UserRpcService {

    /**
     * 获取用户信息通过id
     *
     * @param id 用户id
     * @return 用户信息
     */
    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/user/getById")
    UserDTO getUserInfoById(@RequestParam(value = "id",required = false) String id);

    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/user/getByThirdId")
    UserDTO getUserByThirdId(@RequestParam("companyId") String companyId, @RequestParam("thirdUserId") String thirdUserId);

    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/user/getTokenAndStoreInRedis")
    JSONObject getTokenAndStoreInRedis(@RequestParam(value = "username")String username);

    @PostMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/user/updateUserDremioInfoById")
    boolean updateUserDremioInfoById(@RequestBody UserDTO.DremioDTO dremioInfo);
}
