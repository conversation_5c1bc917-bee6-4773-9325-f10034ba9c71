package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.Raw;
import com.taiyi.shuduoduo.ims.entity.RawTable;
import com.taiyi.shuduoduo.ims.service.QueryTopicService;
import com.taiyi.shuduoduo.ims.service.RawService;
import com.taiyi.shuduoduo.ims.service.RawTableService;
import com.taiyi.shuduoduo.ims.util.SqlBuilderUtil;
import com.taiyi.shuduoduo.ims.vo.RawTableAttrVo;
import com.taiyi.shuduoduo.ims.vo.RawVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.List;

/**
 * 台账控制器
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@Api(tags = "台账")
@RestController
@RequestMapping("/raw")
@Validated
public class RawController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private RawService rawService;

    @Autowired
    private RawTableService rawTableService;

    @Autowired
    private QueryTopicService queryTopicService;

    /**
     * 新增台账
     *
     * @param t t
     * @return T
     */
    @ApiOperation("新增台账")
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@ApiParam @RequestBody @Validated RawVo.InsertParam t) {
        boolean f;
        try {
            f = rawService.saveRaw(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 台账详情
     *
     * @param id 台账ID
     * @return T
     */
    @ApiOperation("台账详情")
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@ApiParam @PathVariable("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, rawService.getDetailById(id));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 台账删除
     *
     * @param id 台账ID
     * @return T
     */
    @ApiOperation("台账删除")
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        // 检查是否有在使用的自定义报表
        if (queryTopicService.hasTopicUsedWithRawId(id)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_USED_IN_TOPIC);
        }
        try {
            f = rawService.deleteByRawId(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 台账分页
     *
     * @param query q
     * @return T
     */
    @ApiOperation("台账分页")
    @PostMapping("/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody(required = false) RawVo.RawPageVo query) {
        try {
            query.setType(query.getType() == null ? 1 : query.getType());
            PageResult<RawVo.PageResponse> page = rawService.getPage(query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 修改
     *
     * @param id 台账ID
     * @param t  修改参数
     * @return bool
     */
    @PatchMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> patch(@PathVariable("id") String id, @RequestBody @Validated RawVo.InsertParam t) {
        boolean f;
        try {
            t.setId(id);
            f = rawService.saveRaw(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取台账列表
     *
     * @param unionId 目录ID
     * @param type    id类型 1、板块2、维度文件夹3、数据域、4、业务过程 5、查询全部
     * @param keyWord 关键字查询
     * @return T
     */
    @ApiOperation("获取台账列表")
    @GetMapping("/list/{unionId}")
    public ResponseEntity<ResponseVo.ResponseBean> getListByPlateId(@PathVariable("unionId") String unionId, @RequestParam("type") @Validated @NotBlank String type, @RequestParam(value = "keyWord", required = false) String keyWord) {
        try {
            List<RawVo.ListResponse> t = rawService.getListByUnionIdAndType(unionId, Integer.valueOf(type), keyWord);
            return ResponseVo.response(MessageCode.SUCCESS, t.isEmpty() ? new ArrayList<>() : t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 上移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/up/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> up(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (rawService.sequence(id, orderBy, Raw.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 下移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/down/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> down(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (rawService.sequence(id, orderBy, Raw.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取数据浏览器台账详情
     *
     * @param id 台账ID
     * @return T
     */
    @GetMapping("/browser/detail/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> browserDetail(@PathVariable("id") String id) {
        Raw raw = rawService.getById(id);
        if (null == raw) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, rawService.getBrowserDetail(raw));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取数据浏览器台账可选字段列表
     *
     * @param id 台账ID
     * @return T
     */
    @GetMapping("/optional/list/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> optionalList(@PathVariable("id") String id) {
        Raw raw = rawService.getById(id);
        if (null == raw) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, rawService.getOptionalList(raw));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 权限配置获取台账列表
     *
     * @param catalogId 目录ID
     * @param type      type类型 1、板块2、维度文件夹3、数据域、4、业务过程 5、查询全部
     * @param keyWord   模糊查询
     * @return T
     */
    @ApiOperation("权限配置获取台账列表")
    @GetMapping("/auth/list/{catalogId}")
    public ResponseEntity<ResponseVo.ResponseBean> getListByPlateIdToAuth(@PathVariable("catalogId") String catalogId,
                                                                          @RequestParam("type") @Validated @NotBlank String type,
                                                                          @RequestParam(value = "keyWord", required = false) String keyWord) {
        try {
            List<RawVo.ListResponse> t = rawService.getAuthRawListByUnionIdAndType(catalogId, Integer.valueOf(type), keyWord);
            return ResponseVo.response(MessageCode.SUCCESS, t.isEmpty() ? new ArrayList<>() : t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 获取数据浏览器台账可选字段列表
     *
     * @param rawId 台账ID
     * @return T
     */
    @GetMapping("/auth/rawTable/{rawId}")
    public ResponseEntity<ResponseVo.ResponseBean> authRawTableAndAttrList(@PathVariable String rawId) {
        Raw raw = rawService.getById(rawId);
        if (null == raw) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, rawService.getTableWithAttrTree(raw));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 台账搜索列表
     *
     * @param query q
     * @return T
     */
    @PostMapping("/search/list")
    public ResponseEntity<ResponseVo.ResponseBean> searchList(@RequestBody @Validated RawVo.SearchParam query) {
        if (query == null){
            return ResponseVo.response(MessageCode.PARAMETER_ERROR);
        }
        if (StringUtils.isNotBlank(query.getKeyWord())) {
            if (SqlBuilderUtil.sqlValidate(query.getKeyWord()) || query.getKeyWord().contains("'")) {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.FILTER_PARAM_NOT_ALLOWED);
            }
        }
        try {
            List<RawVo.SearchResponse> rawList = rawService.getSearchRawList(query);
            return ResponseVo.response(MessageCode.SUCCESS, rawList);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 刷新台账表数据
     *
     * @param rawTableId 台账表ID
     * @return T
     */
    @GetMapping("/refresh/attrs/{rawTableId}")
    public ResponseEntity<ResponseVo.ResponseBean> refreshRawTableAttrs(@PathVariable("rawTableId") String rawTableId) {
        try {
            RawTable rawTable = rawTableService.getById(rawTableId);
            if (null == rawTable) {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_TABLE_DETAIL_NOT_EXIST);
            }

            List<RawTableAttrVo.InsertParam> t = rawService.refreshRawTableAttrs(rawTable);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 根据用户ID获取台账列表
     *
     * @param keyWord 模糊查询
     * @return T
     */
    @ApiOperation("根据用户ID获取台账列表")
    @GetMapping("/user/list")
    public ResponseEntity<ResponseVo.ResponseBean> getListByUser(@RequestParam(value = "keyWord", required = false) String keyWord) {
        try {
            List<Raw> t = rawService.getRawListByUserId(CurrentUserUtil.get().getId(), keyWord);
            return ResponseVo.response(MessageCode.SUCCESS, t.isEmpty() ? new ArrayList<>() : t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 开启/关闭权限校验
     *
     * @param id     id
     * @param ifAuth ifAuth
     * @return T
     */
    @PatchMapping("/ifAuth/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> publishOrDownDws(@PathVariable("id") String id, @RequestParam("ifAuth") String ifAuth) {
        try {
            if (rawService.ifAuthedRaw(id, Boolean.valueOf(ifAuth))) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }
}
