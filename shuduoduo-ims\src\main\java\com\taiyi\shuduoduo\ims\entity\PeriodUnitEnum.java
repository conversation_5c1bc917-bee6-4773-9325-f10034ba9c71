package com.taiyi.shuduoduo.ims.entity;

/**
 * 统计周期枚举类
 *
 * <AUTHOR>
 */

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.time.DayOfWeek;

public enum PeriodUnitEnum {
    DAILY("每日") {
        @Override
        public LocalDate getStartDate(LocalDate referenceDate) {
            return referenceDate;
        }

        @Override
        public LocalDate getEndDate(LocalDate referenceDate) {
            return referenceDate;
        }
    },
    WEEKLY("每周") {
        @Override
        public LocalDate getStartDate(LocalDate referenceDate) {
            return referenceDate.with(DayOfWeek.MONDAY);
        }

        @Override
        public LocalDate getEndDate(LocalDate referenceDate) {
            return referenceDate.with(DayOfWeek.SUNDAY);
        }
    },
    MONTHLY("每月") {
        @Override
        public LocalDate getStartDate(LocalDate referenceDate) {
            return referenceDate.with(TemporalAdjusters.firstDayOfMonth());
        }

        @Override
        public LocalDate getEndDate(LocalDate referenceDate) {
            return referenceDate.with(TemporalAdjusters.lastDayOfMonth());
        }
    },
    QUARTERLY("每季度") {
        @Override
        public LocalDate getStartDate(LocalDate referenceDate) {
            int currentMonth = referenceDate.getMonthValue();
            int startMonth = ((currentMonth - 1) / 3) * 3 + 1;
            return LocalDate.of(referenceDate.getYear(), startMonth, 1);
        }

        @Override
        public LocalDate getEndDate(LocalDate referenceDate) {
            int currentMonth = referenceDate.getMonthValue();
            int endMonth = ((currentMonth - 1) / 3 + 1) * 3;
            return LocalDate.of(referenceDate.getYear(), endMonth,
                    LocalDate.of(referenceDate.getYear(), endMonth, 1)
                            .with(TemporalAdjusters.lastDayOfMonth()).getDayOfMonth());
        }
    },
    YEARLY("每年") {
        @Override
        public LocalDate getStartDate(LocalDate referenceDate) {
            return referenceDate.with(TemporalAdjusters.firstDayOfYear());
        }

        @Override
        public LocalDate getEndDate(LocalDate referenceDate) {
            return referenceDate.with(TemporalAdjusters.lastDayOfYear());
        }
    };

    private final String description;

    PeriodUnitEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static PeriodUnitEnum fromString(String period) {
        try {
            return PeriodUnitEnum.valueOf(period.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Unknown period: " + period);
        }
    }

    public abstract LocalDate getStartDate(LocalDate referenceDate);

    public abstract LocalDate getEndDate(LocalDate referenceDate);

    @Override
    public String toString() {
        return description;
    }
}

