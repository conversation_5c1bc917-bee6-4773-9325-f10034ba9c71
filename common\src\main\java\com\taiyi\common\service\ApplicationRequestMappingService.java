package com.taiyi.common.service;

import com.taiyi.common.entity.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 项目下 request mapping 扫描
 *
 * <AUTHOR>
 */
public class ApplicationRequestMappingService {

    @Autowired
    private ConfigurableApplicationContext context;

    public List<RequestMapping> getRequestMapping() {
        Environment env = context.getEnvironment();
        String contextPath = env.getProperty("server.servlet.context-path");

        RequestMappingHandlerMapping mapping = context.getBean(RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = mapping.getHandlerMethods();

        List<RequestMapping> list = new ArrayList<>();
        handlerMethods.forEach((info, handlerMethod) -> {
            Set<RequestMethod> requestMethods = info.getMethodsCondition().getMethods();
            Set<String> requestPatterns = info.getPatternsCondition().getPatterns();
            String className = handlerMethod.getBeanType().getName();
            String methodName = handlerMethod.getMethod().getName();
            list.add(new RequestMapping(className, methodName, requestMethods,
                    requestPatterns.stream().map(s -> contextPath + s).collect(Collectors.toSet())));
        });
        return list; 
    }

}
