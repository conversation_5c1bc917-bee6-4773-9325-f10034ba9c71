package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.entity.DbType;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.StringUtil;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import com.taiyi.shuduoduo.common.connector.api.service.DbRpcService;
import com.taiyi.shuduoduo.ims.dao.DwsDao;
import com.taiyi.shuduoduo.ims.entity.*;
import com.taiyi.shuduoduo.ims.util.DateUtil;
import com.taiyi.shuduoduo.ims.util.SqlBuilderUtil;
import com.taiyi.shuduoduo.ims.vo.*;
import com.taiyi.shuduoduo.nocodb.api.dto.SqliteMasterDTO;
import com.taiyi.shuduoduo.nocodb.api.servie.NcProjectRpcService;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyRpcService;
import com.taiyi.shuduoduo.ums.api.service.RoleRpcService;
import com.taiyi.shuduoduo.ums.api.service.UserRpcService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 指标业务层
 *
 * <AUTHOR>
 */
@Service
public class DwsService extends CommonMysqlService<DwsDao, Dws> {
    @Override
    public Class<Dws> getEntityClass() {
        return Dws.class;
    }

    public static final String CURRENT_VALUE = "current_value";
    public static final String YOY_VALUE = "yoy_value";
    public static final String MOM_VALUE = "mom_value";

    public static final String BEFORE_2_VALUE = "before_2_value";

    public static final String BEFORE_3_VALUE = "before_3_value";

    public static final String BEFORE_4_VALUE = "before_4_value";

    public static final String BEFORE_5_VALUE = "before_5_value";


    @Autowired
    private DimensionAuthService dimensionAuthService;

    @Autowired
    private DimensionAuthForeignService dimensionAuthForeignService;

    @Autowired
    private NcProjectRpcService ncProjectRpcService;

    @Autowired
    private RoleRpcService roleRpcService;


    @Autowired
    private CompanyRpcService companyRpcService;

    @Autowired
    private AuthTableService authTableService;

    @Autowired
    private DwsDao dwsDao;

    @Autowired
    private PlateService plateService;

    @Autowired
    private DataFieldService dataFieldService;

    @Autowired
    private BusinessProcessService businessProcessService;

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private DimAttributeService dimAttributeService;

    @Autowired
    private PlateControlDeptService plateControlDeptService;

    @Autowired
    private PlateSourceService plateSourceService;

    @Autowired
    private DimDwsService dimDwsService;

    @Autowired
    private LabelService labelService;

    @Autowired
    private LabelUnionService labelUnionService;

    @Autowired
    private DwsOperationLogService dwsOperationLogService;

    @Autowired
    private DwsDataAuthService dwsDataAuthService;

    @Autowired
    private RawTableService rawTableService;

    @Autowired
    private UserRpcService userRpcService;

    @Autowired
    private DbRpcService dbRpcService;

    @Autowired
    private FactDimensionService factDimensionService;

    @Autowired
    private PlateLayerService plateLayerService;

    /**
     * 新增编辑
     *
     * @param entity
     * @param t
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(Dws entity, DwsVo.InsertParam t) {
        if (StringUtils.isNotBlank(t.getId())) {
            // 删除标签
            //labelUnionService.deleteByDwsId(t.getId(), getEntityClass());

            if (entity.getIfPublished()) {
                entity.setVersionNum(entity.getVersionNum() + 1);
            }
        } else {
            entity.setId(null);
            entity.setSno(IdUtil.simpleUUID());
            entity.setOrderBy(this.getMaxOrder());
            entity.setCreatedBy(CurrentUserUtil.get().getId());
            entity.setVersionNum(1L);
        }
        entity.setUpdatedBy(CurrentUserUtil.get().getId());
        boolean f = StringUtils.isBlank(t.getId()) ? super.save(entity) : super.updateById(t.getId(), entity);
        /*if (f) {
            // 异步操作
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    if (null != t.getLabelList()) {
                        // 处理标签
                        labelUnionService.saveBatchByDwsId(entity.getId(), t.getLabelList(), getEntityClass());
                    }
                    if (null != t.getUnionList()) {
                        // 处理关联关系
                        dimDwsService.saveBatchByDwsIdWithUnionList(entity.getId(), t.getUnionList());
                        // 更新维度表指标字段
                        dimAttributeService.updateDwsByTableIdAndAttrId(entity.getId(), t.getUnionList());
                        // 通知维度表关联的台账--有事实表字段关联指标
                        dimensionService.callRawUpdate(companyId, entity.getId(), null, t.getUnionList());
                    }
                }
            });
        }*/
        if (f) {
            return dwsOperationLogService.saveLog(entity.getId(), StringUtils.isNotBlank(t.getId()) ? OperationType.EDIT.getValue() : OperationType.NEW.getValue(), CurrentUserUtil.get().getId());
        }
        return false;
    }

    public boolean isExist(Dws dws) {
        QueryWrapper<Dws> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(dws.getId())) {
            queryWrapper.ne("id", dws.getId());
        }
        queryWrapper.eq("if_deleted", 0);
        if (StringUtils.isNotBlank(dws.getDataFieldId())) {
            queryWrapper.eq("data_field_id", dws.getDataFieldId());
        }
        queryWrapper.eq("plate_id", dws.getPlateId());
        queryWrapper.eq("name", dws.getName());
        queryWrapper.last("LIMIT 1");
        Dws one = super.getOne(queryWrapper);
        return one != null;
    }

    /**
     * 指标导入
     *
     * @param workbook  Excel
     * @param companyId 公司ID
     * @return T
     */
    public Object importDws(Workbook workbook, String companyId) {
        //获取Excel第一个sheet页
        DataFormatter formatter = new DataFormatter();
        List<Dws> dwsList = new ArrayList<>();
        //定义错误信息记录集合
        List<String> list = new ArrayList<>();
        // 多页签循环
        /* for (int numOfSheet = 0; numOfSheet < workbook.getNumberOfSheets(); numOfSheet++) {


            break;
        }*/
        // 暂不循环第二个页签
        Sheet sheets = workbook.getSheetAt(0);
        Long maxOrder = this.getMaxOrder();
        //遍历行row
        for (int rowNum = 2; rowNum <= sheets.getLastRowNum(); rowNum++) {
            // 定义一个指标名称列表集合判断重复
            List<Dws> oldDwsList;
            Row row = sheets.getRow(rowNum);
            Dws dws = new Dws();
            dws.setCompanyId(companyId);
            dws.setSno(IdUtil.simpleUUID());
            dws.setOrderBy(maxOrder);
            dws.setCreatedBy(CurrentUserUtil.get().getRealName());
            dws.setUpdatedBy(CurrentUserUtil.get().getRealName());
            dws.setVersionNum(1L);

            Plate plate;
            DataField dataField = null;
            BusinessProcess businessProcess = null;
            //将每一行数据的每一单元格数据取出
            // 板块
            String plateName = formatter.formatCellValue(row.getCell(0));
            if (StringUtils.isEmpty(plateName)) {
                list.add("第" + (rowNum + 1) + "行,业务板块不能为空");
                continue;
            } else {
                plate = plateService.getByNameAndCompanyId(plateName, companyId);
                if (null == plate) {
                    list.add("第" + (rowNum + 1) + "行,业务板块不存在");
                    continue;
                } else {
                    dws.setPlateId(plate.getId());
                    oldDwsList = getList(plate.getId(), 1, null);
                }
            }

            // 数据域
            String dataFieldName = formatter.formatCellValue(row.getCell(1));
            if (StringUtils.isNotEmpty(dataFieldName)) {
                dataField = dataFieldService.getByNameAndPlateId(dataFieldName, plate.getId());
                if (null == dataField) {
                    list.add("第" + (rowNum + 1) + "行,数据域不存在");
                    continue;
                } else {
                    dws.setDataFieldId(dataField.getId());
                    oldDwsList = getList(dataField.getId(), 3, null);
                }
            }

            // 业务过程
            String busProcessName = formatter.formatCellValue(row.getCell(2));
            if (StringUtils.isNotEmpty(busProcessName) && null != dataField) {
                businessProcess = businessProcessService.getByNameAndDataFieldId(busProcessName, dataField.getId());
                if (null == businessProcess) {
                    list.add("第" + (rowNum + 1) + "行,业务过程不存在");
                    continue;
                } else {
                    dws.setBusProcessId(businessProcess.getId());
                }
            }

            // 指标类型
            String type = formatter.formatCellValue(row.getCell(3));
            if (StringUtils.isEmpty(type)) {
                list.add("第" + (rowNum + 1) + "行,指标类型不能为空");
            } else {
                if ("原子指标".equals(type)) {
                    dws.setType(1);
                } else if ("衍生指标".equals(type)) {
                    dws.setType(2);
                } else if ("复合指标".equals(type)) {
                    dws.setType(3);
                } else {
                    list.add("第" + (rowNum + 1) + "行,指标类型错误");
                }
            }

            // 指标名称
            String name = formatter.formatCellValue(row.getCell(4));
            if (StringUtils.isEmpty(name)) {
                list.add("第" + (rowNum + 1) + "行,指标名称不能为空");
            } else {
                List<Dws> res1 = oldDwsList.stream().filter(dws1 ->
                        dws1.getPlateId().equals(plate.getId()) && dws1.getName().equals(name)).collect(Collectors.toList());

                List<Dws> res2 = dwsList.stream().filter(dws1 ->
                        dws1.getPlateId().equals(plate.getId()) && dws1.getName().equals(name)).collect(Collectors.toList());

                if (!res1.isEmpty() || !res2.isEmpty()) {
                    list.add("第" + (rowNum + 1) + "行,指标名称已存在");
                    continue;
                }
                dws.setName(name);
            }

            // 指标别名
            String alias = formatter.formatCellValue(row.getCell(5));
            dws.setAlias(alias);

            // 指标级别
            String level = formatter.formatCellValue(row.getCell(6));
            if (StringUtils.isNotBlank(level)) {
                try {
                    int levelValue = (int) row.getCell(37).getNumericCellValue();
                    dws.setLevel(levelValue + "");
                } catch (Exception e) {
                    list.add("第" + (rowNum + 1) + "行,[" + level + "]数据格式错误");
                    break;
                }
            }

            // 业务含义
            String desc = formatter.formatCellValue(row.getCell(7));
            dws.setDescription(desc);

            // 英文名称
            String enName = formatter.formatCellValue(row.getCell(8));
            dws.setEnName(enName);

            // 英文简称
            String enAbbr = formatter.formatCellValue(row.getCell(9));
            dws.setEnAbbr(enAbbr);

            // 指标编码
            String code = formatter.formatCellValue(row.getCell(10));
            dws.setCode(code);

            // 业务负责部门
            String deptNames = formatter.formatCellValue(row.getCell(11));
            if (StringUtils.isNotEmpty(deptNames)) {
                String[] names = deptNames.split(StrUtil.COMMA);
                List<String> values = new ArrayList<>();
                for (String s : names) {
                    PlateControlDept plateControlDept = plateControlDeptService.getByPlateIdAndName(plate.getId(), s);
                    if (null == plateControlDept) {
                        list.add("第" + (rowNum + 1) + "行,[" + plate.getName() + "]下[" + s + "]业务负责部门不存在");
                        break;
                    } else {
                        values.add(plateControlDept.getId());
                    }
                }
                dws.setPlateDeptIds(StringUtil.listToString(values, StrUtil.COMMA));
            }

            // 业务负责人
            String busLeaders = formatter.formatCellValue(row.getCell(12));
            if (StringUtils.isNotEmpty(busLeaders)) {
                UserDTO userDTO = userRpcService.getUserByThirdId(companyId, busLeaders);
                if (null == userDTO) {
                    list.add("第" + (rowNum + 1) + "行,[" + busLeaders + "]业务负责人不存在");
                    break;
                }
                dws.setBusinessLeaders(userDTO.getId());
            }

            // 技术负责部门
            String techNames = formatter.formatCellValue(row.getCell(13));
            if (StringUtils.isNotEmpty(techNames)) {
                String[] names = techNames.split(StrUtil.COMMA);
                List<String> values = new ArrayList<>();
                for (String s : names) {
                    PlateControlDept plateControlDept = plateControlDeptService.getByPlateIdAndName(plate.getId(), s);
                    if (null == plateControlDept) {
                        list.add("第" + (rowNum + 1) + "行,[" + plate.getName() + "]下[" + s + "]技术负责部门不存在");
                        break;
                    } else {
                        values.add(plateControlDept.getId());
                    }
                }
                dws.setTechnicalDepts(StringUtil.listToString(values, StrUtil.COMMA));
            }

            // 技术负责人
            String techLeaders = formatter.formatCellValue(row.getCell(14));
            if (StringUtils.isNotEmpty(techLeaders)) {
                UserDTO userDTO = userRpcService.getUserByThirdId(companyId, techLeaders);
                if (null == userDTO) {
                    list.add("第" + (rowNum + 1) + "行,[" + techLeaders + "]技术负责人不存在");
                    break;
                }
                dws.setTechnicalLeaders(userDTO.getId());
            }

            // 参考/引用
            String referStandard = formatter.formatCellValue(row.getCell(15));
            if (StringUtils.isNotBlank(referStandard)) {
                try {
                    int referStandardValue = (int) row.getCell(38).getNumericCellValue();
                    dws.setReferStandard(referStandardValue + "");
                } catch (Exception e) {
                    list.add("第" + (rowNum + 1) + "行,[" + referStandard + "]数据格式错误");
                    break;
                }

            }

            // 参考/引用名称
            String referStandardName = formatter.formatCellValue(row.getCell(16));
            dws.setReferStandardName(referStandardName);

            // 数据来源系统
            String sourceNames = formatter.formatCellValue(row.getCell(17));
            if (StringUtils.isNotEmpty(sourceNames)) {
                String[] names = sourceNames.split(StrUtil.COMMA);
                List<String> values = new ArrayList<>();
                for (String s : names) {
                    PlateSource source = plateSourceService.getByPlateIdAndName(plate.getId(), s);
                    if (null == source) {
                        list.add("第" + (rowNum + 1) + "行,[" + plate.getName() + "]下[" + s + "]来源系统不存在");
                        break;
                    } else {
                        values.add(source.getId());
                    }
                }
                dws.setPlateSourceIds(StringUtil.listToString(values, StrUtil.COMMA));
            }

            // 数据来源系统页面
            String sourcePage = formatter.formatCellValue(row.getCell(18));
            dws.setDataSourcePage(sourcePage);

            // 可用周期
            String periodValues = formatter.formatCellValue(row.getCell(19));
            if (StringUtils.isNotEmpty(periodValues)) {
                String[] names = periodValues.split(StrUtil.COMMA);
                List<String> values = new ArrayList<>();
                for (String s : names) {
                    Dimension dimension = dimensionService.getByPlateIdAndName(plate.getId(), s);
                    if (null == dimension) {
                        list.add("第" + (rowNum + 1) + "行,[" + plate.getName() + "]下[" + s + "]可用周期不存在");
                        break;
                    } else {
                        values.add(dimension.getId());
                    }
                }
                dws.setPeriodValue(StringUtil.listToString(values, StrUtil.COMMA));
            }

            // 可用维度
            String dimNames = formatter.formatCellValue(row.getCell(20));
            if (StringUtils.isNotEmpty(dimNames)) {
                String[] names = dimNames.split(StrUtil.COMMA);
                List<String> values = new ArrayList<>();
                for (String s : names) {
                    Dimension dimension = dimensionService.getByPlateIdAndName(plate.getId(), s);
                    if (null == dimension) {
                        list.add("第" + (rowNum + 1) + "行,[" + plate.getName() + "]下[" + s + "]维度不存在");
                        break;
                    } else {
                        values.add(dimension.getId());
                    }
                }
                dws.setDimIds(StringUtil.listToString(values, StrUtil.COMMA));
            }

            // 可用分析
            String analysis = formatter.formatCellValue(row.getCell(21));
            if (StringUtils.isNotBlank(analysis)) {
                try {
                    int analysisValue = (int) row.getCell(39).getNumericCellValue();
                    dws.setUsableAnalysis(analysisValue + "");
                } catch (Exception e) {
                    list.add("第" + (rowNum + 1) + "行,[" + analysis + "]数据格式错误");
                    break;
                }
            }

            // 业务规则
            String role = formatter.formatCellValue(row.getCell(22));
            dws.setBusinessRole(role);

            // 计量单位
            String unit = formatter.formatCellValue(row.getCell(23));
            dws.setDataUnit(unit);

            // 数据格式
            String dataType = formatter.formatCellValue(row.getCell(24));
            // 数据长度
            String dataLen = formatter.formatCellValue(row.getCell(25));
            // 数据精度
            String accuracy = formatter.formatCellValue(row.getCell(26));
            if (StringUtils.isNotEmpty(dataType) && StringUtils.isNotEmpty(dataLen) && StringUtils.isNotEmpty(accuracy)) {
                Map<String, Object> dataFormat = new HashMap<>();
                dataFormat.put("type", dataType);
                dataFormat.put("len", dataLen);
                dataFormat.put("accuracy", accuracy);
                dws.setDataFormat(JSONObject.toJSONString(dataFormat));
            }

            // 技术口径
            String techCaliber = formatter.formatCellValue(row.getCell(27));
            dws.setTechnicalCaliber(techCaliber);

            // 上级指标
            String dwsNames = formatter.formatCellValue(row.getCell(28));
            if (type != null && !type.equals("1")) {
                if (StringUtils.isNotEmpty(dwsNames)) {
                    if ("原子指标".equals(type)) {
                        list.add("第" + (rowNum + 1) + "行,指标类型为原子指标,不得添加其他指标");
                    }
                    String[] names = dwsNames.split(StrUtil.COMMA);
                    List<String> values = new ArrayList<>();
                    for (String s : names) {
                        Dws dws1 = this.getByPlateIdAndName(plate.getId(), s);
                        if (null == dws1) {
                            list.add("第" + (rowNum + 1) + "行,[" + plate.getName() + "]下[" + s + "]原子指标不存在");
                            break;
                        } else {
                            values.add(dws1.getId());
                        }
                    }
                    dws.setBasicDws(StringUtil.listToString(values, StrUtil.COMMA));
                }
            }

            // 数据来源表名称
            String dataSourceTable = formatter.formatCellValue(row.getCell(29));
            dws.setDataSourceTable(dataSourceTable);

            // 数据来源表字段名称
            String dataSourceField = formatter.formatCellValue(row.getCell(30));
            dws.setDataSourceTableField(dataSourceField);

            // 相关调度
            String relDispatch = formatter.formatCellValue(row.getCell(31));
            dws.setRelatedDispatch(relDispatch);

            // 统计频度
            String statistics = formatter.formatCellValue(row.getCell(32));
            if (StringUtils.isNotBlank(statistics)) {
                try {
                    int statisticsValue = (int) row.getCell(43).getNumericCellValue();
                    dws.setStatistics(statisticsValue + "");
                } catch (Exception ignored) {
                    list.add("第" + (rowNum + 1) + "行,[" + statistics + "]数据格式错误");
                    break;
                }
            }

            // ETL加载说明
            String etl = formatter.formatCellValue(row.getCell(33));
            dws.setEtlDesc(etl);

            // 业务流向图在线文档地址
            String onlineUri = formatter.formatCellValue(row.getCell(34));
            dws.setBusinessOnlineUri(onlineUri);

            // 应用系统
            String system = formatter.formatCellValue(row.getCell(35));
            dws.setApplicationSystem(system);

            // 锚定值
            String anchorValue = formatter.formatCellValue(row.getCell(36));
            dws.setAnchorValue(anchorValue);
            dwsList.add(dws);

            // 排序加1
            maxOrder = maxOrder + 1;

        }
        Map<String, Object> map = new HashMap<>(2);
        if (CollectionUtils.isNotEmpty(list)) {
            map.put("fieldTotal", list.size());
            map.put("detail", list);
            return map;
        }
        super.saveBatch(dwsList);
        return map;
    }

    private Dws getByPlateIdAndName(String plateId, String name) {
        QueryWrapper<Dws> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("if_deleted", false)
                .eq("plate_id", plateId)
                .eq("name", name)
                .last("LIMIT 1");
        return super.getOne(queryWrapper);
    }

    /**
     * 指标删除
     *
     * @param dwsId 指标ID
     * @return bool
     */
    @Override
    public boolean logicDeleteById(String dwsId) {
        List<DimDws> dimDwsList = dimDwsService.getListByDwsId(dwsId);
        String companyId = CurrentUserUtil.get().getCompanyId();
        for (DimDws dimDws : dimDwsList) {
            // 修改台账表字段指标信息
            rawTableService.updateDwsId(companyId, dimDws.getDimId(), dimDws.getDwsId());
        }
        // 删除标签关联
        labelUnionService.deleteByUnionId(dwsId, 2);
        // 删除关联维度表
        return dimDwsService.deletedByDwsId(companyId, dwsId) && super.logicDeleteById(dwsId);
    }

    /**
     * 列表查询
     *
     * @param keyWord 关键字搜索
     * @return list
     */
    public List<Dws> getList(String unionId, @NotNull Integer type, String keyWord) {
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false);
        if (type == 1) {
            wrapper.eq("plate_id", unionId);
        } else if (type == 2) {
            wrapper.eq("dim_folder_id", unionId);
        } else if (type == 3) {
            wrapper.eq("data_field_id", unionId);
        } else if (type == 4) {
            wrapper.eq("bus_process_id", unionId);
        }
        if (StringUtils.isNotBlank(keyWord)) {
            wrapper.like("name", keyWord);
        }
        wrapper.orderByAsc("order_by");
        return super.list(wrapper);
    }

    /**
     * 列表查询
     *
     * @param keyWord 关键字搜索
     * @return list
     */
    public List<Dws> getListByName(String keyWord) {
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false);
        if (StringUtils.isNotBlank(keyWord)) {
            wrapper.like("name", keyWord);
        }
        wrapper.orderByAsc("order_by");
        return super.list(wrapper);
    }

    /**
     * 获取最大排序
     *
     * @return long
     */
    public Long getMaxOrder() {
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).orderByDesc("order_by").last("LIMIT 1");
        Dws dws = super.getOne(wrapper);
        return null == dws ? 1L : dws.getOrderBy() + 1;
    }

    /**
     * 根据目录获取指标总数
     *
     * @param requiredId 目录ID
     * @param type       目录类型 1.板块  2.数据域  3. 业务过程
     * @return int
     */
    public Integer getCountByRequiredId(String requiredId, Integer type) {
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false);
        if (type == 1) {
            wrapper.eq("plate_id", requiredId);
        } else if (type == 2) {
            wrapper.eq("data_field_id", requiredId);
        } else {
            wrapper.eq("bus_process_id", requiredId);
        }
        return super.count(wrapper);
    }

    /**
     * 指标详情
     *
     * @param id 指标ID
     * @return T
     */
    public DwsVo.DetailVo getDetailById(String id) {
        Dws dws = getById(id);
        DwsVo.DetailVo res = BeanUtil.copy(dws, DwsVo.DetailVo.class);
        if (StringUtils.isNotBlank(res.getCreatedBy())) {
            UserDTO userDTO = userRpcService.getUserInfoById(res.getCreatedBy());
            res.setCreateUserName(null == userDTO ? "" : userDTO.getRealName());
        }
        if (StringUtils.isNotBlank(res.getUpdatedBy())) {
            UserDTO userDTO1 = userRpcService.getUserInfoById(res.getUpdatedBy());
            res.setUpdateUserName(null == userDTO1 ? "" : userDTO1.getRealName());
        }
//        List<DimDws> dimDwsList = dimDwsService.getListByDwsId(id);
        /*List<DwsVo.DimensionUnion> unionList = new ArrayList<>();
        for (DimDws dimDws : dimDwsList) {
            DwsVo.DimensionUnion dimensionUnion = new DwsVo.DimensionUnion();
            dimensionUnion.setAttrId(dimDws.getDimAttrId());
            dimensionUnion.setTableId(dimAttributeService.getById(dimDws.getDimAttrId()).getDimId());
            unionList.add(dimensionUnion);
        }
        res.setUnionList(unionList);
        // 查询标签
        List<LabelUnion> labelUnionList = labelUnionService.getListByRequiredId(id, getEntityClass());
        List<LabelVo> labelVoList = new ArrayList<>();
        for (LabelUnion labelUnion : labelUnionList) {
            LabelVo labelVo = new LabelVo();
            labelVo.setId(labelUnion.getLabelId());
            labelVoList.add(labelVo);
        }
        res.setLabelList(labelVoList);*/
        // 认证处理 (指标定义认证即为已认证)
        DwsAuthVo.Detail basicAuth = dwsDataAuthService.getByDwsId(1, id);
        DwsAuthVo.Detail dataAuth = dwsDataAuthService.getByDwsId(2, id);
        res.setIfCertified(null != dataAuth ? dataAuth.getStatus().toString() : null);
        res.setAuthStatus(null != basicAuth ? basicAuth.getStatus().toString() : null);
        return res;
    }

    /**
     * 指标定义分页查询
     *
     * @param pageVo vo
     * @return T
     */
    public PageResult<DwsVo.PageResponseVo> getPage(DwsVo.DwsPageVo pageVo) {
        Page<Dws> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        page.setOptimizeCountSql(false);
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId());
        wrapper.eq("if_deleted", false);
        if (StringUtils.isBlank(pageVo.getPlateId()) && StringUtils.isBlank(pageVo.getDataFieldId()) && StringUtils.isBlank(pageVo.getBusProcessId())) {
            //添加板块控制
            List<Plate> plateList = plateService.getListByCompanyId(CurrentUserUtil.get().getCompanyId());
            if (ObjectUtil.isEmpty(plateList)) {
                return new PageResult<>();
            }
            List<String> plateIds = new ArrayList<>();
            plateList.forEach(plate -> plateIds.add(plate.getId()));
            wrapper.in("plate_id", plateIds);
        }
        if (StringUtils.isNotBlank(pageVo.getPlateId())) {
            wrapper.eq("plate_id", pageVo.getPlateId());
        }
        if (StringUtils.isNotBlank(pageVo.getDataFieldId())) {
            wrapper.eq("data_field_id", pageVo.getDataFieldId());
        }
        if (StringUtils.isNotBlank(pageVo.getBusProcessId())) {
            wrapper.eq("bus_process_id", pageVo.getBusProcessId());
        }
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.and(queryWrapper -> queryWrapper.like("name", pageVo.getKeyWord())
                    .or()
                    .like("code", pageVo.getKeyWord())
                    .or()
                    .like("description", pageVo.getKeyWord()));
        }
        if (StringUtils.isNotBlank(pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getSortType())) {
            wrapper.last("order by " + StrUtil.toSymbolCase(pageVo.getColumn(), '_') + " " + pageVo.getSortType());
        } else {
            wrapper.orderByAsc("order_by");
        }
        IPage<Dws> iPage = super.page(page, wrapper);
        List<DwsVo.PageResponseVo> res = BeanUtil.copyList(iPage.getRecords(), DwsVo.PageResponseVo.class);
        for (DwsVo.PageResponseVo vo : res) {
            if (StringUtils.isNotBlank(vo.getCreatedBy())) {
                UserDTO userDTO = userRpcService.getUserInfoById(vo.getCreatedBy());
                vo.setCreateUserName(null == userDTO ? "" : userDTO.getRealName());
            }
            if (StringUtils.isNotBlank(vo.getUpdatedBy())) {
                UserDTO userDTO1 = userRpcService.getUserInfoById(vo.getUpdatedBy());
                vo.setUpdateUserName(null == userDTO1 ? "" : userDTO1.getRealName());
            }
            // 认证处理 (指标定义认证即为已认证)
            DwsAuthVo.Detail basicAuth = dwsDataAuthService.getByDwsId(1, vo.getId());
            DwsAuthVo.Detail dataAuth = dwsDataAuthService.getByDwsId(2, vo.getId());
            vo.setIfCertified(null != dataAuth ? dataAuth.getStatus().toString() : null);
            vo.setAuthStatus(null != basicAuth ? basicAuth.getStatus().toString() : null);
        }
        PageResult<DwsVo.PageResponseVo> result = new PageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setList(res);
        result.setTotal(iPage.getTotal());
        return result;
    }

    /**
     * 数据挂接分页
     *
     * @param pageVo vo
     * @return T
     */
    public PageResult<DwsVo.PageResponseVo> getDockPage(DwsVo.DwsPageVo pageVo) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Page<Dws> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        page.setOptimizeCountSql(false);
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId);
        wrapper.eq("if_deleted", false);
        if (pageVo.getBeginTime() != null && pageVo.getEndTime() != null) {
            wrapper.between("update_time", pageVo.getBeginTime(), pageVo.getEndTime());
        }
        if (StringUtils.isBlank(pageVo.getPlateId()) && StringUtils.isBlank(pageVo.getDataFieldId()) && StringUtils.isBlank(pageVo.getBusProcessId())) {
            //添加板块控制
            List<Plate> plateList = plateService.getListByCompanyId(companyId);
            if (ObjectUtil.isEmpty(plateList)) {
                return new PageResult<>();
            }
            List<String> plateIds = new ArrayList<>();
            plateList.forEach(plate -> plateIds.add(plate.getId()));
            wrapper.in("plate_id", plateIds);
        }
        if (StringUtils.isNotBlank(pageVo.getPlateId())) {
            wrapper.eq("plate_id", pageVo.getPlateId());
        }
        if (StringUtils.isNotBlank(pageVo.getDataFieldId())) {
            wrapper.eq("data_field_id", pageVo.getDataFieldId());
        }
        if (StringUtils.isNotBlank(pageVo.getBusProcessId())) {
            wrapper.eq("bus_process_id", pageVo.getBusProcessId());
        }
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.and(queryWrapper -> queryWrapper.like("name", pageVo.getKeyWord())
                    .or()
                    .like("code", pageVo.getKeyWord())
                    .or()
                    .like("description", pageVo.getKeyWord()));
        }
        if (StringUtils.isNotBlank(pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getSortType())) {
            wrapper.last("order by " + StrUtil.toSymbolCase(pageVo.getColumn(), '_') + " " + pageVo.getSortType());
        } else {
            wrapper.orderByAsc("order_by");
        }
        IPage<Dws> iPage = super.page(page, wrapper);
        List<DwsVo.PageResponseVo> resList = BeanUtil.copyList(iPage.getRecords(), DwsVo.PageResponseVo.class);
        for (DwsVo.PageResponseVo vo : resList) {
            if (StringUtils.isNotBlank(vo.getCreatedBy())) {
                UserDTO userDTO = userRpcService.getUserInfoById(vo.getCreatedBy());
                vo.setCreateUserName(null == userDTO ? "" : userDTO.getRealName());
            }
            if (StringUtils.isNotBlank(vo.getUpdatedBy())) {
                UserDTO userDTO1 = userRpcService.getUserInfoById(vo.getUpdatedBy());
                vo.setUpdateUserName(null == userDTO1 ? "" : userDTO1.getRealName());
            }
            vo.setTotal(dimDwsService.getCountByDwsId(vo.getId()));
        }
        PageResult<DwsVo.PageResponseVo> result = new PageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setList(resList);
        result.setTotal(iPage.getTotal());
        return result;
    }

    /**
     * 指标认证分页
     *
     * @param pageVo vo
     * @return T
     */
    public PageResult<DwsVo.PageResponseVo> getAuthPage(DwsVo.DwsPageVo pageVo) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Page<DwsVo.PageResponseVo> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        page.setOptimizeCountSql(false);
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.eq("t.company_id", companyId)
                .eq("t.if_deleted", false);
        if (pageVo.getBeginTime() != null && pageVo.getEndTime() != null) {
            wrapper.and(wrapper1 -> wrapper1.between("t1.create_time", pageVo.getBeginTime(), pageVo.getEndTime())
                    .or()
                    .between("t2.create_time", pageVo.getBeginTime(), pageVo.getEndTime()));
        }
        if (StringUtils.isBlank(pageVo.getPlateId())) {
            //添加板块控制
            List<Plate> plateList = plateService.getListByCompanyId(companyId);
            if (ObjectUtil.isEmpty(plateList)) {
                return new PageResult<>();
            }
            List<String> plateIds = new ArrayList<>();
            plateList.forEach(plate -> plateIds.add(plate.getId()));
            wrapper.in("t.plate_id", plateIds);
        }
        if (StringUtils.isNotBlank(pageVo.getPlateId())) {
            wrapper.eq("plate_id", pageVo.getPlateId());
        }
        if (StringUtils.isNotBlank(pageVo.getDataFieldId())) {
            wrapper.eq("data_field_id", pageVo.getDataFieldId());
        }
        if (StringUtils.isNotBlank(pageVo.getBusProcessId())) {
            wrapper.eq("bus_process_id", pageVo.getBusProcessId());
        }
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.and(queryWrapper -> queryWrapper.like("t.name", pageVo.getKeyWord())
                    .or()
                    .like("t.code", pageVo.getKeyWord())
                    .or()
                    .like("t.en_name", pageVo.getKeyWord())
                    .or()
                    .like("t.description", pageVo.getKeyWord()));
        }
        wrapper.eq(StringUtils.isNotBlank(pageVo.getLevel()), "t.level", pageVo.getLevel());
        if (StringUtils.isNotBlank(pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getSortType())) {
            wrapper.last("order by " + StrUtil.toSymbolCase(pageVo.getColumn(), '_') + " " + pageVo.getSortType());
        } else {
            wrapper.orderByAsc("t.order_by");
        }
        if (null != pageVo.getAuthType()) {
            if (pageVo.getAuthType() == Certification.DOUBLE_CERTIFICATION.getValue()) {
                wrapper.and(wrapper2 -> wrapper2.isNotNull("t1.status").isNotNull("t2.status"));
            } else if (pageVo.getAuthType() == Certification.CERTIFICATION_DEFINITION.getValue()) {
                wrapper.isNotNull("t1.status");
            } else if (pageVo.getAuthType() == Certification.CERTIFICATION_DATA.getValue()) {
                wrapper.isNotNull("t2.status");
            } else {
                wrapper.and(wrapper2 -> wrapper2.isNull("t1.status").isNull("t2.status"));
            }
        }
        IPage<DwsVo.PageResponseVo> iPage = dwsDao.authPage(page, wrapper);
        for (DwsVo.PageResponseVo vo : iPage.getRecords()) {
            if (StringUtils.isNotBlank(vo.getCreatedBy())) {
                UserDTO userDTO = userRpcService.getUserInfoById(vo.getCreatedBy());
                vo.setCreateUserName(null == userDTO ? "" : userDTO.getRealName());
            }
            if (StringUtils.isNotBlank(vo.getUpdatedBy())) {
                UserDTO userDTO1 = userRpcService.getUserInfoById(vo.getUpdatedBy());
                vo.setUpdateUserName(null == userDTO1 ? "" : userDTO1.getRealName());
            }
            // 标签处理
            vo.setLabelList(getDwsListByDwsId(vo.getId()));
            // 认证处理 (指标定义认证即为已认证)
            vo.setBasicAuth(dwsDataAuthService.getByDwsId(1, vo.getId()));
            vo.setDataAuth(dwsDataAuthService.getByDwsId(2, vo.getId()));
        }
        PageResult<DwsVo.PageResponseVo> result = new PageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setList(iPage.getRecords());
        result.setTotal(iPage.getTotal());
        return result;
    }

    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 指标百科分页
     *
     * @param pageVo vo
     * @return T
     */
    public PageResult getWikiPage(DwsVo.DwsPageVo pageVo) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Page<Dws> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        page.setOptimizeCountSql(false);
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.eq("t.company_id", companyId);
        wrapper.eq("t.if_deleted", false);
        wrapper.eq(StrUtil.isNotBlank(pageVo.getId()), "t.id", pageVo.getId());
        if (StringUtils.isBlank(pageVo.getPlateId())) {
            //添加板块控制
            List<Plate> plateList = plateService.getListByCompanyId(companyId);
            if (ObjectUtil.isEmpty(plateList)) {
                return new PageResult<>();
            }
            List<String> plateIds = new ArrayList<>();
            plateList.forEach(plate -> plateIds.add(plate.getId()));
            wrapper.in("t.plate_id", plateIds);
        }
        if (StringUtils.isNotBlank(pageVo.getPlateId())) {
            wrapper.eq("t.plate_id", pageVo.getPlateId());
        }
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.and(queryWrapper -> queryWrapper.like("t.name", pageVo.getKeyWord())
                    .or()
                    .like("t.code", pageVo.getKeyWord())
                    .or()
                    .like("t.description", pageVo.getKeyWord()));
        }
        wrapper.eq(StringUtils.isNotBlank(pageVo.getLevel()), "t.level", pageVo.getLevel());
        // 认证处理
        if (pageVo.getQueryType() == 2) {
            wrapper.inSql("t.id", "select dws_id from ims_dws_basic_auth t3 where t3.status = 1");
        }
        // 中交环境处理只查询已发布的数据
        if ("zjprod".equals(active)) {
            wrapper.eq("t.if_published", true);
        }
        wrapper.groupBy("t.id");
        wrapper.orderByAsc("t.order_by");
        IPage<Dws> iPage = null;
        // 标签处理
        if (null != pageVo.getLabelIds() && !pageVo.getLabelIds().isEmpty()) {
            wrapper.in("t2.id", pageVo.getLabelIds());
            iPage = dwsDao.wikiPageByLabels(page, wrapper, pageVo.getLabelIds().size());
        } else {
            iPage = dwsDao.wikiPage(page, wrapper);
        }
        List<DwsVo.PageResponseVo> res = BeanUtil.copyList(iPage.getRecords(), DwsVo.PageResponseVo.class);
        for (DwsVo.PageResponseVo vo : res) {
            if (StringUtils.isNotBlank(vo.getCreatedBy())) {
                UserDTO userDTO = userRpcService.getUserInfoById(vo.getCreatedBy());
                vo.setCreateUserName(null == userDTO ? "" : userDTO.getRealName());
            }
            if (StringUtils.isNotBlank(vo.getUpdatedBy())) {
                UserDTO userDTO1 = userRpcService.getUserInfoById(vo.getUpdatedBy());
                vo.setUpdateUserName(null == userDTO1 ? "" : userDTO1.getRealName());
            }
            // 标签处理
            vo.setLabelList(getDwsListByDwsId(vo.getId()));
            // 认证处理 (指标定义认证即为已认证)
            DwsAuthVo.Detail basicAuth = dwsDataAuthService.getByDwsId(1, vo.getId());
            DwsAuthVo.Detail dataAuth = dwsDataAuthService.getByDwsId(2, vo.getId());
            vo.setIfCertified(null != dataAuth ? dataAuth.getStatus().toString() : null);
            vo.setAuthStatus(null != basicAuth ? basicAuth.getStatus().toString() : null);
            if (StringUtils.isNotBlank(vo.getBusinessLeaders())) {
                String busLeaderNames = "";
                String busLeaderAvatars = "";
                for (String s : vo.getBusinessLeaders().split(StrUtil.COMMA)) {
                    UserDTO userDTO = userRpcService.getUserInfoById(s);
                    busLeaderNames = String.join(StrUtil.COMMA, null == userDTO ? "" : userDTO.getRealName());
                    busLeaderAvatars = String.join(StrUtil.COMMA, null == userDTO ? "" : userDTO.getAvatarUri());
                }
                vo.setBusinessLeaderNames(busLeaderNames);
                vo.setBusinessLeaderAvatarUris(busLeaderAvatars);
            }
            if (StringUtils.isNotBlank(vo.getTechnicalLeaders())) {
                String techLeaderNames = "";
                String techLeaderAvatars = "";
                for (String s : vo.getTechnicalLeaders().split(StrUtil.COMMA)) {
                    UserDTO userDTO = userRpcService.getUserInfoById(s);
                    techLeaderNames = String.join(StrUtil.COMMA, null == userDTO ? "" : userDTO.getRealName());
                    techLeaderAvatars = String.join(StrUtil.COMMA, null == userDTO ? "" : userDTO.getAvatarUri());
                }
                vo.setTechnicalLeaderNames(techLeaderNames);
                vo.setTechnicalLeaderAvatarUris(techLeaderAvatars);
            }
        }
        PageResult result = new PageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setList(res);
        result.setTotal(iPage.getTotal());
        return result;
    }

    /**
     * 获取标签列表根据指标ID
     *
     * @param id 指标ID
     * @return 标签列表
     */
    public List<LabelVo.DwsDetail> getDwsListByDwsId(String id) {
        List<LabelVo.DwsDetail> res = new ArrayList<>();
        List<LabelUnion> unions = labelUnionService.getListByUnionId(id, 2);
        for (LabelUnion union : unions) {
            LabelVo.DwsDetail detail = new LabelVo.DwsDetail();
            Label label = labelService.getById(union.getLabelId());
            if (null != label) {
                detail.setName(label.getName());
                detail.setDwsId(id);
                detail.setLabelId(label.getId());
                detail.setType(label.getType());
                detail.setUnionId(union.getId());
                res.add(detail);
            }
        }
        return res;
    }

    /**
     * 搜索列表
     *
     * @param param 搜索参数
     * @return List
     */
    public List<DwsVo.SearchResponse> getSearchDwsList(RawVo.SearchParam param) {
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false);
        if (StringUtils.isNotBlank(param.getKeyWord())) {
            wrapper.like("name", param.getKeyWord());
        }
        return BeanUtil.copyList(super.list(wrapper), DwsVo.SearchResponse.class);
    }

    /**
     * 根据指标ID获取认证列表
     *
     * @param dwsId 指标ID
     * @return Map
     */
    public Map<String, Object> getAuthListByDwsId(String dwsId) {
        DwsAuthVo.Detail detail = dwsDataAuthService.getByDwsId(1, dwsId);
        if (null != detail) {
            detail.setType(1);
        }
        DwsAuthVo.Detail detail1 = dwsDataAuthService.getByDwsId(2, dwsId);
        if (null != detail1) {
            detail1.setType(2);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("basicAuth", detail);
        map.put("dataAuth", detail1);
        return map;
    }

    /**
     * 发布上线
     *
     * @param id        指标ID
     * @param ifPublish 发布/上线
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean publishOrDownDws(String id, Boolean ifPublish) {
        Dws dws = super.getById(id);
        if (null == dws) {
            return false;
        }
        UpdateWrapper<Dws> wrapper = new UpdateWrapper<>();
        wrapper.eq("id", id);
        wrapper.set("if_published", ifPublish);
        boolean f = super.update(wrapper);
        if (f) {
            return dwsOperationLogService.saveLog(id, ifPublish ? OperationType.PUBLISHED.getValue() : OperationType.OFFLINE.getValue(), CurrentUserUtil.get().getId());
        }
        return false;
    }

    /**
     * 获取指标操作日志
     *
     * @param dwsId 指标ID
     * @return 日志列表
     */
    public List<DwsVo.OperationResponse> getLogs(String dwsId) {
        Dws dws = super.getById(dwsId);
        List<DwsOperationLog> logList = dwsOperationLogService.getLogByDwsId(dwsId);
        List<DwsVo.OperationResponse> res = BeanUtil.copyList(logList, DwsVo.OperationResponse.class);
        for (DwsVo.OperationResponse response : res) {
            response.setId(dwsId);
            response.setName(dws.getName());
            response.setTypeName(OperationType.getDesc(response.getType()));
            if (StringUtils.isNotBlank(response.getActionUser())) {
                UserDTO userDTO = userRpcService.getUserInfoById(response.getActionUser());
                response.setUsername(null == userDTO ? "" : userDTO.getRealName());
            }
        }
        return res;
    }

    /**
     * 根据指标名称获取指标列表
     *
     * @param nameList 指标名称
     * @return 指标列表
     */
    public List<Map> queryCodeByNames(List<String> nameList) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        List<Map> res = new ArrayList<>();
        for (String s : nameList) {
            Map<String, Object> map = new HashMap<>(2);
            map.put("name", s);
            map.put("list", getInfoByName(companyId, s));
            res.add(map);
        }
        return res;
    }

    /**
     * 根据指标名称获取指标列表
     *
     * @param companyId 企业ID
     * @param name      指标名称
     * @return 指标列表
     */
    public List<Dws> getInfoByName(String companyId, String name) {
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.select("id", "plate_id", "name", "en_name").eq("company_id", companyId).eq("name", name).eq("if_deleted", false);
        return super.list(wrapper);
    }

    /**
     * 根据维度ID获取指标列表并获取对应的数据域,并根据数据域分组
     *
     * @param dimId 维度ID
     * @return List
     */
    public List<DwsVo.DwsPeriodList> getDwsListByDimId(String dimId) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        List<DimDws> dimDwsList = dimDwsService.getListByDimensionId(dimId);
        if (dimDwsList.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> dwsIdList = dimDwsList.stream().map(DimDws::getDwsId).collect(Collectors.toList());
        List<Dws> list = super.listByIds(dwsIdList);
        // TODO: 2025/2/10 feat: 修改指标列表为指标+统计周期
        List<DwsVo.DwsPeriodList> res = BeanUtil.copyList(list, DwsVo.DwsPeriodList.class);
        for (DwsVo.DwsPeriodList dws : res) {
            List<Dimension> periodList = new ArrayList<>();
            // 获取指标挂接的统计周期
            List<DimDws> dwsList1 = dimDwsService.getListByDwsIdAndDimId(dws.getId(), dimId);
            for (DimDws dimDws : dwsList1) {
                // 保留维度的挂接数据
                if (StringUtils.isNotBlank(dimDws.getPeriodValue()) && dimId.equals(dimDws.getDimension())) {
                    Dimension dimension = dimensionService.getById(dimDws.getPeriodValue());
                    FactDimension factDimension = factDimensionService.geDimPeriodByFactIdAndPeriodId(companyId, dimDws.getDimId(), dimDws.getPeriodValue());
                    if (null != factDimension) {
                        dimension.setName(factDimension.getPeriodUnit());
                        periodList.add(dimension);
                    }
                }
            }
            dws.setPeriodList(periodList);
        }
        return res;
//        return listDwsGroupByDataFieldId(dwsList);
    }

    /**
     * 根据指标列表获取对应的数据域,并根据数据域分组
     *
     * @param dwsList 指标列表
     * @return List
     */
    public List<DwsVo.DataFieldResponse> listDwsGroupByDataFieldId(List<Dws> dwsList) {
        // 1. 按数据域分组
        Map<String, List<Dws>> groupedByFieldId = dwsList.stream()
                .collect(Collectors.groupingBy(Dws::getDataFieldId));

        // 2. 将分组后的结果转换为 DataFieldResponse 对象
        List<DwsVo.DataFieldResponse> result = new ArrayList<>();
        for (Map.Entry<String, List<Dws>> entry : groupedByFieldId.entrySet()) {
            String dataFieldId = entry.getKey();
            List<Dws> dwsGroup = entry.getValue();
            DwsVo.DataFieldResponse response = new DwsVo.DataFieldResponse();
            response.setDataFieldId(dataFieldId);
            // 设置 dataFieldName
            DataField dataField = dataFieldService.getById(dataFieldId);
            response.setDataFieldName(null != dataField ? dataField.getName() : null);

            List<DwsVo.SearchResponse> searchResponses = dwsGroup.stream()
                    .map(dws -> BeanUtil.copy(dws, DwsVo.SearchResponse.class))
                    .collect(Collectors.toList());
            response.setDwsList(searchResponses);

            result.add(response);
        }
        return result;
    }

    /**
     * 构建外键关联条件
     *
     * @param foreignKeys 外键集合
     * @return 外键关联条件
     */
    public List<Map<String, Object>> buildForeignFilter(List<RawForeignKey> foreignKeys) {
        List<Map<String, Object>> joinKeys = new ArrayList<>();
        for (RawForeignKey foreignKey : foreignKeys) {
            Map<String, Object> map = new HashMap<>(2);
            map.put("main", rawTableService.getById(foreignKey.getRawTableId()).getCode() + StrUtil.DOT + foreignKey.getDimAttrCode());
            map.put("join", rawTableService.getById(foreignKey.getUnionRawTableId()).getCode() + StrUtil.DOT + foreignKey.getUnionDimAttrCode());
            joinKeys.add(map);
        }
        return joinKeys;
    }

    public String getErrorValues(List<Map<String, Object>> data) {
        for (Map<String, Object> map : data) {
            if (map.containsKey("error")) {
                return map.get("error").toString();
            }
        }
        return null;
    }

    /**
     * dremio 信息 转换为通用连接参数
     *
     * @param dremioDTO dremio 信息
     * @param sql       查询SQL
     * @return DbDto.DbQueryBySql
     */
    public DbDto.DbQueryBySql convertByDremioInfo(CompanyDremioDTO dremioDTO, Plate plate, String sql) {
        DbDto.DbQueryBySql dbQueryBySql = new DbDto.DbQueryBySql();
        dbQueryBySql.setHost(dremioDTO.getDremioHost());
        dbQueryBySql.setPort(dremioDTO.getDremioPort());
        dbQueryBySql.setUsername(dremioDTO.getDremioUsername());
        dbQueryBySql.setPassword(dremioDTO.getDremioPasswd());
        dbQueryBySql.setType(DbType.DREMIO.toString());
        dbQueryBySql.setDatabase(plate.getPlateCode());
        dbQueryBySql.setSql(sql);
        return dbQueryBySql;
    }

    /**
     * 根据事实表ID获取数据湖查询表名
     *
     * @param tableId 事实表ID
     * @return 数据湖查询表名
     */
    public String getQueryTable(String tableId) {

        // 查询所对应的维度表信息所在的分层信息
        Dimension dimension = dimensionService.getById(tableId);
        if (dimension == null) {
            return null;
        }
        // 查询维度表所在的分层信息
        PlateLayer plateLayer = plateLayerService.getById(dimension.getPlateLayerId());
        if (plateLayer == null) {
            return null;
        }
        return plateLayer.getCode() + StrUtil.DOT + dimension.getCode();
    }

    /**
     * 事实表查询 构建查询字段SQL
     *
     * @param param     查询参数
     * @param factTable 事实表
     * @param factField 事实表字段
     * @param period    统计周期字段
     * @return String
     */
    public String buildQueryFieldSql(DwsVo.QueryDataRequest param, Dimension factTable, DimAttribute factField, DimAttribute period, Integer type) {
        String queryColumnSqlTemplate = "";
        String queryColumnSql = "";

        // 当前时间sql
        String currentDateSql = "";
        // 同比时间sql
        String yoyDateSql = "";
        // 环比时间sql
        String momDateSql = "";
        // 上2时间sql
        String before2DateSql = "";
        // 上3时间sql
        String before3DateSql = "";
        // 上4时间sql
        String before4DateSql = "";
        // 上5时间sql
        String before5DateSql = "";

        // 当前日期
        LocalDate referenceDate = param.getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        // 年
        int year = referenceDate.getYear();
        // 月
        int month = referenceDate.getMonthValue();
        // 日
        int day = referenceDate.getDayOfMonth();
        // 周
        WeekFields weekFields = WeekFields.of(Locale.CHINA);
        int week = referenceDate.get(weekFields.weekOfWeekBasedYear());
        // 季
        int quarter = (month + 2) / 3;
        // 半年
        int half = month > 6 ? 1 : 2;
        // 处理时间
        if ("年".equals(param.getPeriodUnit())) {
            // 获取年份 格式为 yyyy
            currentDateSql = String.format("%d", year);
            // 获取去年
            yoyDateSql = String.format("%d", year - 1);
            // 获取环比去年
            momDateSql = yoyDateSql;
            // 获取前年
            before2DateSql = String.format("%d", year - 2);
            // 获取三年前
            before3DateSql = String.format("%d", year - 3);
            // 获取四年前
            before4DateSql = String.format("%d", year - 4);
            // 获取五年前
            before5DateSql = String.format("%d", year - 5);
        } else if ("半年".equals(param.getPeriodUnit())) {
            // 获取半年 格式为 yyyyhh
            currentDateSql = DateUtil.getPreviousHalfYear(referenceDate, 0);
            // 去年该半年
            yoyDateSql = DateUtil.getPreviousHalfYear(referenceDate, 2);
            // 上半年
            momDateSql = DateUtil.getPreviousHalfYear(referenceDate, 1);
            // 上二三四五个半年的时间
            before2DateSql = DateUtil.getPreviousHalfYear(referenceDate, 2);
            before3DateSql = DateUtil.getPreviousHalfYear(referenceDate, 3);
            before4DateSql = DateUtil.getPreviousHalfYear(referenceDate, 4);
            before5DateSql = DateUtil.getPreviousHalfYear(referenceDate, 5);
        } else if ("季".equals(param.getPeriodUnit())) {
            // 获取季 格式为 yyyyqq
            currentDateSql = DateUtil.getPreviousQuarter(referenceDate, 0);
            // 去年该季度
            yoyDateSql = DateUtil.getPreviousQuarter(referenceDate, 4);
            // 上季度
            momDateSql = DateUtil.getPreviousQuarter(referenceDate, 1);
            // 上二三四五个季度的时间
            before2DateSql = DateUtil.getPreviousQuarter(referenceDate, 2);
            before3DateSql = DateUtil.getPreviousQuarter(referenceDate, 3);
            before4DateSql = DateUtil.getPreviousQuarter(referenceDate, 4);
            before5DateSql = DateUtil.getPreviousQuarter(referenceDate, 5);
        } else if ("月".equals(param.getPeriodUnit())) {
            // 获取月份 格式为 yyyymm
            currentDateSql = String.format("%d%02d", year, month);
            yoyDateSql = String.format("%d%02d", year - 1, month);
            // 上月
            int preMonth = (month == 1) ? 12 : month - 1;
            int preMonthYear = (month == 1) ? year - 1 : year;
            momDateSql = String.format("%d%02d", preMonthYear, preMonth);
            // 上两个、三个、四个、五个月的时间
            int preMonthYear2 = (month <= 2) ? year - 1 : year;
            int preMonth2 = (month <= 2) ? (12 + month - 2) : (month - 2);
            before2DateSql = String.format("%d%02d", preMonthYear2, preMonth2);
            int preMonthYear3 = (month <= 3) ? year - 1 : year;
            int preMonth3 = (month <= 3) ? (12 + month - 3) : (month - 3);
            before3DateSql = String.format("%d%02d", preMonthYear3, preMonth3);
            int preMonthYear4 = (month <= 4) ? year - 1 : year;
            int preMonth4 = (month <= 4) ? (12 + month - 4) : (month - 4);
            before4DateSql = String.format("%d%02d", preMonthYear4, preMonth4);
            int preMonthYear5 = (month <= 5) ? year - 1 : year;
            int preMonth5 = (month <= 5) ? (12 + month - 5) : (month - 5);
            before5DateSql = String.format("%d%02d", preMonthYear5, preMonth5);
        } else if ("周".equals(param.getPeriodUnit())) {
            // 获取周 格式为 yyyyww
            currentDateSql = String.format("%d%02d", year, week);
            // 去年这周
            LocalDate lastWeek = referenceDate.minusYears(1).with(weekFields.weekOfYear(), week);
            int lastYearWeek = lastWeek.get(weekFields.weekOfWeekBasedYear());
            yoyDateSql = String.format("%d%02d", lastWeek.getYear(), lastYearWeek);
            // 上周
            LocalDate preWeek = referenceDate.minusWeeks(1);
            int preWeek_ = preWeek.get(weekFields.weekOfWeekBasedYear());
            momDateSql = String.format("%d%02d", preWeek.getYear(), preWeek_);
            // 上两个、三个、四个、五周的时间
            LocalDate preWeek2 = referenceDate.minusWeeks(2);
            int preWeek_2 = preWeek2.get(weekFields.weekOfWeekBasedYear());
            before2DateSql = String.format("%d%02d", preWeek2.getYear(), preWeek_2);
            LocalDate preWeek3 = referenceDate.minusWeeks(3);
            int preWeek_3 = preWeek3.get(weekFields.weekOfWeekBasedYear());
            before3DateSql = String.format("%d%02d", preWeek3.getYear(), preWeek_3);
            LocalDate preWeek4 = referenceDate.minusWeeks(4);
            int preWeek_4 = preWeek4.get(weekFields.weekOfWeekBasedYear());
            before4DateSql = String.format("%d%02d", preWeek4.getYear(), preWeek_4);
            LocalDate preWeek5 = referenceDate.minusWeeks(5);
            int preWeek_5 = preWeek5.get(weekFields.weekOfWeekBasedYear());
            before5DateSql = String.format("%d%02d", preWeek5.getYear(), preWeek_5);
        } else if ("日".equals(param.getPeriodUnit())) {
            // 获取日 格式为 yyyy-mm-dd
            currentDateSql = String.format("%d-%02d-%02d", year, month, day);
            // 去年今天
            LocalDate lastYearDay = referenceDate.minusYears(1);
            yoyDateSql = String.format("%d-%02d-%02d", lastYearDay.getYear(), lastYearDay.getMonthValue(), lastYearDay.getDayOfMonth());
            // 昨天
            LocalDate yesterday = referenceDate.minusDays(1);
            momDateSql = String.format("%d-%02d-%02d", yesterday.getYear(), yesterday.getMonthValue(), yesterday.getDayOfMonth());
            // 上两个、三个、四个、五天的时间
            LocalDate before2Day = referenceDate.minusDays(2);
            before2DateSql = String.format("%d-%02d-%02d", before2Day.getYear(), before2Day.getMonthValue(), before2Day.getDayOfMonth());
            LocalDate before3Day = referenceDate.minusDays(3);
            before3DateSql = String.format("%d-%02d-%02d", before3Day.getYear(), before3Day.getMonthValue(), before3Day.getDayOfMonth());
            LocalDate before4Day = referenceDate.minusDays(4);
            before4DateSql = String.format("%d-%02d-%02d", before4Day.getYear(), before4Day.getMonthValue(), before4Day.getDayOfMonth());
            LocalDate before5Day = referenceDate.minusDays(5);
            before5DateSql = String.format("%d-%02d-%02d", before5Day.getYear(), before5Day.getMonthValue(), before5Day.getDayOfMonth());
        }
        // 新指标SQL模板
        if (type == 1) {
            queryColumnSqlTemplate = "SUM(CASE WHEN {0} THEN {1} ELSE 0 END) AS " + CURRENT_VALUE + ", " +
                    "SUM(CASE WHEN {2} THEN {3} ELSE 0 END) AS " + YOY_VALUE + ", " +
                    "SUM(CASE WHEN {4} THEN {5} ELSE 0 END) AS " + MOM_VALUE + ", " +
                    "SUM(CASE WHEN {6} THEN {7} ELSE 0 END) AS " + BEFORE_2_VALUE + ", " +
                    "SUM(CASE WHEN {8} THEN {9} ELSE 0 END) AS " + BEFORE_3_VALUE + ", " +
                    "SUM(CASE WHEN {10} THEN {11} ELSE 0 END) AS " + BEFORE_4_VALUE + ", " +
                    "SUM(CASE WHEN {12} THEN {13} ELSE 0 END) AS " + BEFORE_5_VALUE;
            queryColumnSql = MessageFormat.format(queryColumnSqlTemplate,
                    factTable.getCode() + StrUtil.DOT + period.getCode() + " = '" + currentDateSql + "'",
                    factTable.getCode() + StrUtil.DOT + factField.getCode(),
                    factTable.getCode() + StrUtil.DOT + period.getCode() + " = '" + yoyDateSql + "'",
                    factTable.getCode() + StrUtil.DOT + factField.getCode(),
                    factTable.getCode() + StrUtil.DOT + period.getCode() + " = '" + momDateSql + "'",
                    factTable.getCode() + StrUtil.DOT + factField.getCode(),
                    factTable.getCode() + StrUtil.DOT + period.getCode() + " = '" + before2DateSql + "'",
                    factTable.getCode() + StrUtil.DOT + factField.getCode(),
                    factTable.getCode() + StrUtil.DOT + period.getCode() + " = '" + before3DateSql + "'",
                    factTable.getCode() + StrUtil.DOT + factField.getCode(),
                    factTable.getCode() + StrUtil.DOT + period.getCode() + " = '" + before4DateSql + "'",
                    factTable.getCode() + StrUtil.DOT + factField.getCode(),
                    factTable.getCode() + StrUtil.DOT + period.getCode() + " = '" + before5DateSql + "'",
                    factTable.getCode() + StrUtil.DOT + factField.getCode());
        } else {
            // 计分卡SQL模板
            queryColumnSqlTemplate = "SUM(CASE WHEN {0} THEN {1} ELSE 0 END) AS " + CURRENT_VALUE;
            queryColumnSql = MessageFormat.format(queryColumnSqlTemplate,
                    factTable.getCode() + StrUtil.DOT + period.getCode() + " = '" + currentDateSql + "'",
                    factTable.getCode() + StrUtil.DOT + factField.getCode());
        }
        logger.info("构建查询字段SQL:{}", queryColumnSql);
        return queryColumnSql;
    }

    /**
     * 构建权限过滤条件
     *
     * @param dimensionAuthList 权限集合
     * @return 权限条件
     */
    public List<Map<String, Object>> buildAuthFilter(List<DimensionAuth> dimensionAuthList, CurrentUserUtil.CurrentUser user) {
        List<Map<String, Object>> authFilter = new ArrayList<>();
        for (DimensionAuth dimensionAuth : dimensionAuthList) {
            // 根据dimId 找到对应的表 拼接到字段前
            Dimension dimension = dimensionService.getById(dimensionAuth.getDimId());
            Map<String, Object> map = new HashMap<>(2);
            map.put("op", dimensionAuth.getOperator());
            // 权限条件
            List<SqlBuilderVo.Filter> authInList = new ArrayList<>();
            // 根据台账权限查询关联字段信息
            List<DimensionAuthForeign> dimensionAuthForeign = dimensionAuthForeignService.getListByDimAuthId(dimensionAuth.getId());
            for (DimensionAuthForeign foreign : dimensionAuthForeign) {
                SqlBuilderVo.Filter filter = new SqlBuilderVo.Filter();
                filter.setField(dimension.getCode() + StrUtil.DOT + foreign.getRawColumn());
                filter.setOperator(SqlBuilderUtil.OPERATOR_IN);
                // 设置value
                SqliteMasterDTO.QueryVo.QueryParam queryParam = new SqliteMasterDTO.QueryVo.QueryParam();
                queryParam.setColumnName(Collections.singletonList(foreign.getTableColumn()));
                // 查找表
                AuthTable authTable = authTableService.getById(dimensionAuth.getAuthTableId());
                queryParam.setTableName(authTable.getAuthTable());
                SqliteMasterDTO.QueryVo.Filter nocoFilter = new SqliteMasterDTO.QueryVo.Filter();
                nocoFilter.setColumn(foreign.getAuthColumn());
                nocoFilter.setOp("EQ");
                if (AuthAgentEnum.USER.getType().equals(foreign.getAuthAgent())) {
                    nocoFilter.setValue(user.getThirdUserId());
                } else if (AuthAgentEnum.ROLE.getType().equals(foreign.getAuthAgent())) {
                    // 获取角色信息
                    nocoFilter.setOp("IN");
                    List<String> roleList = roleRpcService.getRoleListByUserId(user.getId());
                    nocoFilter.setValue(String.join(StrUtil.COMMA, roleList));
                } else if (AuthAgentEnum.DEPT.getType().equals(foreign.getAuthAgent())) {
                    // 获取部门信息
                    nocoFilter.setOp("IN");
                    List<String> depts = companyRpcService.getUserInDeptStr(user.getCompanyId(), user.getId());
                    nocoFilter.setValue(String.join(StrUtil.COMMA, depts));
                } else if (AuthAgentEnum.NAME.getType().equals(foreign.getAuthAgent())) {
                    // 获取用户姓名
                    nocoFilter.setValue(user.getRealName());
                    continue;
                } else {
                    return authFilter;
                }
                queryParam.setFilters(Collections.singletonList(nocoFilter));
                List<Map<String, Object>> maps = ncProjectRpcService.showDataMap(queryParam);
                List<String> list = new ArrayList<>();
                maps.forEach(stringObjectMap -> list.add(stringObjectMap.get(foreign.getTableColumn()).toString()));
                filter.setValue(String.join(StrUtil.COMMA, list));
                authInList.add(filter);
            }
            map.put("auth", authInList);
            authFilter.add(map);
        }
        return authFilter;
    }

    /**
     * 指标详情
     *
     * @param id 指标ID
     * @return T
     */
    public DwsVo.OpenAPIResponseDetailVo getOpenApiDetailById(String id) {
        Dws dws = getById(id);
        DwsVo.OpenAPIResponseDetailVo res = BeanUtil.copy(dws, DwsVo.OpenAPIResponseDetailVo.class);
        if (StringUtils.isNotBlank(res.getCreatedBy())) {
            UserDTO userDTO = userRpcService.getUserInfoById(res.getCreatedBy());
            res.setCreateUserName(null == userDTO ? "" : userDTO.getRealName());
        }
        if (StringUtils.isNotBlank(res.getUpdatedBy())) {
            UserDTO userDTO1 = userRpcService.getUserInfoById(res.getUpdatedBy());
            res.setUpdateUserName(null == userDTO1 ? "" : userDTO1.getRealName());
        }
        // 认证处理 (指标定义认证即为已认证)
        DwsAuthVo.Detail basicAuth = dwsDataAuthService.getByDwsId(1, id);
        DwsAuthVo.Detail dataAuth = dwsDataAuthService.getByDwsId(2, id);
        res.setIfCertified(null != dataAuth ? dataAuth.getStatus().toString() : null);
        res.setAuthStatus(null != basicAuth ? basicAuth.getStatus().toString() : null);
        // 标签列表
        res.setLabelList(this.getDwsListByDwsId(id));
        // 认证列表
        res.setAuthInfo(this.getAuthListByDwsId(id));
        // 来源系统列表
        res.setPlateSourceList(plateSourceService.getList(res.getPlateId(), res.getCompanyId()));
        // 板块
        res.setPlate(plateService.getById(res.getPlateId()));
        // 数据域
        res.setDataField(dataFieldService.getById(res.getDataFieldId()));
        // 业务过程
        res.setBusProcess(businessProcessService.getById(res.getBusProcessId()));
        // 维度列表
        res.setDimensionList(dimensionService.getListByIds(res.getDimIds(), res.getCompanyId()));
        // 统计周期列表
        res.setPeriodList(dimensionService.getListByIds(res.getPeriodValue(), res.getCompanyId()));
        // 基础指标列表
        res.setBasicDwsList(getBasicDwsListByIds(res.getBasicDws(), res.getCompanyId()));
        // 获取负责人用户信息
        String businessLeaders = res.getBusinessLeaders();
        String technicalLeaders = res.getTechnicalLeaders();
        List<String> leaders = new ArrayList<>();
        if (StringUtils.isNotBlank(businessLeaders)) {
            leaders.addAll(Arrays.asList(businessLeaders.split(StrUtil.COMMA)));
        }
        if (StringUtils.isNotBlank(technicalLeaders)) {
            leaders.addAll(Arrays.asList(technicalLeaders.split(StrUtil.COMMA)));
        }
        List<DwsVo.UserListResponse> principalList = new ArrayList<>();
        for (String leader : leaders) {
            UserDTO user = userRpcService.getUserInfoById(leader);
            principalList.add(BeanUtil.copy(user, DwsVo.UserListResponse.class));
        }
        res.setUserList(principalList);
        // 负责部门列表
        String plateDeptIds = res.getPlateDeptIds();
        String technicalDepts = res.getTechnicalDepts();
        List<String> deptIds = new ArrayList<>();
        if (StringUtils.isNotBlank(plateDeptIds)) {
            deptIds.addAll(Arrays.asList(plateDeptIds.split(StrUtil.COMMA)));
        }
        if (StringUtils.isNotBlank(technicalDepts)) {
            deptIds.addAll(Arrays.asList(technicalDepts.split(StrUtil.COMMA)));
        }
        res.setDeptList(plateControlDeptService.getListByIds(deptIds, res.getCompanyId()));
        // 数据挂接列表
        List<DimDws> dimDwsList = dimDwsService.getListByDwsId(id);
        List<DimDwsVo.DetailVo> detailVoList = new ArrayList<>();
        for (DimDws dimDws : dimDwsList) {
            DimDwsVo.DetailVo detailVo = new DimDwsVo.DetailVo();
            detailVo.setUnionType(dimDws.getUnionType());
            Dimension dimension = dimensionService.getById(dimDws.getDimId());
            if (null != dimension) {
                detailVo.setPlateId(dimension.getPlateId());
                detailVo.setDataFieldId(dimension.getDataFieldId());
                detailVo.setBusinessProcessId(dimension.getBusProcessId());
                detailVo.setDimType(dimension.getDataType());
                detailVo.setDimId(dimension.getId());
                detailVo.setDimName(dimension.getName());
                detailVo.setDimCode(dimension.getCode());
            }
            DimAttribute attribute = dimAttributeService.getById(dimDws.getDimAttrId());
            if (null != attribute) {
                detailVo.setDimAttrName(attribute.getName());
                detailVo.setDimAttrCode(attribute.getCode());
            }
            detailVoList.add(detailVo);
        }
        res.setDimDwsList(detailVoList);
        return res;
    }

    private List<Dws> getBasicDwsListByIds(String basicDws, String companyId) {
        // 检查输入参数
        if (basicDws == null || basicDws.trim().isEmpty()) {
            logger.warn("basicDws is null or empty");
            return super.list(new QueryWrapper<Dws>().eq("company_id", companyId).eq("if_deleted", false));
        }
        // 处理 dimIds 并去除空字符串
        List<String> idList = Arrays.stream(basicDws.split(","))
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .collect(Collectors.toList());

        if (idList.isEmpty()) {
            logger.warn("No valid ids found in basicDws");
            return super.list(new QueryWrapper<Dws>().eq("company_id", companyId).eq("if_deleted", false));
        }

        // 构建查询条件
        QueryWrapper<Dws> wrapper = new QueryWrapper<>();
        wrapper.select("id", "name").in("id", idList).eq("company_id", companyId).eq("if_deleted", false);

        return super.list(wrapper);
    }
}