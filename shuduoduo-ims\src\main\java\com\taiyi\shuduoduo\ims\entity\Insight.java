package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * 智能洞察
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_insight")
public class Insight extends CommonMySqlEntity {

    private String companyId;

    private String name;

    private String folderId;

    /**
     * 数据湖文件夹ID
     */
    private String dremioFolderId;

    private String flowId;

    private String namespace;

    private String description;

    private String content;

    private String param;

    private boolean ifDeleted;

    private String createdBy;

}