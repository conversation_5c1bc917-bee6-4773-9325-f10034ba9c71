package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.entity.DbType;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import com.taiyi.shuduoduo.common.connector.api.service.DbRpcService;
import com.taiyi.shuduoduo.ims.dao.InsightDao;
import com.taiyi.shuduoduo.ims.dremio.service.DremioApiService;
import com.taiyi.shuduoduo.ims.entity.Dws;
import com.taiyi.shuduoduo.ims.entity.Insight;
import com.taiyi.shuduoduo.ims.entity.InsightFolder;
import com.taiyi.shuduoduo.ims.entity.Plate;
import com.taiyi.shuduoduo.ims.exceptions.QueryFailedException;
import com.taiyi.shuduoduo.ims.vo.DwsAuthVo;
import com.taiyi.shuduoduo.ims.vo.DwsVo;
import com.taiyi.shuduoduo.ims.vo.InsightVo;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyDremioRpcService;
import com.taiyi.shuduoduo.ums.api.service.UserRpcService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 智能洞察服务
 *
 * <AUTHOR>
 */
@Service
public class InsightService extends CommonMysqlService<InsightDao, Insight> {
    @Override
    public Class<Insight> getEntityClass() {
        return Insight.class;
    }

    @Autowired
    private UserRpcService userRpcService;

    @Autowired
    private InsightFolderService insightFolderService;

    /**
     * 检查该文件夹及其子文件夹下是否有洞察数据
     *
     * @param folderId folderId
     * @return boolean
     */
    public boolean hasInsightsInFolderOrSubFolders(String folderId) {
        // 递归获取所有子文件夹的ID
        List<String> allFolderIds = insightFolderService.findAllSubFolderIds(folderId);
        QueryWrapper<Insight> wrapper = new QueryWrapper<>();
        wrapper.in("folder_id", allFolderIds)
                .eq("if_deleted", 0);
        // 查询这些文件夹下是否存在洞察数据
        Integer count = super.count(wrapper);
        return count > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveReturnId(InsightVo.InsertRequest t) {
        Insight insight = BeanUtil.copy(t, Insight.class);
        boolean b = super.save(insight);
        if (b) {
            CompanyDremioDTO dremioDTO = getDremioInfo(CurrentUserUtil.get().getCompanyId());
            String folderId = dremioApiService.createFolder(dremioDTO.getDremioUsername(), dremioDTO.getDremioPasswd(), dremioDTO.getDremioApiUri(),
                    "folder", t.getPath().get(3), t.getPath().get(0), t.getPath());
            UpdateWrapper<Insight> wrapper = new UpdateWrapper<>();
            wrapper.set("dremio_folder_id", folderId).eq("id", insight.getId());
            super.update(wrapper);
        }
        return insight.getId();
    }

    public CompanyDremioDTO getDremioInfo(String companyId) {
        return companyDremioRpcService.getCompanyById(companyId);
    }

    public InsightVo.DetailVo getDetailById(String id) {
        Insight insight = super.getById(id);
        InsightVo.DetailVo detailVo = BeanUtil.copy(insight, InsightVo.DetailVo.class);
        detailVo.setPlateId(insightFolderService.getById(insight.getFolderId()).getPlateId());
        return detailVo;
    }

    /**
     * 分页查询洞察数据
     *
     * @param pageVo 分页参数
     * @return PageResult
     */
    public PageResult selectPage(InsightVo.PageVo pageVo) {
        Page<Insight> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        page.setOptimizeCountSql(false);
        // 构建查询条件
        QueryWrapper<Insight> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", 0).orderByDesc("create_time");
        // 文件夹ID过滤
        if (pageVo.getFolderId() != null && !pageVo.getFolderId().isEmpty()) {
            queryWrapper.eq("folder_id", pageVo.getFolderId());
        } else if (pageVo.getPlateId() != null && !pageVo.getPlateId().isEmpty()) {
            // 根据板块ID过滤
            queryWrapper.inSql("folder_id", "SELECT id FROM ims_insight_folder WHERE plate_id = '" + pageVo.getPlateId() + "'");
        }
        // 名称模糊搜索
        if (pageVo.getKeyWord() != null && !pageVo.getKeyWord().isEmpty()) {
            queryWrapper.like("name", pageVo.getKeyWord());
        }
        IPage<Insight> iPage = super.page(page, queryWrapper);
        List<InsightVo.PageResponseVo> res = BeanUtil.copyList(iPage.getRecords(), InsightVo.PageResponseVo.class);
        for (InsightVo.PageResponseVo vo : res) {
            if (StringUtils.isNotBlank(vo.getCreatedBy())) {
                UserDTO userDTO = userRpcService.getUserInfoById(vo.getCreatedBy());
                vo.setCreateUserName(null == userDTO ? "" : userDTO.getRealName());
            }
            vo.setPlateId(insightFolderService.getById(vo.getFolderId()).getPlateId());
        }
        PageResult result = new PageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setList(res);
        result.setTotal(iPage.getTotal());
        return result;

    }

    public boolean checkFlowId(String id, String plateId, String flowId) {
        QueryWrapper<Insight> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne(StringUtils.isNotBlank(id), "id", id)
                .eq("flow_id", flowId)
                .inSql("folder_id", "SELECT id FROM ims_insight_folder WHERE plate_id = '" + plateId + "'")
                .eq("if_deleted", 0);
        // 查询是否存在该 flowId
        int count = super.count(queryWrapper);
        return count == 0;
    }

    @Autowired
    private DremioApiService dremioApiService;

    @Autowired
    private CompanyDremioRpcService companyDremioRpcService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean logicDeleteById(String id) {
        Insight insight = getById(id);
        // 删除数据湖文件夹
        CompanyDremioDTO dremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == dremioDTO) {
            return false;
        }
        dremioApiService.deleteFolder(dremioDTO.getDremioUsername(), dremioDTO.getDremioPasswd(), dremioDTO.getDremioApiUri(), insight.getDremioFolderId());
        return super.logicDeleteById(id);
    }

    @Autowired
    private DbRpcService dbRpcService;

    /**
     * 根据SQL 查询结果
     *
     * @param sql       sql
     * @param plateCode database code
     * @param dremioDTO 数据源信息
     * @return T
     */
    public List<Map<String, Object>> getQueryData(String sql, String plateCode, CompanyDremioDTO dremioDTO) {
        // 查询数据
        List<Map<String, Object>> data = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plateCode, sql));
        if (null != getErrorValues(data)) {
            throw new QueryFailedException(getErrorValues(data));
        }
        return data;
    }

    /**
     * dremio 信息 转换为通用连接参数
     *
     * @param dremioDTO dremio 信息
     * @param sql       查询SQL
     * @return DbDto.DbQueryBySql
     */
    public DbDto.DbQueryBySql convertByDremioInfo(CompanyDremioDTO dremioDTO, String plateCode, String sql) {
        DbDto.DbQueryBySql dbQueryBySql = new DbDto.DbQueryBySql();
        dbQueryBySql.setHost(dremioDTO.getDremioHost());
        dbQueryBySql.setPort(dremioDTO.getDremioPort());
        dbQueryBySql.setUsername(dremioDTO.getDremioUsername());
        dbQueryBySql.setPassword(dremioDTO.getDremioPasswd());
        dbQueryBySql.setType(DbType.DREMIO.toString());
        dbQueryBySql.setDatabase(plateCode);
        dbQueryBySql.setSql(sql);
        return dbQueryBySql;
    }

    public String getErrorValues(List<Map<String, Object>> data) {
        for (Map<String, Object> map : data) {
            if (map.containsKey("error")) {
                return map.get("error").toString();
            }
        }
        return null;
    }
}