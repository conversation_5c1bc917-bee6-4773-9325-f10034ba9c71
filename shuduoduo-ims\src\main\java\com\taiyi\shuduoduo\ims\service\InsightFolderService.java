package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.InsightFolderDao;
import com.taiyi.shuduoduo.ims.entity.InsightFolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class InsightFolderService extends CommonMysqlService<InsightFolderDao, InsightFolder> {
    @Override
    public Class<InsightFolder> getEntityClass() {
        return InsightFolder.class;
    }

    /**
     * 根据板块查询根文件夹列表
     *
     * @param plateId 板块ID
     * @return List
     */
    public List<InsightFolder> getListByPlateId(String plateId) {
        QueryWrapper<InsightFolder> wrapper = new QueryWrapper<>();
        wrapper.eq("plate_id", plateId).eq("if_deleted", 0);
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId());
        wrapper.and(w -> w.isNull("pid").or().eq("pid", ""));
        return list(wrapper);
    }

    /**
     * 根据PID获取文件夹列表
     *
     * @param pId pid
     * @return List
     */
    public List<InsightFolder> getListByPId(String pId) {
        QueryWrapper<InsightFolder> wrapper = new QueryWrapper<>();
        wrapper.eq("pid", pId).eq("if_deleted", 0);
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId());
        return list(wrapper);
    }

    /**
     * 递归查找文件夹及其所有子文件夹的ID
     *
     * @param folderId folderId
     * @return ids
     */
    public List<String> findAllSubFolderIds(String folderId) {
        List<String> folderIds = new ArrayList<>();
        folderIds.add(folderId);
        QueryWrapper<InsightFolder> wrapper = new QueryWrapper<>();
        wrapper.eq("pid", folderId);
        wrapper.eq("if_deleted", 0);
        // 获取当前文件夹的子文件夹
        List<InsightFolder> subFolders = super.list(wrapper);
        // 递归查找每个子文件夹的子文件夹
        for (InsightFolder subFolder : subFolders) {
            folderIds.addAll(findAllSubFolderIds(subFolder.getId()));
        }
        return folderIds;
    }

    /**
     * 搜索文件夹
     *
     * @param keyword keyword
     * @return List
     */
    public List<InsightFolder> getListByKeyword(String keyword) {
        QueryWrapper<InsightFolder> wrapper = new QueryWrapper<>();
        wrapper.like("name", keyword)
                .eq("if_deleted", 0)
                .eq("company_id", CurrentUserUtil.get().getCompanyId());
        return super.list(wrapper);
    }

    /**
     * 获取指定文件夹的所有父级文件夹ID
     *
     * @param folderId 当前文件夹ID
     * @return 所有父级文件夹ID列表
     */
    public List<String> getParentFolderIds(String folderId) {
        List<String> parentIds = new ArrayList<>();
        findParentFolders(folderId, parentIds);
        return parentIds;
    }

    /**
     * 递归查找父级文件夹
     *
     * @param folderId  当前文件夹ID
     * @param parentIds 父级文件夹ID列表（用于递归累加）
     */
    private void findParentFolders(String folderId, List<String> parentIds) {
        // 根据文件夹ID查询当前文件夹信息
        QueryWrapper<InsightFolder> wrapper = new QueryWrapper<>();
        wrapper.eq("id", folderId)
                .eq("if_deleted", 0)
                .eq("company_id", CurrentUserUtil.get().getCompanyId()).last("LIMIT 1");
        InsightFolder folder = super.getOne(wrapper);
        if (folder != null && StringUtils.isNotBlank(folder.getPid())) {
            // 父级文件夹ID不为空，添加到列表中
            parentIds.add(folder.getPid());
            // 递归查找父级文件夹
            findParentFolders(folder.getPid(), parentIds);
        }
    }
}