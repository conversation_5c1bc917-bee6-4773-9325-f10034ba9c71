package com.taiyi.common.auth.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 正常用户登录
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping("/login")
public class LoginController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());


}
