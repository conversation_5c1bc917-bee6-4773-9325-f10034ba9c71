package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import com.taiyi.shuduoduo.ims.entity.QueryTopic;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class TopicShareVo {

    @Data
    public static class ShareParam {

        private String id;

        /**
         * 自定义查询ID
         */
        private String topicId;

        private Integer userType;

        private List<String> toUsers;

    }

    /**
     * 最近使用
     */
    @Data
    public static class RecentResponse {

        private String id;

        private String subject;

        private Date lastTime;

        private String username;

        private Integer type;

        /**
         * 用户头像
         */
        private String avatarUri;

        /**
         * 用户ID
         */
        private String userId;
    }

    @Data
    public static class DeleteParam {
        private List<String> ids;
    }

    @Data
    public static class ShareDetailResponse {

        private String id;

        private String topicId;

        private QueryTopic queryTopic;
    }

    @Data
    public static class CancelShareParam {
        /**
         * 自定义查询ID
         */
        private String topicId;

        /**
         * 取消分享的用户ID集合
         */
        private List<String> users;

    }

    @Data
    public static class PageResultVo {

        private String id;

        private String subject;

        /**
         * 分享人
         */
        private String fromUser;

        /**
         * 被分享人
         */
        private String toUser;

        /**
         * 分享内容
         */
        private String topicId;

        /**
         * 是否已读
         */
        private Boolean ifRead;

        /**
         * 用户头像
         */
        private String avatarUri;

        /**
         * 最后一次使用时间
         */
        private Date lastTime;

        private Boolean ifDeleted;

        private String rawId;

        private Date createTime;

        private String username;
    }

    @Data
    public static class TopicSharePageVo extends CommonMySqlPageVo {

    }
}
