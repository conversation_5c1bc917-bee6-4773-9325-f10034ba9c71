package com.taiyi.shuduoduo.ims.vo;

import lombok.Data;

import java.util.List;

@Data
public class FactDimensionObjectVo {


    /**
     * 事实表ID
     */
    private String factId;
    /**
     * 事实表字段ID
     */
    private String factAttrId;

    /**
     * 维度表ID
     */
    private String dimId;

    /**
     * 维度表字段ID
     */
    private String dimAttrId;

    /**
     * 维度类型 1、统计维度 2、统计周期
     */
    private Integer dimType;

    /**
     * 统计周期标识
     */
    private String periodUnit;

    /**
     * 统计周期格式
     */
    private String periodFormat;

    private Long orderBy;


    @Data
    public static class InsertParam {

        private List<FactDimensionObjectVo> list;

    }

    @Data
    public static class ListResponse {

        private String id;

        /**
         * 事实表ID
         */
        private String factId;

        /**
         * 事实表名称
         */
        private String factName;

        /**
         * 事实表编码
         */
        private String factCode;

        /**
         * 事实表字段ID
         */
        private String factAttrId;

        /**
         * 事实表字段名称
         */
        private String factAttrName;

        /**
         * 事实表字段编码
         */
        private String factAttrCode;

        /**
         * 维度表ID
         */
        private String dimId;

        /**
         * 维度表名称
         */
        private String dimName;

        /**
         * 维度表编码
         */
        private String dimCode;

        /**
         * 维度表字段ID
         */
        private String dimAttrId;

        /**
         * 维度表字段名称
         */
        private String dimAttrName;

        /**
         * 维度表字段编码
         */
        private String dimAttrCode;

        /**
         * 维度类型 1、统计维度 2、统计周期
         */
        private Integer dimType;

        /**
         * 统计周期标识
         */
        private String periodUnit;

        /**
         * 统计周期格式
         */
        private String periodFormat;
    }
}
