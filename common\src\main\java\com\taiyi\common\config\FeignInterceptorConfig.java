package com.taiyi.common.config;

import com.taiyi.common.entity.HttpHeaderConstant;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 将 jwt Token信息自动添加到Feign RPC 调用头信息中
 *
 * <AUTHOR>
 */
@Configuration
public class FeignInterceptorConfig implements RequestInterceptor {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (null != attributes) {
            HttpServletRequest request = attributes.getRequest();
            String token = request.getHeader(HttpHeaderConstant.HEADER_TOKEN);
            String requestId = request.getHeader(HttpHeaderConstant.REQUEST_ID);
            requestTemplate.header(HttpHeaderConstant.HEADER_TOKEN, token);
            requestTemplate.header(HttpHeaderConstant.REQUEST_ID, requestId);
            logger.debug("Feign rpc request add request id: {} ,token: {}", requestId, token);
        }
    }
}
