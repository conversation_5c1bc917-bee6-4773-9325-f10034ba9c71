package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.data.redis.util.RedisRepository;
import com.taiyi.common.entity.DbType;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.DateFormatUtil;
import com.taiyi.common.util.StringUtil;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import com.taiyi.shuduoduo.common.connector.api.service.DbRpcService;
import com.taiyi.shuduoduo.ims.dao.RawDao;
import com.taiyi.shuduoduo.ims.entity.*;
import com.taiyi.shuduoduo.ims.exceptions.QueryFailedException;
import com.taiyi.shuduoduo.ims.util.CheckQueryDataResponse;
import com.taiyi.shuduoduo.ims.util.SqlBuilderUtil;
import com.taiyi.shuduoduo.ims.vo.*;
import com.taiyi.shuduoduo.nocodb.api.dto.SqliteMasterDTO;
import com.taiyi.shuduoduo.nocodb.api.servie.NcProjectRpcService;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyDremioRpcService;
import com.taiyi.shuduoduo.ums.api.service.CompanyRpcService;
import com.taiyi.shuduoduo.ums.api.service.RoleRpcService;
import com.taiyi.shuduoduo.ums.api.service.UserRpcService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 台账业务层
 *
 * <AUTHOR>
 */
@Service
public class RawService extends CommonMysqlService<RawDao, Raw> {
    @Override
    public Class<Raw> getEntityClass() {
        return Raw.class;
    }

    private static final String NOTICE_MESSAGE = "台账有表字段新关联指标";

    @Autowired
    private RawDao rawDao;

    @Autowired
    private RawTableService rawTableService;

    @Autowired
    private RawTableAttrService rawTableAttrService;

    @Autowired
    private PlateService plateService;

    @Autowired
    private DataFieldService dataFieldService;

    @Autowired
    private BusinessProcessService businessProcessService;

    @Autowired
    private PlateDimFolderService plateDimFolderService;

    @Autowired
    private RawForeignKeyService foreignKeyService;

    @Autowired
    private RawObjectService objectService;

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private DimAttributeService dimAttributeService;

    @Autowired
    private PlateLayerService plateLayerService;

    @Autowired
    private QueryTopicService queryTopicService;

    @Autowired
    private RawAuthService rawAuthService;

    @Autowired
    private RawAuthForeignService rawAuthForeignService;

    @Autowired
    private NcProjectRpcService ncProjectRpcService;

    @Autowired
    private AuthTableService authTableService;

    @Autowired
    private DbRpcService dbRpcService;

    @Autowired
    private CompanyRpcService companyRpcService;

    @Autowired
    private RoleRpcService roleRpcService;

    @Autowired
    private RawPlateControlDeptService rawPlateControlDeptService;

    @Autowired
    private RawPlateSourceService rawPlateSourceService;

    @Autowired
    private RawLicenseService rawLicenseService;

    @Autowired
    private UserRpcService userRpcService;

    @Autowired
    private CompanyDremioRpcService companyDremioRpcService;

    @Autowired
    private RedisRepository redisRepository;

    public static final String EXPORT_DATA_KEY = "EXPORT_";

    /**
     * 新增/编辑
     *
     * @param t t
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRaw(RawVo.InsertParam t) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Raw raw = BeanUtil.copy(t, Raw.class);
        raw.setCompanyId(companyId);
        if (StringUtils.isNotBlank(t.getId())) {
            // 删除表、表字段、字段分组、字段分组关联关系
            rawTableService.logicDeleteByRawId(t.getId());
            // 删除关联关系
            foreignKeyService.logicDeleteByRawId(t.getId());
            // 删除台账对象
            objectService.logicDeleteByRawId(t.getId());
            // 删除台账与板块-来源系统的关联关系
            rawPlateSourceService.logicDeleteByRawId(t.getId());
            // 删除台账与板块-来源系统的关联关系
            rawPlateControlDeptService.logicDeleteByRawId(t.getId());
            super.updateById(t.getId(), raw);
        } else {
            raw.setId(null);
            raw.setOrderBy(this.getMaxOrder());
            raw.setCreateBy(CurrentUserUtil.get().getRealName());
            super.save(raw);
        }

        logger.info("开始保存关联数据，台账ID: {}", raw.getId());

        // 保存台账与板块-来源系统的关联关系
        Optional.ofNullable(t.getControlDepts()).filter(depts -> !depts.isEmpty())
                .ifPresent(depts -> rawPlateControlDeptService.saveBatchByRaw(companyId, raw.getId(), depts));

        // 保存台账与板块-来源系统的关联关系
        Optional.ofNullable(t.getSources()).filter(sources -> !sources.isEmpty())
                .ifPresent(sources -> rawPlateSourceService.saveBatchByRaw(companyId, raw.getId(), sources));

        boolean f;
        // 保存字段对象
        f = t.getObjects().isEmpty() || objectService.saveBatchByRaw(companyId, raw.getId(), t.getObjects());
        // 保存表、表字段、字段分组、字段分组关联关系
        f = f && (t.getRowTables().isEmpty() || rawTableService.saveBatchByRaw(companyId, raw.getId(), t.getRowTables()));
        // 保存字段关联关系
        f = f && (t.getForeignKeys().isEmpty() || foreignKeyService.saveBatchByRaw(companyId, raw.getId(), t.getForeignKeys()));

        return f;
    }

    /**
     * 获取最大排序
     *
     * @return long
     */
    public Long getMaxOrder() {
        QueryWrapper<Raw> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).orderByDesc("order_by").last("LIMIT 1");
        Raw raw = super.getOne(wrapper);
        return null == raw ? 1L : raw.getOrderBy() + 1;
    }

    /**
     * 删除
     *
     * @param id 台账ID
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByRawId(String id) {
        // 删除表、表字段、字段分组、字段分组关联关系
        rawTableService.logicDeleteByRawId(id);
        // 删除关联关系
        foreignKeyService.logicDeleteByRawId(id);
        // 删除台账对象
        objectService.logicDeleteByRawId(id);
        // 删除台账与板块-来源系统的关联关系
        rawPlateSourceService.logicDeleteByRawId(id);
        // 删除台账与板块-来源系统的关联关系
        rawPlateControlDeptService.logicDeleteByRawId(id);
        return super.logicDeleteById(id);
    }

    /**
     * 查询台账详情
     *
     * @param rawId 台账ID
     * @return 详情
     */
    public RawVo.DetailResponse getDetailById(String rawId) {
        Raw raw = super.getById(rawId);
        RawVo.DetailResponse res = BeanUtil.copy(raw, RawVo.DetailResponse.class);
        // 获取台账与板块-来源系统的关联关系
        res.setControlDepts(rawPlateControlDeptService.detailListByRawId(rawId));
        // 获取台账与板块-来源系统的关联关系
        res.setSources(rawPlateSourceService.detailListByRawId(rawId));
        // 获取表及表字段信息
        res.setRowTables(rawTableService.detailListByRawId(rawId));
        // 获取台账对象信息
        res.setObjects(objectService.detailListByRawId(rawId));
        // 获取外键字段信息
        res.setForeignKeys(foreignKeyService.detailListByRawId(rawId));
        // 更新最近使用时间 and 使用次数
        updateLastTime(raw);
        return res;
    }

    /**
     * 查看/操作更新使用时间
     *
     * @param raw 台账信息
     */
    public void updateLastTime(Raw raw) {
        UpdateWrapper<Raw> wrapper = new UpdateWrapper<>();
        wrapper.set("last_time", new Date()).set("total", (raw.getTotal() == null ? 0 : raw.getTotal()) + 1).eq("id", raw.getId());
        super.update(wrapper);
    }

    /**
     * 分页查询
     *
     * @param pageVo 分页参数
     * @return list
     */
    public PageResult<RawVo.PageResponse> getPage(RawVo.RawPageVo pageVo) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Page<Raw> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        List<String> controlDeptRawIds = null;
        List<String> sourceRawIds = null;
        QueryWrapper<Raw> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("if_deleted", false)
                .eq(StringUtils.isNotBlank(pageVo.getPlateId()), "plate_id", pageVo.getPlateId())
                .eq(StringUtils.isNotBlank(pageVo.getDimFolderId()), "dim_folder_id", pageVo.getDimFolderId())
                .eq(StringUtils.isNotBlank(pageVo.getDataFieldId()), "data_field_id", pageVo.getDataFieldId())
                .eq(StringUtils.isNotBlank(pageVo.getBusProcessId()), "bus_process_id", pageVo.getBusProcessId())
                .eq(StrUtil.isNotBlank(pageVo.getId()), "id", pageVo.getId());
        if (StrUtil.isNotBlank(pageVo.getControlDeptId())) {
            controlDeptRawIds = rawPlateControlDeptService.getRawListByControlDeptId(pageVo.getControlDeptId());
            if (controlDeptRawIds.isEmpty()) {
                return new PageResult<>();
            }
            wrapper.in("id", controlDeptRawIds);
        }
        if (StrUtil.isNotBlank(pageVo.getSourceId())) {
            sourceRawIds = rawPlateSourceService.getRawListBySourceId(pageVo.getSourceId());
            if (sourceRawIds.isEmpty()) {
                return new PageResult<>();
            }
            wrapper.in("id", sourceRawIds);
        }
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.like("name", pageVo.getKeyWord());
        }
        if (StringUtils.isNotBlank(pageVo.getId())) {
            wrapper.eq("id", pageVo.getId());
        }
        if (pageVo.getType() == 1) {
            wrapper.orderByAsc("order_by");
        } else if (pageVo.getType() == 2) {
            wrapper.orderByDesc("last_time");
        } else if (pageVo.getType() == 3) {
            wrapper.orderByDesc("total");
        } else {
            wrapper.orderByDesc("create_time");
        }
        IPage<Raw> iPage = super.page(page, wrapper);
        List<RawVo.PageResponse> responseList = BeanUtil.copyList(iPage.getRecords(), RawVo.PageResponse.class);
        for (RawVo.PageResponse response : responseList) {
            // 设置目录
            response.setTarget(
                    (StringUtils.isNotBlank(response.getPlateId()) ? (plateService.getById(response.getPlateId()).getName()) : "") +
                            (StringUtils.isNotBlank(response.getDimFolderId()) ? ("-" + plateDimFolderService.getById(response.getDimFolderId()).getName()) : "") +
                            (StringUtils.isNotBlank(response.getDataFieldId()) ? ("-" + dataFieldService.getById(response.getDataFieldId()).getName()) : "") +
                            (StringUtils.isNotBlank(response.getBusProcessId()) ? ("-" + businessProcessService.getById(response.getBusProcessId()).getName()) : "")
            );
            // 设置认证信息
            response.setRawLicense(rawLicenseService.getByRawIdAndUserId(response.getId(), null));
            // 设置是否有所属表字段更新
            RawTable rawTable = rawTableService.getHasDwsByRawId(companyId, response.getId());
            if (null != rawTable) {
                response.setDwsId(rawTable.getDwsId());
                response.setNoticeMsg(NOTICE_MESSAGE);
            }
        }
        PageResult<RawVo.PageResponse> result = new PageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setList(responseList);
        result.setTotal(iPage.getTotal());
        return result;
    }

    /**
     * 查询台账列表
     *
     * @param unionId 板块、维度文件夹、数据域、业务过程ID
     * @param type    1、板块2、维度文件夹3、数据域、4、业务过程
     * @param keyWord 关键字筛选
     * @return T
     */
    public List<RawVo.ListResponse> getListByUnionIdAndType(String unionId, Integer type, String keyWord) {
        QueryWrapper<Raw> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false);
        if (type == 1) {
            wrapper.eq("plate_id", unionId);
        } else if (type == 2) {
            wrapper.eq("dim_folder_id", unionId);
        } else if (type == 3) {
            wrapper.eq("data_field_id", unionId);
        } else if (type == 4) {
            wrapper.eq("bus_process_id", unionId);
        }
        if (StringUtils.isNotBlank(keyWord)) {
            wrapper.like("name", keyWord);
        }
        wrapper.orderByDesc("order_by", "create_time");
        return BeanUtil.copyList(super.list(wrapper), RawVo.ListResponse.class);
    }

    /**
     * 查询浏览器台账详情
     *
     * @param raw 台账
     * @return 台账详情
     */
    public RawVo.BrowserDetail getBrowserDetail(Raw raw) {
        RawVo.BrowserDetail res = BeanUtil.copy(raw, RawVo.BrowserDetail.class);
        // 设置由我创建
        res.setCreateByMyself(queryTopicService.browserDetailByRawId(raw.getId()));
        // 设置由其他人创建
        res.setCreateByOther(queryTopicService.browserDetailByRawIdExceptMe(raw.getId()));
        // 设置对象
        res.setObjects(objectService.browserDetailByRawId(raw.getId()));
        // 设置字段
        res.setRowTableAttrs(rawTableService.browserDetailByRawId(raw.getId()));
        return res;
    }

    /**
     * 查询可选字段列表
     *
     * @param raw 台账对象
     * @return 可选字段列表
     */
    public List<RawTableVo.OptionParam> getOptionalList(Raw raw) {
        return rawTableService.optionListByRawId(raw.getId());
    }

    /**
     * 查询可选字段列表
     *
     * @param raw 台账对象
     * @return 可选字段列表
     */
    public List<RawTableVo.OptionParam> getTableWithAttrTree(Raw raw) {
        return rawTableService.getTableWithAttrTree(raw.getId());
    }

    /**
     * 查询数据
     */
    public PageResult<Map<String, Object>> queryData(QueryDataVo.QueryRequest param, Raw raw, Plate plate, CompanyDremioDTO dremioDTO) {
        CurrentUserUtil.CurrentUser user = CurrentUserUtil.get();
        PageResult<Map<String, Object>> result = new PageResult<>();
        result.setPageNo(param.getPageNo());
        result.setPageSize(param.getPageSize());

        // 查询列
        List<String> queryColumns = new ArrayList<>();
        // 查询表
        List<String> queryTables = new ArrayList<>();
        // 关联关系
        List<Map<String, Object>> joinKeys;
        // 权限校验
        List<Map<String, Object>> authFilter = null;

        // 查询表及表字段
        List<RawTableAttr> attrs = rawTableAttrService.listByIds(param.getTableAttrIds());

        for (RawTableAttr attr : attrs) {
            // 获取所在表
            RawTable rawTable = rawTableService.getById(attr.getRawTableId());
            queryColumns.add(rawTable.getCode() + StrUtil.DOT + attr.getCode() + " AS " + CharUtil.DOUBLE_QUOTES + rawTable.getCode() + SqlBuilderUtil.DOLLAR + attr.getCode() + CharUtil.DOUBLE_QUOTES);
            String queryTable = getQueryTable(attr.getRawTableId());
            if (StringUtils.isNotBlank(queryTable) && !queryTables.contains(queryTable)) {
                queryTables.add(queryTable);
            }
        }

        // 查询表字段关联关系
        List<RawForeignKey> foreignKeys = foreignKeyService.listByRawId(raw.getId());
        // 添加未在查询列但包含外键关系的表
        for (RawForeignKey foreignKey : foreignKeys) {
            String queryTable = getQueryTable(foreignKey.getRawTableId());
            if (StringUtils.isNotBlank(queryTable) && !queryTables.contains(queryTable)) {
                queryTables.add(queryTable);
            }
            String unionQueryTable = getQueryTable(foreignKey.getUnionRawTableId());
            if (StringUtils.isNotBlank(unionQueryTable) && !queryTables.contains(unionQueryTable)) {
                queryTables.add(unionQueryTable);
            }
        }
        joinKeys = buildForeignFilter(foreignKeys);

        // 管理员不参与权限校验
        if (!user.getIfBackend() && raw.getIfAuth()) {
            // 查询权限信息
            List<RawAuth> rawAuthList = rawAuthService.getListByRawId(raw.getId());
            // 无权限信息直接返回空数据
            if (rawAuthList.isEmpty()) {
                result.setList(new ArrayList<>());
                result.setTotal(0);
                return result;
            }
            authFilter = buildAuthFilter(rawAuthList, user);
        }

        // 组装SQL
        String sql = SqlBuilderUtil.buildSql(false, queryColumns, queryTables, joinKeys, param.getFilters(), authFilter, param.getOrders(), null);
        // 分页SQL
        String pageSql = SqlBuilderUtil.buildPageSql(sql, new Page(param.getPageNo(), param.getPageSize()));
        // 总数SQL
        String countSql = SqlBuilderUtil.buildCountSql(sql);

        // 查询数据
        List<Map<String, Object>> data = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, pageSql));
        if (null != CheckQueryDataResponse.getErrorValues(data)) {
            throw new QueryFailedException(CheckQueryDataResponse.getErrorValues(data));
        }
        result.setList(data);
        List<Map<String, Object>> total = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, countSql));
        if (null != CheckQueryDataResponse.getErrorValues(total)) {
            throw new QueryFailedException(CheckQueryDataResponse.getErrorValues(total));
        }
        result.setTotal(Optional.of(total)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(map -> map.get("total"))
                .map(Object::toString)
                .map(Long::valueOf)
                .orElse(0L));
        return result;
    }

    public Object queryDataByAi(QueryDataVo.QueryRequest param, Raw raw, String companyId) {
        // 查询板块信息
        Plate plate = plateService.getById(raw.getPlateId());
        if (null == plate) {
            return TipsConstant.RAW_BELONGS_PLATE_NOT_FOUND;
        }
        // 获取数据源信息
        CompanyDremioDTO companyDremioDTO = companyDremioRpcService.getCompanyById(companyId);
        if (null == companyDremioDTO) {
            return TipsConstant.DATA_SOURCE_NOT_FOUND;
        }
        param.setPageNo(1);
        param.setPageSize(50);
        return queryData(param, raw, plate, companyDremioDTO);
    }

    /**
     * 根据台账表ID获取数据湖查询表名
     *
     * @param rawTableId 台账表ID
     * @return 数据湖查询表名
     */
    public String getQueryTable(String rawTableId) {
        // 获取所在表
        RawTable rawTable = rawTableService.getById(rawTableId);
        // 查询所对应的维度表信息所在的分层信息
        Dimension dimension = dimensionService.getById(rawTable.getDimId());
        if (dimension == null) {
            return null;
        }
        // 查询维度表所在的分层信息
        PlateLayer plateLayer = plateLayerService.getById(dimension.getPlateLayerId());
        if (plateLayer == null) {
            return null;
        }
        return plateLayer.getCode() + StrUtil.DOT + rawTable.getCode();
    }

    /**
     * dremio 信息 转换为通用连接参数
     *
     * @param dremioDTO dremio 信息
     * @param sql       查询SQL
     * @return DbDto.DbQueryBySql
     */
    public DbDto.DbQueryBySql convertByDremioInfo(CompanyDremioDTO dremioDTO, Plate plate, String sql) {
        DbDto.DbQueryBySql dbQueryBySql = new DbDto.DbQueryBySql();
        dbQueryBySql.setHost(dremioDTO.getDremioHost());
        dbQueryBySql.setPort(dremioDTO.getDremioPort());
        dbQueryBySql.setUsername(dremioDTO.getDremioUsername());
        dbQueryBySql.setPassword(dremioDTO.getDremioPasswd());
        dbQueryBySql.setType(DbType.DREMIO.toString());
        dbQueryBySql.setDatabase(plate.getPlateCode());
        dbQueryBySql.setSql(sql);
        return dbQueryBySql;
    }

    /**
     * 导出
     *
     * @param param     导出参数
     * @param raw       台账信息
     * @param plate     板块信息
     * @param dremioDTO dremio信息
     * @return map
     */
    public Map<String, Object> export(QueryDataVo.QueryRequest param, Raw raw, Plate plate, CompanyDremioDTO dremioDTO) {
        CurrentUserUtil.CurrentUser user = CurrentUserUtil.get();
        String key = EXPORT_DATA_KEY + IdUtil.simpleUUID();
        long expireIn = 5 * 60;
        Map<String, Object> map = new HashMap<>(2);
        map.put("id", key);
        map.put("expireIn", expireIn);
        List<Map<String, Object>> list = getExportData(user, param, raw, plate, dremioDTO);
        try {
            redisRepository.setExpire(key, list, expireIn);
        } catch (Exception e) {
            return null;
        }
        return map;
    }

    /**
     * redis 获取导出数据
     *
     * @param key key
     * @return 数据
     */
    public List<Map<String, Object>> getExportData(String key) {
        Object o = redisRepository.get(key);
        if (o == null) {
            return new ArrayList<>();
        }
        return (List<Map<String, Object>>) o;
    }

    /**
     * 查询IN列表
     *
     * @param param 查询参数
     * @return T
     */
    public PageResult<String> queryInData(QueryDataVo.InQueryRequest param, Plate plate, CompanyDremioDTO dremioDTO) {
        PageResult<String> result = new PageResult<>();
        result.setPageNo(param.getPageNo());
        result.setPageSize(param.getPageSize());
        RawTableAttr attr = rawTableAttrService.getById(param.getTableAttrId());
        // 查询列
        List<String> queryColumns = new ArrayList<>();
        // 查询表
        List<String> queryTables = new ArrayList<>();
        // 筛选列
        List<SqlBuilderVo.Filter> filters = new ArrayList<>();

        // 获取所在表
        RawTable rawTable = rawTableService.getById(attr.getRawTableId());
        queryColumns.add(rawTable.getCode() + StrUtil.DOT + attr.getCode() + " AS " + CharUtil.DOUBLE_QUOTES + rawTable.getCode() + SqlBuilderUtil.DOLLAR + attr.getCode() + CharUtil.DOUBLE_QUOTES);
        String queryTable = getQueryTable(attr.getRawTableId());
        if (StringUtils.isNotBlank(queryTable)) {
            queryTables.add(queryTable);
        }

        SqlBuilderVo.Filter filter = new SqlBuilderVo.Filter();
        filter.setField(rawTable.getCode() + StrUtil.DOT + attr.getCode());
        filter.setOperator(SqlBuilderUtil.OPERATOR_IS_NOT);
        filters.add(filter);

        if (StringUtils.isNoneBlank(param.getKeyWord())) {
            filter = new SqlBuilderVo.Filter();
            filter.setField(rawTable.getCode() + StrUtil.DOT + attr.getCode());
            filter.setOperator(SqlBuilderUtil.OPERATOR_LIKE);
            filter.setValue(param.getKeyWord());
            filters.add(filter);
        }

        // 组装SQL
        String sql = SqlBuilderUtil.buildSql(true, queryColumns, queryTables, null, filters, null, param.getOrders(), null);
        // 分页SQL
        String pageSql = SqlBuilderUtil.buildPageSql(sql, new Page(param.getPageNo(), param.getPageSize()));
        // 总数SQL
        String countSql = SqlBuilderUtil.buildCountSql(sql);

        // 查询数据
        List<Map<String, Object>> data = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, pageSql));
        if (null != CheckQueryDataResponse.getErrorValues(data)) {
            throw new QueryFailedException(CheckQueryDataResponse.getErrorValues(data));
        }
        List<String> res = new ArrayList<>();
        for (Map<String, Object> map : data) {
            Object o = map.get(rawTable.getCode() + SqlBuilderUtil.DOLLAR + attr.getCode());
            res.add(null == o ? null : o.toString());
        }
        result.setList(res);
        List<Map<String, Object>> total = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, countSql));
        if (null != CheckQueryDataResponse.getErrorValues(total)) {
            throw new QueryFailedException(CheckQueryDataResponse.getErrorValues(total));
        }
        result.setTotal(Optional.of(total)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(map -> map.get("total"))
                .map(Object::toString)
                .map(Long::valueOf)
                .orElse(0L));
        return result;
    }

    /**
     * 查询台账列表
     *
     * @param catalogId 板块、维度文件夹、数据域、业务过程ID
     * @param type      1、板块2、维度文件夹3、数据域、4、业务过程
     * @return T
     */
    public List<RawVo.ListResponse> getAuthRawListByUnionIdAndType(String catalogId, Integer type, String keyWord) {
        QueryWrapper<Raw> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false);
        if (StringUtils.isNotBlank(keyWord)) {
            wrapper.like("name", keyWord);
        }
        if (type == 1) {
            wrapper.eq("plate_id", catalogId);
            wrapper.and((rawQueryWrapper -> {
                rawQueryWrapper.isNull("dim_folder_id").or().eq("dim_folder_id", "");
            }));
            wrapper.and((rawQueryWrapper -> {
                rawQueryWrapper.isNull("data_field_id").or().eq("data_field_id", "");
            }));
            wrapper.and((rawQueryWrapper -> {
                rawQueryWrapper.isNull("bus_process_id").or().eq("bus_process_id", "");
            }));
        } else if (type == 2) {
            wrapper.eq("dim_folder_id", catalogId);
        } else if (type == 3) {
            wrapper.eq("data_field_id", catalogId);
            wrapper.and((rawQueryWrapper -> {
                rawQueryWrapper.isNull("bus_process_id").or().eq("bus_process_id", "");
            }));
        } else if (type == 4) {
            wrapper.eq("bus_process_id", catalogId);
        }
        wrapper.orderByDesc("order_by", "create_time");
        return BeanUtil.copyList(super.list(wrapper), RawVo.ListResponse.class);
    }

    /**
     * 根据企业ID 查询台账列表
     *
     * @param companyId 企业ID
     * @return list
     */
    public List<Raw> getListByCompanyId(String companyId) {
        QueryWrapper<Raw> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("company_id", companyId);
        return super.list(wrapper);
    }

    /**
     * 构建权限过滤条件
     *
     * @param rawAuthList 权限集合
     * @return 权限条件
     */
    public List<Map<String, Object>> buildAuthFilter(List<RawAuth> rawAuthList, CurrentUserUtil.CurrentUser user) {
        List<Map<String, Object>> authFilter = new ArrayList<>();
        for (RawAuth rawAuth : rawAuthList) {
            Map<String, Object> map = new HashMap<>(2);
            map.put("op", rawAuth.getOperator());
            // 权限条件
            List<SqlBuilderVo.Filter> authInList = new ArrayList<>();
            // 根据台账权限查询关联字段信息
            List<RawAuthForeign> rawAuthForeign = rawAuthForeignService.getListByRawAuthId(rawAuth.getId());
            for (RawAuthForeign foreign : rawAuthForeign) {
                SqlBuilderVo.Filter filter = new SqlBuilderVo.Filter();
                filter.setField(foreign.getRawColumn());
                filter.setOperator(SqlBuilderUtil.OPERATOR_IN);
                // 设置value
                SqliteMasterDTO.QueryVo.QueryParam queryParam = new SqliteMasterDTO.QueryVo.QueryParam();
                queryParam.setColumnName(Collections.singletonList(foreign.getTableColumn()));
                // 查找表
                AuthTable authTable = authTableService.getById(rawAuth.getAuthTableId());
                queryParam.setTableName(authTable.getAuthTable());
                SqliteMasterDTO.QueryVo.Filter nocoFilter = new SqliteMasterDTO.QueryVo.Filter();
                nocoFilter.setColumn(foreign.getAuthColumn());
                nocoFilter.setOp("EQ");
                if (AuthAgentEnum.USER.getType().equals(foreign.getAuthAgent())) {
                    nocoFilter.setValue(user.getThirdUserId());
                } else if (AuthAgentEnum.ROLE.getType().equals(foreign.getAuthAgent())) {
                    // 获取角色信息
                    nocoFilter.setOp("IN");
                    List<String> roleList = roleRpcService.getRoleListByUserId(user.getId());
                    nocoFilter.setValue(String.join(StrUtil.COMMA, roleList));
                } else if (AuthAgentEnum.DEPT.getType().equals(foreign.getAuthAgent())) {
                    // 获取部门信息
                    nocoFilter.setOp("IN");
                    List<String> depts = companyRpcService.getUserInDeptStr(user.getCompanyId(), user.getId());
                    nocoFilter.setValue(String.join(StrUtil.COMMA, depts));
                } else if (AuthAgentEnum.NAME.getType().equals(foreign.getAuthAgent())) {
                    // 获取用户姓名
                    nocoFilter.setValue(user.getRealName());
                    continue;
                } else {
                    return authFilter;
                }
                queryParam.setFilters(Collections.singletonList(nocoFilter));
                List<Map<String, Object>> maps = ncProjectRpcService.showDataMap(queryParam);
                List<String> list = new ArrayList<>();
                maps.forEach(stringObjectMap -> list.add(stringObjectMap.get(foreign.getTableColumn()).toString()));
                filter.setValue(String.join(StrUtil.COMMA, list));
                authInList.add(filter);
            }
            map.put("auth", authInList);
            authFilter.add(map);
        }
        return authFilter;
    }

    /**
     * 构建外键关联条件
     *
     * @param foreignKeys 外键集合
     * @return 外键关联条件
     */
    public List<Map<String, Object>> buildForeignFilter(List<RawForeignKey> foreignKeys) {
        List<Map<String, Object>> joinKeys = new ArrayList<>();
        for (RawForeignKey foreignKey : foreignKeys) {
            Map<String, Object> map = new HashMap<>(2);
            map.put("main", rawTableService.getById(foreignKey.getRawTableId()).getCode() + StrUtil.DOT + foreignKey.getDimAttrCode());
            map.put("join", rawTableService.getById(foreignKey.getUnionRawTableId()).getCode() + StrUtil.DOT + foreignKey.getUnionDimAttrCode());
            joinKeys.add(map);
        }
        return joinKeys;
    }

    /**
     * 搜索列表
     *
     * @param param 搜索参数
     * @return List
     */
    public List<RawVo.SearchResponse> getSearchRawList(RawVo.SearchParam param) {
        QueryWrapper<Raw> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false);
        if (StringUtils.isNotBlank(param.getKeyWord())) {
            wrapper.like("name", param.getKeyWord());
        }
        return rawDao.selectSearchRawList(wrapper, param.getKeyWord(), CurrentUserUtil.get().getCompanyId());
    }

    /**
     * 刷新台账表数据
     *
     * @param rawTable 台账表信息
     * @return List
     **/
    public List<RawTableAttrVo.InsertParam> refreshRawTableAttrs(RawTable rawTable) {
        List<RawTableAttrVo.InsertParam> res = new ArrayList<>();
        // 台账已保存的字段列表
        List<RawTableAttrVo.InsertParam> rawList = rawTableAttrService.detailListByRawTableId(rawTable.getId());
        // 台账表对应的事实/维度表字段列表
        List<DimAttribute> attributeList = dimAttributeService.listByDimId(rawTable.getDimId());

        List<String> codes = rawList.stream().collect(ArrayList::new, (list, rawTableAttr) -> list.add(rawTableAttr.getCode()), ArrayList::addAll);
        List<String> names = rawList.stream().collect(ArrayList::new, (list, rawTableAttr) -> list.add(rawTableAttr.getName()), ArrayList::addAll);
        List<String> aliasNames = rawList.stream().collect(ArrayList::new, (list, rawTableAttr) -> list.add(rawTableAttr.getAliasName()), ArrayList::addAll);
        List<String> dwsIds = rawList.stream().collect(ArrayList::new, (list, rawTableAttr) -> {
            if (null != rawTableAttr.getDwsId()) {
                list.add(rawTableAttr.getDwsId());
            }
        }, ArrayList::addAll);
        logger.info("台账字段指标ID集合：{}", dwsIds);

        //事实/维度表字段列表
        for (DimAttribute attr : attributeList) {
            // 台账已保存的字段列表
            /*for (RawTableAttrVo.InsertParam param : rawList) {
                // 如果[名称]、[代码]、[别名]、[指标] 都相同,使用台账表字段
                *//*if (param.getCode().equals(attr.getCode()) && param.getName().equals(attr.getName())
                        && param.getAliasName().equals(attr.getAliasName()) && param.getDwsId().equals(attr.getDwsId())) {
                    res.add(param);
                } else*//*
                if (param.getCode().equals(attr.getCode())) {
                    // 如果[代码]相同 ,使用台账字段格式信息，更新其他信息
                    param.setName(attr.getName());
                    param.setAliasName(attr.getAliasName());
                    param.setDwsId(attr.getDwsId());
                    res.add(param);
                } else {
                    // 其他情况更新为维度表信息
                    res.add(rawTableAttrService.convertToRawTableAttrVo(attr));
                }
            }*/

            // 如果[名称],[代码],[别名],[指标]都存在,不更新
            if (codes.contains(attr.getCode()) && names.contains(attr.getName())
                    && aliasNames.contains(attr.getAliasName()) && dwsIds.contains(attr.getDwsId()) && attr.getDwsId() != null) {

                // 使用原台账信息
                RawTableAttrVo.InsertParam param2 = rawList.stream().filter(param -> param.getCode().equals(attr.getCode())).findFirst().get();
                res.add(param2);
            }
            // 如果[代码]相同，保留已有的开启、格式、默认查询与分组信息。
            else if (codes.contains(attr.getCode())) {
                RawTableAttrVo.InsertParam param1 = rawList.stream().filter(param -> param.getCode().equals(attr.getCode())).findFirst().get();
                param1.setName(attr.getName());
                param1.setAliasName(attr.getAliasName());
                param1.setDwsId(attr.getDwsId());
                res.add(param1);
            } else {
                // 其他更新为维度表信息
                res.add(rawTableAttrService.convertToRawTableAttrVo(attr));
            }
        }
        /*for (RawTableAttrVo.InsertParam param : rawList) {
            // 如果代码相同
            if (param.getCode().equals(attr.getCode())) {
                param.setName(attr.getName());
                param.setAliasName(attr.getAliasName());
                param.setDwsId(attr.getDwsId());
                res.add(param);
                break;
            }
            if (StringUtils.isEmpty(param.getDwsId()) && attr.getDwsId() != null) {
                res.add(rawTableAttrService.convertToRawTableAttrVo(attr));
                break;
            }
            if (param.getCode().equals(attr.getCode()) && param.getName().equals(attr.getName())
                    && param.getAliasName().equals(attr.getAliasName())
                    && ((StringUtils.isEmpty(param.getDwsId()) && StringUtils.isEmpty(attr.getDwsId())) || param.getDwsId().equals(attr.getDwsId()))) {
                res.add(param);
                break;
            }
        }*/
        return res;
    }

    /**
     * 根据用户查询台账列表
     *
     * @param userId  用户ID
     * @param keyWord 关键词筛选
     * @return 台账列表
     */
    public List<Raw> getRawListByUserId(String userId, String keyWord) {
        QueryWrapper<Raw> queryWrapper = new QueryWrapper<>();
        UserDTO userDTO = userRpcService.getUserInfoById(userId);
        queryWrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("create_by", userDTO.getRealName())
                .like(StrUtil.isNotBlank(keyWord), "name", keyWord)
                .eq("if_deleted", false)
                .orderByDesc("order_by", "create_time");
        return super.list(queryWrapper);
    }

    /**
     * 获取默认显示的字段ID
     *
     * @param rawId 台账ID
     * @return 字段ID
     */
    public List<String> getDefaultShowAttrs(String rawId) {
        return rawTableService.defaultShowAttrListByRawId(rawId);
    }

    /**
     * 获取导出数据
     *
     * @return List<Map < String, Object>>
     */
    public List<Map<String, Object>> getExportData(CurrentUserUtil.CurrentUser user, QueryDataVo.QueryRequest param, Raw raw, Plate plate, CompanyDremioDTO dremioDTO) {
        // 查询列
        List<String> queryColumns = new ArrayList<>();
        // 查询表
        List<String> queryTables = new ArrayList<>();
        // 关联关系
        List<Map<String, Object>> joinKeys;
        // 权限校验
        List<Map<String, Object>> authFilter = null;

        // 查询表及表字段
        List<RawTableAttr> attrs = rawTableAttrService.listByIds(param.getTableAttrIds());
        for (int i = 0; i < attrs.size(); i++) {
            // 获取所在表
            RawTable rawTable = rawTableService.getById(attrs.get(i).getRawTableId());
            queryColumns.add(rawTable.getCode() + StrUtil.DOT + attrs.get(i).getCode() + " AS " + CharUtil.DOUBLE_QUOTES + rawTable.getCode() + SqlBuilderUtil.DOLLAR + attrs.get(i).getCode() + CharUtil.DOUBLE_QUOTES);
            String queryTable = getQueryTable(attrs.get(i).getRawTableId());
            if (StringUtils.isNotBlank(queryTable) && !queryTables.contains(queryTable)) {
                queryTables.add(queryTable);
            }
        }

        // 查询表字段关联关系
        List<RawForeignKey> foreignKeys = foreignKeyService.listByRawId(raw.getId());
        // 添加未在查询列但包含外键关系的表
        for (int j = 0; j < foreignKeys.size(); j++) {
            String queryTable = getQueryTable(foreignKeys.get(j).getRawTableId());
            if (StringUtils.isNotBlank(queryTable) && !queryTables.contains(queryTable)) {
                queryTables.add(queryTable);
            }
            String unionQueryTable = getQueryTable(foreignKeys.get(j).getUnionRawTableId());
            if (StringUtils.isNotBlank(unionQueryTable) && !queryTables.contains(unionQueryTable)) {
                queryTables.add(unionQueryTable);
            }
        }
        joinKeys = buildForeignFilter(foreignKeys);

        // 管理员不参与权限校验
        if (!user.getIfBackend() && raw.getIfAuth()) {
            // 查询权限信息
            List<RawAuth> rawAuthList = rawAuthService.getListByRawId(raw.getId());
            // 无权限信息直接返回空数据
            if (rawAuthList.isEmpty()) {
                return new ArrayList<>();
            }
            authFilter = buildAuthFilter(rawAuthList, user);
        }

        // 组装SQL
        String exportSql = SqlBuilderUtil.buildSql(false, queryColumns, queryTables, joinKeys, param.getFilters(), authFilter, param.getOrders(), null);
        // 分页SQL
        String pageSql = SqlBuilderUtil.buildPageSql(exportSql, new Page(param.getPageNo(), param.getPageSize()));
        List<Map<String, Object>> data = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, pageSql));
        //处理data
        return data.stream()
                .map(row -> {
                    Map<String, Object> resMap = new LinkedHashMap<>();
                    for (Map.Entry<String, Object> entry : row.entrySet()) {
                        String[] values = entry.getKey().split("\\$");
                        for (RawTableAttr attr : attrs) {
                            if (values[1].equals(attr.getCode())) {
                                String columnName = attr.getAliasName();
                                if (columnName == null || columnName.isEmpty()) {
                                    columnName = attr.getName();
                                }
                                if (columnName == null || columnName.isEmpty()) {
                                    columnName = values[1];
                                }
                                resMap.put(columnName, entry.getValue());
                                break;
                            }
                        }
                    }
                    return resMap;
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建生成Excel
     *
     * @param sheet   excel-sheet
     * @param mapList 表内容
     */
    public void createExcel(SXSSFSheet sheet, List<Map<String, Object>> mapList, Boolean ifShow, String headerContext) {
        Row row = null;
        // 当前sheet的最后一行
        int start = sheet.getLastRowNum();
        // 如果创建了一个 sheet，但该 sheet 里没有数据，getLastRowNum 方法将返回 -1。这表示该 sheet 中并没有实际的数据行。
        if (start == -1) {
            if (ifShow) {
                row = sheet.createRow(0);
                Cell cell = row.createCell(0, CellType.STRING);
                cell.setCellValue(headerContext);
            }
            //创建表头
            row = sheet.createRow(sheet.getLastRowNum() + 1);
            if (!mapList.isEmpty()) {
                Set<String> keySet = mapList.get(0).keySet();
                for (int i = 0; i < keySet.size(); i++) {
                    Cell cell = row.createCell(i);
                    cell.setCellValue(keySet.toArray()[i].toString());
                    //表头自适应宽度,单元格单行最长支持255*256宽度（每个单元格样式已经设置自动换行，超出即换行）
                    int width = Math.max(15 * 256, Math.min(255 * 256, sheet.getColumnWidth(i) * 12 / 10));
                    sheet.setColumnWidth(i, width);
                }
            }
        }
        //写入表内容
        if (!mapList.isEmpty()) {
            for (int i = 0; i < mapList.size(); i++) {
                row = sheet.createRow(1 + sheet.getLastRowNum());
                Map<String, Object> table = mapList.get(i);
                for (int j = 0; j < table.keySet().size(); j++) {
                    Cell cell = row.createCell(j);
                    Object cellValue = null;
                    if (table.get(table.keySet().toArray()[j].toString()) != null) {
                        cellValue = table.get(table.keySet().toArray()[j].toString());
                    }
                    if (ObjectUtil.isEmpty(cellValue) || ObjectUtil.isNull(cellValue)) {
                        cell.setCellType(CellType.STRING);
                        cell.setCellValue("");
                    } else if (cellValue instanceof Date) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue(DateFormatUtil.dateFormat((Date) cellValue));
                    } else if (cellValue instanceof Short) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue((Short) cellValue);
                    } else if (cellValue instanceof Long) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue((Long) cellValue);
                    } else if (cellValue instanceof Integer) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue((Integer) cellValue);
                    } else if (cellValue instanceof Double) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue((Double) cellValue);
                    } else if (cellValue instanceof BigDecimal) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue(((BigDecimal) cellValue).doubleValue());
                    } else if (cellValue instanceof Boolean) {
                        cell.setCellType(CellType.BOOLEAN);
                        cell.setCellValue((Boolean) cellValue);
                    } else {
                        cell.setCellType(CellType.STRING);
                        cell.setCellValue(cellValue.toString());
                    }
                }
            }
        }
    }

    /**
     * 查询首行数据
     *
     * @param param     查询参数
     * @param raw       台账
     * @param plate     板块
     * @param dremioDTO 连接信息
     * @return list
     */
    public List<Map<String, Object>> queryFirstData(CurrentUserUtil.CurrentUser user, QueryDataVo.QueryRequest param, Raw raw, Plate plate, CompanyDremioDTO dremioDTO) {

        List<Map<String, Object>> res = new ArrayList<>();

        // 查询列
        List<String> queryColumns = new ArrayList<>();
        // 查询表
        List<String> queryTables = new ArrayList<>();
        // 关联关系
        List<Map<String, Object>> joinKeys;
        // 权限校验
        List<Map<String, Object>> authFilter = null;

        // 查询表及表字段
        List<RawTableAttr> attrs = rawTableAttrService.listByIds(param.getTableAttrIds());
        for (int i = 0; i < attrs.size(); i++) {
            // 获取所在表
            RawTable rawTable = rawTableService.getById(attrs.get(i).getRawTableId());
            queryColumns.add(rawTable.getCode() + StrUtil.DOT + attrs.get(i).getCode() + " AS " + CharUtil.DOUBLE_QUOTES + rawTable.getCode() + SqlBuilderUtil.DOLLAR + attrs.get(i).getCode() + CharUtil.DOUBLE_QUOTES);
            String queryTable = getQueryTable(attrs.get(i).getRawTableId());
            if (StringUtils.isNotBlank(queryTable) && !queryTables.contains(queryTable)) {
                queryTables.add(queryTable);
            }
        }

        // 查询表字段关联关系
        List<RawForeignKey> foreignKeys = foreignKeyService.listByRawId(raw.getId());
        // 添加未在查询列但包含外键关系的表
        for (int j = 0; j < foreignKeys.size(); j++) {
            String queryTable = getQueryTable(foreignKeys.get(j).getRawTableId());
            if (StringUtils.isNotBlank(queryTable) && !queryTables.contains(queryTable)) {
                queryTables.add(queryTable);
            }
            String unionQueryTable = getQueryTable(foreignKeys.get(j).getUnionRawTableId());
            if (StringUtils.isNotBlank(unionQueryTable) && !queryTables.contains(unionQueryTable)) {
                queryTables.add(unionQueryTable);
            }
        }
        joinKeys = buildForeignFilter(foreignKeys);

        // 管理员不参与权限校验
        if (!user.getIfBackend() && raw.getIfAuth()) {
            // 查询权限信息
            List<RawAuth> rawAuthList = rawAuthService.getListByRawId(raw.getId());
            // 无权限信息直接返回空数据
            if (rawAuthList.isEmpty()) {
                return res;
            }
            authFilter = buildAuthFilter(rawAuthList, user);
        }

        // 组装SQL
        String sql = SqlBuilderUtil.buildSql(false, queryColumns, queryTables, joinKeys, param.getFilters(), authFilter, param.getOrders(), "LIMIT 1");

        // 查询数据
        List<Map<String, Object>> data = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, sql));
        if (null != CheckQueryDataResponse.getErrorValues(data)) {
            throw new QueryFailedException(CheckQueryDataResponse.getErrorValues(data));
        }
        for (Map<String, Object> map : data) {
            for (String key : map.keySet()) {
                String attrCode = key.substring(key.lastIndexOf(StrUtil.DOT) + 1);
                RawTableAttr attr = rawTableAttrService.getByCodeAndAttrIds(user.getCompanyId(), attrCode, param.getTableAttrIds());
                Map<String, Object> resMap = new HashMap<>();
                resMap.put("id", attr.getId());
                resMap.put("code", key);
                resMap.put("name", attr.getName());
                resMap.put("value", map.get(key));
                res.add(resMap);
            }
        }
        return res;
    }

    /**
     * 根据名称完全匹配台账
     *
     * @param companyId 企业ID
     * @param name      台账名称
     * @return 台账
     */
    public Raw getOneByName(@NotNull String companyId, @NotNull String name) {
        QueryWrapper<Raw> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("if_deleted", false)
                .eq("name", name)
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 根据企业获取台账名称
     *
     * @param companyId 企业ID
     * @return 台账名称
     */
    public List<Raw> getListNameByCompanyId(@NotNull String companyId) {
        QueryWrapper<Raw> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("if_deleted", false);
        return super.list(wrapper);
    }

    /**
     * 数据聚合
     *
     * @param param     参数
     * @param raw       台账
     * @param plate     板块
     * @param dremioDTO 数据源
     * @return 统计数据
     */
    public Object calcMetric(QueryDataVo.QueryRequest param, Raw raw, Plate plate, CompanyDremioDTO dremioDTO) {
        CurrentUserUtil.CurrentUser user = CurrentUserUtil.get();

        // 查询列
        List<String> queryColumns = new ArrayList<>();
        // 查询表
        List<String> queryTables = new ArrayList<>();
        // 关联关系
        List<Map<String, Object>> joinKeys;
        // 权限校验
        List<Map<String, Object>> authFilter = null;

        // 查询表及表字段
        List<RawTableAttr> attrs = rawTableAttrService.listByIds(param.getTableAttrIds());
        for (int i = 0; i < attrs.size(); i++) {
            // 获取所在表
            RawTable rawTable = rawTableService.getById(attrs.get(i).getRawTableId());
            queryColumns.add(rawTable.getCode() + StrUtil.DOT + attrs.get(i).getCode());
            String queryTable = getQueryTable(attrs.get(i).getRawTableId());
            if (StringUtils.isNotBlank(queryTable) && !queryTables.contains(queryTable)) {
                queryTables.add(queryTable);
            }
        }

        // 查询表字段关联关系
        List<RawForeignKey> foreignKeys = foreignKeyService.listByRawId(raw.getId());
        // 添加未在查询列但包含外键关系的表
        for (int j = 0; j < foreignKeys.size(); j++) {
            String queryTable = getQueryTable(foreignKeys.get(j).getRawTableId());
            if (StringUtils.isNotBlank(queryTable) && !queryTables.contains(queryTable)) {
                queryTables.add(queryTable);
            }
            String unionQueryTable = getQueryTable(foreignKeys.get(j).getUnionRawTableId());
            if (StringUtils.isNotBlank(unionQueryTable) && !queryTables.contains(unionQueryTable)) {
                queryTables.add(unionQueryTable);
            }
        }
        joinKeys = buildForeignFilter(foreignKeys);

        // 管理员不参与权限校验
        if (!user.getIfBackend() && raw.getIfAuth()) {
            // 查询权限信息
            List<RawAuth> rawAuthList = rawAuthService.getListByRawId(raw.getId());
            // 无权限信息直接返回空数据
            if (rawAuthList.isEmpty()) {
                return "";
            }
            authFilter = buildAuthFilter(rawAuthList, user);
        }

        // 组装SQL 默认使用求和
        String sql = SqlBuilderUtil.buildCalcSql(StringUtils.isBlank(param.getCalcFunc()) ? SqlBuilderVo.SUM : param.getCalcFunc(), StringUtil.listToString(queryColumns, StrUtil.SPACE), queryTables, joinKeys, param.getFilters(), authFilter, param.getOrders(), null);

        // 查询数据
        List<Map<String, Object>> res = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, sql));
        return res.get(0).get("total") == null ? 0 : res.get(0).get("total");
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean ifAuthedRaw(String id, Boolean ifAuth) {
        Raw dws = super.getById(id);
        if (null == dws) {
            return false;
        }
        UpdateWrapper<Raw> wrapper = new UpdateWrapper<>();
        wrapper.eq("id", id);
        wrapper.set("if_auth", ifAuth);
        return super.update(wrapper);
    }
}