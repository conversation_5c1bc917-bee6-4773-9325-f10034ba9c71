package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ims.dao.DwsBasicAuthDao;
import com.taiyi.shuduoduo.ims.entity.DwsBasicAuth;
import com.taiyi.shuduoduo.ims.entity.DwsDataAuth;
import com.taiyi.shuduoduo.ims.vo.DwsAuthVo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DwsBasicAuthService extends CommonMysqlService<DwsBasicAuthDao, DwsBasicAuth> {
    @Override
    public Class<DwsBasicAuth> getEntityClass() {
        return DwsBasicAuth.class;
    }

    public DwsBasicAuth getByDwsId(String dwsId) {
        QueryWrapper<DwsBasicAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId).orderByDesc("create_time").last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public List<DwsBasicAuth> getAuthListByDwsId(String dwsId) {
        QueryWrapper<DwsBasicAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId).orderByDesc("create_time");
        return super.list(wrapper);
    }
}