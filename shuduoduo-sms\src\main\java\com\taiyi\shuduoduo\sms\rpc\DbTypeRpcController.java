package com.taiyi.shuduoduo.sms.rpc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.sms.api.dto.DbTypeDTO;
import com.taiyi.shuduoduo.sms.entity.DbType;
import com.taiyi.shuduoduo.sms.service.DbTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 数据源类型
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rpc/dbType")
public class DbTypeRpcController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DbTypeService dbTypeService;

    /**
     * 根据类型获取数仓类型
     *
     * @param type 类型
     * @return DbTypeDTO
     */
    @GetMapping("/getByType")
    public DbTypeDTO getByType(@RequestParam("type") Integer type) {
        return BeanUtil.copy(dbTypeService.getOne(new QueryWrapper<DbType>().eq("type", type)), DbTypeDTO.class);
    }


}
