package com.taiyi.common.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class CurrentUserUtilTest {

    @Test
    void threadLocal() throws InterruptedException {

        Thread thread1 = new Thread(() -> {
            CurrentUserUtil.CurrentUser user = new CurrentUserUtil.CurrentUser();
            user.setOpenId("1");

            CurrentUserUtil.put(user);

            Assertions.assertTrue(user.getOpenId().equals(CurrentUserUtil.get().getOpenId()));

        });

        Thread thread2 = new Thread(() -> {

            CurrentUserUtil.CurrentUser user = new CurrentUserUtil.CurrentUser();
            user.setOpenId("2");

            CurrentUserUtil.put(user);

            Assertions.assertTrue(user.getOpenId().equals(CurrentUserUtil.get().getOpenId()));

        });

        thread1.start();
        thread2.start();

        thread1.join();
        thread2.join();
    }
}