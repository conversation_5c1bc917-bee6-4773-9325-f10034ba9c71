package com.taiyi.common.data.mysql.entity;


import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class CommonMySqlEntity implements Serializable {

    /**
     * 数据库主键 ID
     */
    public String id;

    /**
     * 版本号，乐观锁使用
     */
    public Long version;

    /**
     * 创建时间
     */
    public Date createTime;

    /**
     * 最后更新时间
     */
    public Date updateTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}