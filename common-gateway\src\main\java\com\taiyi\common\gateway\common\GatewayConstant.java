package com.taiyi.common.gateway.common;

/**
 * <AUTHOR>
 */
public interface GatewayConstant {

    /**
     * 无TOKEN 返回JSON
     */
    String RESPONSE_NO_TOKEN_JSON = "{\n" +
            "  \"code\": \"B200\",\n" +
            "  \"message\": \"用户访问权限异常\",\n" +
            "  \"tips\": \"Token不能为空\",\n" +
            "  \"data\": \"Token不能为空\"\n" +
            "}";

    /**
     * 无权限 返回JSON
     */
    String RESPONSE_NO_PERMISSION_JSON = "{\n" +
            "  \"code\": \"B200\",\n" +
            "  \"message\": \"用户访问权限异常\",\n" +
            "  \"tips\": \"用户权限不足\",\n" +
            "  \"data\": \"用户权限不足\"\n" +
            "}";

    /**
     * Token已过期 返回JSON
     */
    String RESPONSE_INVALID_TOKEN_JSON = "{\n" +
            "  \"code\": \"B200\",\n" +
            "  \"message\": \"用户访问权限异常\",\n" +
            "  \"tips\": \"Token 无效\",\n" +
            "  \"data\": \"Token 无效\"\n" +
            "}";


    /**
     * 服务端异常 提示信息
     */
    String SERVER_ERROR = "系统服务异常";


}
