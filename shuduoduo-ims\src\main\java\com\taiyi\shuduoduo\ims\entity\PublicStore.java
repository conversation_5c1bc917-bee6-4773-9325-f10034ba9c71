package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_public_store")
public class PublicStore extends CommonMySqlEntity {

    private String baseId;

    /**
     * 项目ID
     */
    private String projectId;

    private String companyId;

    /**
     * 项目前缀
     */
    private String projectPrefix;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

    private Boolean ifPublic;

}