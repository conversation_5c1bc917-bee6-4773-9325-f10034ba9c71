package com.taiyi.common.connector.service;

import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@AutoConfigureMockMvc
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DbQueryServiceTest {

    @Autowired
    DbQueryService dbQueryService;

    @Test
    void testConnect() {
    }

    @Test
    void connect() {
    }

    @Test
    void getTables() {
    }

    @Test
    void getColumns() {
    }

    @Test
    void getColumnsAndType() {
        DbDto.DbQuery dbDto = new DbDto.DbQuery();
        dbDto.setHost("pgm-bp126pxhjsf986tj3o.pg.rds.aliyuncs.com");
        dbDto.setPort(1921);
        dbDto.setDatabase("shuduoduo_data");
        dbDto.setSchema("test");
        dbDto.setType("POSTGRE");
        dbDto.setUsername("shujiajia");
        dbDto.setPassword("shujiajia1999!");
        dbDto.setTableName("dws_zuixin_fangyuan");
//        List<Map<String, Object>> columnsAndType = dbQueryService.getColumnsAndType(dbDto);
//        assertNotNull(columnsAndType);
        for (int i = 0; i < 21; i++) {
            List<Map<String, Object>> columnsAndType1 = dbQueryService.getColumnsAndType(dbDto);
            assertNotNull(columnsAndType1);
        }
    }

    @Test
    void query() {
    }

    @Test
    void queryBySql() {
        DbDto.DbQueryBySql dbDto = new DbDto.DbQueryBySql();
        dbDto.setHost("pgm-bp126pxhjsf986tj3o.pg.rds.aliyuncs.com");
        dbDto.setPort(1921);
        dbDto.setDatabase("shuduoduo_data");
        dbDto.setSchema("test");
        dbDto.setType("POSTGRE");
        dbDto.setUsername("shujiajia");
        dbDto.setPassword("shujiajia1999!");
        dbDto.setSql("SELECT\n" +
                "\tdim_chengshi_gongsi.jituan_mingcheng AS jituan_mingcheng0,\n" +
                "\tdim_chengshi_gongsi.jituan_bianma AS jituan_bianma1,\n" +
                "\tdim_chengshi_gongsi.chengshi_mingcheng AS chengshi_mingcheng2,\n" +
                "\tdim_chengshi_gongsi.chengshi_bianma AS chengshi_bianma3,\n" +
                "\tdws_yue_chengshi0.kucun_jine AS kucun_jine4 \n" +
                "FROM\n" +
                "\tdim_chengshi_gongsi\n" +
                "\tLEFT JOIN (\n" +
                "\tSELECT\n" +
                "\t\tdws_yue_chengshi.kucun_jine,\n" +
                "\t\tdws_yue_chengshi.chengshi_bianma \n" +
                "\tFROM\n" +
                "\t\tdws_yue_chengshi \n" +
                "WHERE\n" +
                "\tdws_yue_chengshi.nianyue = '202201' ) " +
                "AS dws_yue_chengshi0 ON dim_chengshi_gongsi.chengshi_bianma=dws_yue_chengshi0.chengshi_bianma order by kucun_jine4");
        List<Map<String, Object>> maps = dbQueryService.queryBySql(dbDto);
        System.out.println(maps);
        List<String> header = new ArrayList<>();
        header.add("集团名称");
        header.add("集团编码");
        header.add("城市名称");
        header.add("城市编码");
        header.add("202201库存金额");
        // 创建一个excel文件
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(xssfWorkbook, 100, true);
        //构建Excel
        createExcel(sxssfWorkbook, "fileName", header, maps);
    }

    private void createExcel(SXSSFWorkbook sxssfWorkbook, String fileName, List<String> header, List<Map<String, Object>> mapList) {
        SXSSFSheet sheet = sxssfWorkbook.createSheet(fileName);
        Row row = null;
        //创建表头
        if (header.size() > 0) {
            row = sheet.createRow(0);
            for (int i = 0; i < header.size(); i++) {
                Cell cell = row.createCell(i); 
                cell.setCellValue(header.get(i));
                //表头自适应宽度
                sheet.setColumnWidth(i, header.get(i).getBytes().length * 256);
            }
        }
        //写入表内容
        if (mapList.size() > 0) {
            for (int i = 0; i < mapList.size(); i++) {
                row = sheet.createRow(i + 1);
                Map<String, Object> map = mapList.get(i);
                System.out.println(map);
                Object[] objects = map.values().toArray();
                for (int j = 0; j < objects.length; j++) {
                    Cell cell = row.createCell(j);
                    System.out.println(objects[j].toString());
                    cell.setCellValue(objects[j].toString());
                }

                /*Set<String> mapKey = map.keySet();
                Iterator<String> iterator = mapKey.iterator();
                int num = 0;
                while (iterator.hasNext()) {
                    Cell cell = row.createCell(num);
                    num++;
                    String key = iterator.next();
                    logger.info(key);
                    Object obj = map.get(key);
                    if (obj instanceof Date) {
                        SimpleDateFormat sdf = new SimpleDateFormat(DatePattern.NORM_DATETIME_MINUTE_PATTERN);
                        cell.setCellValue(sdf.format(obj));
                    } else if (obj instanceof Integer) {
                        cell.setCellValue((Integer) obj);
                    } else if (obj instanceof Double) {
                        cell.setCellValue((Double) obj);
                    } else {
                        cell.setCellValue((String) obj);
                    }
                }*/
            }
        }
    }

    @Test
    @Order(10)
    void getHttpUrl() {
//        String s = dbQueryService.getHttpUrl();
//        System.out.println(s);
    }

    @Test
    @Order(20)
    void getExportUrl() {
//        String s = dbQueryService.getExportUrl();
//        System.out.println(s);
    }


    @Test
    void sendMessage(){
    }
}