package com.taiyi.shuduoduo.common.connector.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class DbDto {

    private String engineHost;

    /**
     * 地址
     */
    @NotBlank
    private String host;

    /**
     * 端口
     */
    @NotBlank
    private Integer port;

    /**
     * 用户名
     */
    @NotBlank
    private String username;

    /**
     * 密码
     */
    @NotBlank
    private String password;

    /**
     * 数据库类型
     */
    @NotBlank
    private String type;

    /**
     * 数据库
     */
    @NotBlank
    private String database;

    /**
     * schema
     */
    private String schema;

    /**
     * 现在在common也定义了一个
     * 数仓类型
     */
    public enum DbType {
        /**
         * mysql
         */
        MYSQL, POSTGRE, ORACLE, INCEPTOR, DREMIO;

    }

    /**
     * 查询基类
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DbQueryBySql extends DbDto {

        @NotBlank
        private String sql;
    }


    /**
     * 查询基类
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DbQuery extends DbDto {

        /**
         * 表名
         */
        @NotBlank
        private String tableName;

        /**
         * 查询列
         */
        private List<String> columns;

        /**
         * 查询条件  eg:[{"":""},{"":""}]
         */
        private List<Map<String, Object>> queryParam;

        /**
         * 排序条件
         */
        private List<Map<String, Object>> sortParam;

    }

    /**
     * 导出参数
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DbExport extends DbDto {

        /**
         * 文件名
         */
        @NotBlank
        private String fileName;


        /**
         * 查询语句
         */
        @NotBlank
        private String sql;

        /**
         * 表头
         */
        @NotNull
        private Map<String, Object> header;

    }
}
