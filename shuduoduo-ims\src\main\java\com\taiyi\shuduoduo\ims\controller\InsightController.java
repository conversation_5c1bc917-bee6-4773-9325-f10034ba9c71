package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.Dimension;
import com.taiyi.shuduoduo.ims.entity.ImsCommonTips;
import com.taiyi.shuduoduo.ims.entity.Insight;
import com.taiyi.shuduoduo.ims.entity.InsightExecuteLog;
import com.taiyi.shuduoduo.ims.exceptions.QueryFailedException;
import com.taiyi.shuduoduo.ims.service.DimensionService;
import com.taiyi.shuduoduo.ims.service.InsightExecuteLogService;
import com.taiyi.shuduoduo.ims.service.InsightService;
import com.taiyi.shuduoduo.ims.util.SqlBuilderUtil;
import com.taiyi.shuduoduo.ims.vo.InsightVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyDremioRpcService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;

/**
 * 智能洞察
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/insight")
@Validated
public class InsightController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private InsightService insightService;

    @Autowired
    private InsightExecuteLogService insightExecuteLogService;

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private CompanyDremioRpcService companyDremioRpcService;

    /**
     * 新增数据
     *
     * @param t InsightVo.InsertRequest
     * @return 洞察ID
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated InsightVo.InsertRequest t) {
        if (t.getPath().size() != 4) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR);
        }
        if (insightService.getDremioInfo(CurrentUserUtil.get().getCompanyId()) == null) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_ENGINE_NULL);
        }
        String id;
        try {
            t.setCompanyId(CurrentUserUtil.get().getCompanyId());
            t.setCreatedBy(CurrentUserUtil.get().getId());
            id = insightService.saveReturnId(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        return ResponseVo.response(MessageCode.SUCCESS, null, id);
    }

    /**
     * 根据ID查询数据
     *
     * @param id 洞察ID
     * @return T
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, insightService.getDetailById(id));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 校验 flowId 唯一性
     *
     * @param flowId flowId
     * @return T
     */
    @GetMapping("/check")
    public ResponseEntity<ResponseVo.ResponseBean> checkFlowId(@RequestParam(value = "id", required = false) String id, @RequestParam("flowId") String flowId, @RequestParam("plateId") String plateId) {
        try {
            boolean t = insightService.checkFlowId(id, plateId, flowId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 删除洞察数据
     *
     * @param id 洞察ID
     * @return bool
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = insightService.logicDeleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 分页查询
     *
     * @param pageVo 分页参数
     * @return T
     */
    @PostMapping("/page")
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody(required = false) InsightVo.PageVo pageVo) {
        try {
            PageResult page = insightService.selectPage(pageVo);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 更新部分字段信息
     *
     * @param t
     * @return
     */
    @PatchMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> patch(@PathVariable String id, @RequestBody @Validated Insight t) {
        boolean f;
        try {
            f = insightService.updateById(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 新增执行日志数据
     *
     * @param t InsightVo.InsertRequest
     * @return 洞察ID
     */
    @PostMapping("/executions/log")
    public ResponseEntity<ResponseVo.ResponseBean> saveExecutionLog(@RequestBody @Validated InsightVo.InsertInsightExecuteLogVo t) {
        boolean f;
        InsightExecuteLog executeLog = BeanUtil.copy(t, InsightExecuteLog.class);
        try {
            executeLog.setCompanyId(CurrentUserUtil.get().getCompanyId());
            executeLog.setExecUser(CurrentUserUtil.get().getId());
            f = insightExecuteLogService.save(executeLog);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        }
        return ResponseVo.response(MessageCode.REQUEST_ERROR);
    }

    /**
     * 根据日志ID查询日志数据
     *
     * @param id 日志ID
     * @return T
     */
    @GetMapping("/executions/log/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getLogByLogId(@PathVariable("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, insightExecuteLogService.getById(id));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 根据洞察ID查询日志数据
     *
     * @param insightId 洞察ID
     * @return T
     */
    @GetMapping("/executions/log/list")
    public ResponseEntity<ResponseVo.ResponseBean> getLogListByInsightId(@RequestParam("insightId") String insightId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, insightExecuteLogService.getLogListByInsightId(insightId));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 根据执行ID查询日志数据
     *
     * @param executionId 执行ID
     * @return T
     */
    @GetMapping("/executions/log/execute/{executionId}")
    public ResponseEntity<ResponseVo.ResponseBean> getLogByExecutionId(@PathVariable("executionId") String executionId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, insightExecuteLogService.getByExecutionId(executionId));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 输出表节点-新增维度/事实表
     *
     * @param addDimension 维度类
     * @return ResponseEntity<ResponseVo.ResponseBean>
     */
    @PostMapping("/outputs")
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated InsightVo.AddDimension addDimension) {
        //检查重名
        Dimension dimension = BeanUtil.copy(addDimension, Dimension.class);
        if (dimensionService.isNameDuplicate(dimension, dimension.getName())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.NAME_DUPLICATE);
        }

        if (dimensionService.isCodeDuplicate(dimension, dimension.getCode())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.CODE_DUPLICATE);
        }

        if (dimensionService.isCodeLegal(dimension.getCode())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ImsCommonTips.CODE_LEGAL);
        }

        if (null == addDimension.getAttrList()) {
            addDimension.setAttrList(new ArrayList());
        }

        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, dimensionService.insertDimensionByInsight(addDimension));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 输出表节点-新增维度/事实表
     *
     * @param addDimension 维度类
     * @return ResponseEntity<ResponseVo.ResponseBean>
     */
    @PutMapping("/outputs")
    public ResponseEntity<ResponseVo.ResponseBean> edit(@RequestBody @Validated InsightVo.AddDimension addDimension) {
        //检查重名
        Dimension dimension = BeanUtil.copy(addDimension, Dimension.class);
        if (dimensionService.isNameDuplicate(dimension, dimension.getName())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.NAME_DUPLICATE);
        }

        if (dimensionService.isCodeDuplicate(dimension, dimension.getCode())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.CODE_DUPLICATE);
        }

        if (dimensionService.isCodeLegal(dimension.getCode())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ImsCommonTips.CODE_LEGAL);
        }

        if (null == addDimension.getAttrList()) {
            addDimension.setAttrList(new ArrayList());
        }

        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, dimensionService.editDimensionByInsight(addDimension));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 删除维度/事实表数据
     *
     * @param id 维度/事实表ID
     * @return bool
     */
    @DeleteMapping("/outputs/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteDimensionById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = dimensionService.logicDeleteDimensionByInsight(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取SQL结果
     *
     * @param param 查询SQL
     * @return T
     */
    @PostMapping("/outputs/query")
    public ResponseEntity<ResponseVo.ResponseBean> getQueryData(@RequestBody InsightVo.SqlParam param) {
        if (SqlBuilderUtil.containsSqlInjection(param.getSql())) {
            return ResponseVo.response(MessageCode.SUCCESS, "SQL中包含非法字符", null);
        }
        // 获取数据源信息
        CompanyDremioDTO companyDremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == companyDremioDTO) {
            return ResponseVo.response(MessageCode.SUCCESS, TipsConstant.DATA_SOURCE_NOT_FOUND, null);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, insightService.getQueryData(param.getSql(), param.getPlateCode(), companyDremioDTO));
        } catch (QueryFailedException e) {
            return ResponseVo.response(MessageCode.SUCCESS, e.getMessage(), null);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.SUCCESS, TipsConstant.TOPIC_NOT_FOUND, null);
        }
    }
}
