package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * 台账权限表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw_auth")
public class RawAuth extends CommonMySqlEntity {
    /**
     * 权限表ID
     */
    private String authTableId;

    private Boolean ifDeleted;

    /**
     * 台账ID
     */
    private String rawId;

    /**
     * 状态 0、未开启1、开启
     */
    private Boolean status;

    /** AND,且，两个条件都满足 */
    /**
     * OR,或，满足多个条件的一个即可
     */
    private String operator;

}