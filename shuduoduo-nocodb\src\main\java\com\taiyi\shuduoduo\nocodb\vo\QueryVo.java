package com.taiyi.shuduoduo.nocodb.vo;

import com.taiyi.shuduoduo.nocodb.entity.OpEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

public class QueryVo {

    @Data
    public static class QueryParam{

        /**
         * 表名
         */
        @NotBlank
        private String tableName;

        /**
         * 列名
         */
        @NotEmpty
        private List<String> columnName;

        private List<Filter> filters;
    }

    @Data
    public static class Filter{

        private String column;

        private OpEnum op;

        private Object value;
    }
}
