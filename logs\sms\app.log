2025-07-19 17:16:51.212 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 17:16:51.216 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:16:51.219 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:16:51.220 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:16:51.221 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:16:51.221 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:16:51.221 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:16:52.770 [main] INFO  c.t.s.sms.ShuduoduoSmsApplication - The following profiles are active: local
2025-07-19 17:16:53.621 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-19 17:16:53.624 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 17:16:53.652 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15ms. Found 0 Redis repository interfaces.
2025-07-19 17:16:53.710 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 17:16:53.811 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=3f9157f3-1c8e-39ac-9208-8743f303a50b
2025-07-19 17:16:53.829 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 17:16:53.830 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:16:53.830 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 17:16:53.830 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 17:16:53.831 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:16:53.831 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:16:53.831 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:16:53.831 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:16:53.831 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:16:53.832 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:16:53.832 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:16:53.832 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:16:53.832 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:16:54.215 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 17:16:54.221 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 17:16:54.223 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 17:16:54.244 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:16:54.263 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$20e030ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:16:54.292 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:16:54.373 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:16:54.501 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:16:56.132 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8860 (http)
2025-07-19 17:16:56.152 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 17:16:56.153 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.37]
2025-07-19 17:16:56.433 [main] INFO  o.a.c.c.C.[.[.[/shuduoduo/sms] - Initializing Spring embedded WebApplicationContext
2025-07-19 17:16:56.434 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3647 ms
2025-07-19 17:16:56.683 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 17:16:56.880 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join properties config complete
2025-07-19 17:16:57.004 [main] WARN  com.zaxxer.hikari.HikariConfig - mysql - idleTimeout has been set but has no effect because the pool is operating as a fixed size pool.
2025-07-19 17:16:57.007 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Starting...
2025-07-19 17:16:57.445 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Start completed.
2025-07-19 17:16:57.449 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Starting...
2025-07-19 17:16:57.541 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Start completed.
2025-07-19 17:16:57.542 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sqlite] success
2025-07-19 17:16:57.542 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [mysql] success
2025-07-19 17:16:57.543 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [mysql]
2025-07-19 17:16:57.642 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join SqlInjector init
2025-07-19 17:16:59.398 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 17:16:59.398 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 17:16:59.406 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 17:16:59.406 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 17:16:59.818 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-07-19 17:16:59.905 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-19 17:17:01.063 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 17:17:02.934 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 19 endpoint(s) beneath base path '/actuator'
2025-07-19 17:17:02.997 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8860 (http) with context path '/shuduoduo/sms'
2025-07-19 17:17:02.998 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 17:17:02.998 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 17:17:02.999 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 17:17:02.999 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 17:17:02.999 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 17:17:02.999 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application-local.yml] refreshed
2025-07-19 17:17:02.999 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.properties] refreshed
2025-07-19 17:17:02.999 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.yml] refreshed
2025-07-19 17:17:02.999 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudDefaultProperties refreshed
2025-07-19 17:17:02.999 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source cachedrandom refreshed
2025-07-19 17:17:02.999 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:17:02.999 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 17:17:03.000 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-19 17:17:03.000 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
2025-07-19 17:17:03.037 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP shuduoduo-sms *************:8860 register finished
2025-07-19 17:17:03.812 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 17:17:03.812 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 17:17:03.812 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 17:17:03.813 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 17:17:03.813 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 17:17:03.813 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:03.813 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:17:03.813 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:17:03.813 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:17:03.819 [main] INFO  c.t.c.l.NacosConfigRefreshListener - [配置监听] Nacos配置动态监听器已加载
2025-07-19 17:17:03.828 [main] INFO  c.t.s.sms.ShuduoduoSmsApplication - Started ShuduoduoSmsApplication in 14.553 seconds (JVM running for 15.228)
2025-07-19 17:46:55.373 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 17:46:55.392 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 17:46:55.397 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-19 17:46:55.398 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-19 17:46:55.400 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-19 17:46:55.400 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown initiated...
2025-07-19 17:46:55.402 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown completed.
2025-07-19 17:46:55.404 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown initiated...
2025-07-19 17:46:55.406 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown completed.
2025-07-19 17:46:55.407 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-19 21:32:42.557 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:32:42.566 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:32:42.571 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:42.571 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:42.572 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:32:42.573 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:32:42.574 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:45.121 [main] INFO  c.t.s.sms.ShuduoduoSmsApplication - The following profiles are active: local
2025-07-19 21:32:46.651 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-19 21:32:46.656 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 21:32:46.707 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30ms. Found 0 Redis repository interfaces.
2025-07-19 21:32:46.809 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 21:32:46.972 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=3f9157f3-1c8e-39ac-9208-8743f303a50b
2025-07-19 21:32:47.001 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:32:47.002 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:32:47.002 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:32:47.003 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:32:47.003 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:47.004 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:32:47.004 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:32:47.004 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:47.005 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:47.005 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:47.005 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:47.006 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:47.006 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:32:47.596 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 21:32:47.603 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 21:32:47.606 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 21:32:47.635 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:32:47.661 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$cadc4e35] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:32:47.691 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:32:47.785 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:32:47.927 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:32:49.543 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8860 (http)
2025-07-19 21:32:49.562 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 21:32:49.563 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.37]
2025-07-19 21:32:49.887 [main] INFO  o.a.c.c.C.[.[.[/shuduoduo/sms] - Initializing Spring embedded WebApplicationContext
2025-07-19 21:32:49.888 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4744 ms
2025-07-19 21:32:50.180 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 21:32:50.410 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join properties config complete
2025-07-19 21:32:50.575 [main] WARN  com.zaxxer.hikari.HikariConfig - mysql - idleTimeout has been set but has no effect because the pool is operating as a fixed size pool.
2025-07-19 21:32:50.580 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Starting...
2025-07-19 21:32:51.104 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Start completed.
2025-07-19 21:32:51.110 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Starting...
2025-07-19 21:32:51.201 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Start completed.
2025-07-19 21:32:51.201 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sqlite] success
2025-07-19 21:32:51.201 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [mysql] success
2025-07-19 21:32:51.202 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [mysql]
2025-07-19 21:32:51.310 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join SqlInjector init
2025-07-19 21:32:53.486 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 21:32:53.487 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 21:32:53.497 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 21:32:53.498 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 21:32:53.921 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-07-19 21:32:54.012 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-19 21:32:55.128 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 21:32:56.933 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 19 endpoint(s) beneath base path '/actuator'
2025-07-19 21:32:56.990 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8860 (http) with context path '/shuduoduo/sms'
2025-07-19 21:32:56.990 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 21:32:56.991 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 21:32:56.991 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 21:32:56.991 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 21:32:56.991 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 21:32:56.991 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application-local.yml] refreshed
2025-07-19 21:32:56.992 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.properties] refreshed
2025-07-19 21:32:56.992 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.yml] refreshed
2025-07-19 21:32:56.992 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudDefaultProperties refreshed
2025-07-19 21:32:56.992 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source cachedrandom refreshed
2025-07-19 21:32:56.992 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:32:56.992 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:32:56.992 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-19 21:32:56.992 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
2025-07-19 21:32:57.027 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP shuduoduo-sms *************:8860 register finished
2025-07-19 21:32:57.700 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 21:32:57.701 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 21:32:57.701 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 21:32:57.701 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 21:32:57.701 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 21:32:57.701 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:57.701 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:32:57.701 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:32:57.701 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:32:57.705 [main] INFO  c.t.c.l.NacosConfigRefreshListener - [配置监听] Nacos配置动态监听器已加载
2025-07-19 21:32:57.717 [main] INFO  c.t.s.sms.ShuduoduoSmsApplication - Started ShuduoduoSmsApplication in 18.187 seconds (JVM running for 19.153)
2025-07-19 21:33:29.326 [http-nio-8860-exec-1] INFO  o.a.c.c.C.[.[.[/shuduoduo/sms] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 21:33:29.327 [http-nio-8860-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 21:33:29.336 [http-nio-8860-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 9 ms
2025-07-19 21:33:29.448 [http-nio-8860-exec-1] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$28b08387 - request path: /shuduoduo/sms/rpc/log
2025-07-19 21:33:29.629 [http-nio-8860-exec-1] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - ==>  Preparing: INSERT INTO sms_log ( id, action_description, company_id, do_action, ip, parameters, path, version, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-19 21:33:29.646 [http-nio-8860-exec-1] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - ==> Parameters: 1abec74350f045cca26a8e424a226a9f(String), 管理员登录(String), 7172070a1c264c2bb6bb201e17bf8ee7(String), 用户登录(String), *************(String), [{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","name":"admin","pwd":"1578214679e95319fe07ad6352c68c40"}](String), /shuduoduo/ums/login/adminlogin(String), 1(Long), 2025-07-19 21:33:29.558(Timestamp), 2025-07-19 21:33:29.558(Timestamp)
2025-07-19 21:33:29.680 [http-nio-8860-exec-1] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - <==    Updates: 1
2025-07-19 21:35:03.033 [http-nio-8860-exec-3] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$28b08387 - request path: /shuduoduo/sms/rpc/log
2025-07-19 21:35:03.054 [http-nio-8860-exec-3] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - ==>  Preparing: INSERT INTO sms_log ( id, action_description, company_id, do_action, ip, parameters, path, version, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-19 21:35:03.054 [http-nio-8860-exec-3] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - ==> Parameters: 0ea48cf5349d4791be16358ddd561a6a(String), 管理员登录(String), 7172070a1c264c2bb6bb201e17bf8ee7(String), 用户登录(String), *************(String), [{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","name":"admin","pwd":"1801c3547cec87a6acd385ecc1fd2a88"}](String), /shuduoduo/ums/login/adminlogin(String), 1(Long), 2025-07-19 21:35:03.035(Timestamp), 2025-07-19 21:35:03.035(Timestamp)
2025-07-19 21:35:03.092 [http-nio-8860-exec-3] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - <==    Updates: 1
2025-07-19 21:36:53.615 [http-nio-8860-exec-5] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$28b08387 - request path: /shuduoduo/sms/rpc/log
2025-07-19 21:36:53.633 [http-nio-8860-exec-5] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - ==>  Preparing: INSERT INTO sms_log ( id, action_description, company_id, do_action, ip, parameters, path, version, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-19 21:36:53.634 [http-nio-8860-exec-5] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - ==> Parameters: 3fa16ef28eb8425f87090eaafd4b03e7(String), 管理员登录(String), 7172070a1c264c2bb6bb201e17bf8ee7(String), 用户登录(String), *************(String), [{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","name":"admin","pwd":"1801c3547cec87a6acd385ecc1fd2a88"}](String), /shuduoduo/ums/login/adminlogin(String), 1(Long), 2025-07-19 21:36:53.617(Timestamp), 2025-07-19 21:36:53.617(Timestamp)
2025-07-19 21:36:53.666 [http-nio-8860-exec-5] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - <==    Updates: 1
2025-07-19 21:46:01.467 [http-nio-8860-exec-7] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$28b08387 - request path: /shuduoduo/sms/rpc/log
2025-07-19 21:46:01.489 [http-nio-8860-exec-7] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - ==>  Preparing: INSERT INTO sms_log ( id, action_description, company_id, do_action, ip, parameters, path, version, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-19 21:46:01.489 [http-nio-8860-exec-7] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - ==> Parameters: 6dd9cc543b6e43d59fd10dc85c39950a(String), 管理员登录(String), 7172070a1c264c2bb6bb201e17bf8ee7(String), 用户登录(String), *************(String), [{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","name":"admin","pwd":"1801c3547cec87a6acd385ecc1fd2a88"}](String), /shuduoduo/ums/login/adminlogin(String), 1(Long), 2025-07-19 21:46:01.468(Timestamp), 2025-07-19 21:46:01.468(Timestamp)
2025-07-19 21:46:01.526 [http-nio-8860-exec-7] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - <==    Updates: 1
2025-07-19 21:46:51.888 [http-nio-8860-exec-8] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$28b08387 - request path: /shuduoduo/sms/rpc/log
2025-07-19 21:46:51.905 [http-nio-8860-exec-8] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - ==>  Preparing: INSERT INTO sms_log ( id, action_description, company_id, do_action, ip, parameters, path, version, create_time, update_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-19 21:46:51.906 [http-nio-8860-exec-8] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - ==> Parameters: 060aa33913e24cce99f8d8d0dba253f2(String), 管理员登录(String), 7172070a1c264c2bb6bb201e17bf8ee7(String), 用户登录(String), *************(String), [{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","name":"admin","pwd":"1801c3547cec87a6acd385ecc1fd2a88"}](String), /shuduoduo/ums/login/adminlogin(String), 1(Long), 2025-07-19 21:46:51.889(Timestamp), 2025-07-19 21:46:51.889(Timestamp)
2025-07-19 21:46:51.938 [http-nio-8860-exec-8] DEBUG c.t.shuduoduo.sms.dao.LogDao.insert - <==    Updates: 1
2025-07-19 21:50:13.498 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 21:50:13.514 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 21:50:13.517 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-19 21:50:13.519 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-19 21:50:13.520 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-19 21:50:13.521 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown initiated...
2025-07-19 21:50:13.526 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown completed.
2025-07-19 21:50:13.526 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown initiated...
2025-07-19 21:50:13.529 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown completed.
2025-07-19 21:50:13.529 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
