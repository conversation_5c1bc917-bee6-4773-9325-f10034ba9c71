package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.core.util.CharsetUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ums.entity.Company;
import com.taiyi.shuduoduo.ums.entity.Department;
import com.taiyi.shuduoduo.ums.service.CompanyService;
import com.taiyi.shuduoduo.ums.service.DepartmentService;
import com.taiyi.shuduoduo.ums.vo.CompanyDepartmentVo;
import com.taiyi.shuduoduo.ums.vo.CompanyVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公司
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/company")
@Validated
public class CompanyController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private CompanyService companyService;
    @Autowired
    private DepartmentService departmentService;

    /**
     * 公司/单位-新增公司
     *
     * @param company 公司实体
     * @return ResponseEntity
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated Company company) {
        boolean f;
        try {
            f = companyService.addCompany(company);
        } catch (Exception e) {
            logger.error("添加企业失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 公司/单位-修改公司
     *
     * @param id 公司id
     * @param t  公司实体
     * @return ResponseEntity
     */
    @PutMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateById(@PathVariable("id") String id, @RequestBody @Validated Company t) {
        boolean f;
        try {
            f = companyService.updateById(id, t);
        } catch (Exception e) {
            logger.error("修改企业失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 公司/单位-新增部门
     *
     * @param t 部门实体
     * @return ResponseEntity
     */
    @PostMapping("/department")
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated Department t) {
        boolean f;
        try {
            f = departmentService.save(t);
        } catch (Exception e) {
            logger.error("添加部门失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 公司/单位-修改部门
     *
     * @param id 部门id
     * @param t  部门实体
     * @return ResponseEntity
     */
    @PutMapping("/department/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateById(@PathVariable("id") String id, @RequestBody @Validated Department t) {
        boolean f;
        try {
            f = departmentService.updateById(id, t);
        } catch (Exception e) {
            logger.error("修改部门失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 公司/单位-启用/停用公司
     *
     * @param id 公司id
     * @param t  公司实体vo，停用字段
     * @return
     */
    @PostMapping("/lock/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> lock(@PathVariable("id") String id, @RequestBody @Validated CompanyDepartmentVo.Companylock t) {
        boolean f;
        try {
            f = companyService.updateById(id, BeanUtil.copy(t, Company.class));
        } catch (Exception e) {
            logger.error("启用/停用公司失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 公司/单位-详情
     *
     * @param id 单位id
     * @return ResponseEntity
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            CompanyVo.CompanyInfo t = BeanUtil.copy(companyService.getById(id), CompanyVo.CompanyInfo.class);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.error("查询公司详情失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 获取公司列表
     *
     * @param keyWord 模糊查询
     * @return 公司列表
     */
    @PostMapping("/list")
    public ResponseEntity<ResponseVo.ResponseBean> companyList(@RequestParam @Validated String keyWord) {
        try {
            List<CompanyVo.CompanyList> list = companyService.companyList(URLDecoder.decode(keyWord, CharsetUtil.CHARSET_UTF_8));
            return ResponseVo.response(MessageCode.SUCCESS, list);
        } catch (Exception e) {
            logger.error("获取公司列表失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取公司nocodb信息
     *
     * @param comId 公司id
     * @return nocodb信息
     */
    @GetMapping("/noco/info/{comId}")
    public ResponseEntity<ResponseVo.ResponseBean> getUserNocoInfo(@PathVariable("comId") String comId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, companyService.getUserNocoInfo(comId));
        } catch (Exception e) {
            logger.error("获取公司nocodb信息失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取公司nocodb表
     *
     * @param comId 公司id
     * @return nocodb表
     */
    @GetMapping("/noco/showTables/{comId}")
    public ResponseEntity<ResponseVo.ResponseBean> getAuthTableList(@PathVariable("comId") String comId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, companyService.getAuthTableList(comId));
        } catch (Exception e) {
            logger.error("获取公司nocodb表失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取公司nocodb表字段
     *
     * @param table 表名
     * @return nocodb表字段
     */
    @GetMapping("/noco/showColumns/{table}")
    public ResponseEntity<ResponseVo.ResponseBean> getColumns(@PathVariable("table") String table) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, companyService.getColumns(table));
        } catch (Exception e) {
            logger.error("获取公司nocodb表字段失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 公司-启用/停用指标权限
     *
     * @param id       公司id
     * @param ifAuthed 是否启用
     * @return Bool
     */
    @PostMapping("/dws/auth/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> dwsAuth(@PathVariable("id") String id, @RequestParam(value = "ifAuthed") Boolean ifAuthed) {
        boolean f;
        try {
            f = companyService.updateDwsAuthed(id, ifAuthed);
        } catch (Exception e) {
            logger.error("启用/停用指标权限失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
