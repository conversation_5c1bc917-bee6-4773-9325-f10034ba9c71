spring:
  datasource:
    dynamic:
      primary: mysql
      strict: false
      datasource:
        mysql:
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: root
          url: *********************************************************************************************************************************************************
          hikari:
            maximum-pool-size: 5
            minimum-idle: 5
            connection-test-query: SELECT 1
            max-lifetime: 60000
            idle-timeout: 60000
        sqlite:
          driver-class-name: org.sqlite.JDBC
          url: jdbc:sqlite:C:\Users\<USER>\Documents\Virtual Machines\服务器\public_dir\nocodb\noco.db?date_string_format=yyyy-MM-dd HH:mm:ss
          username:
          password:



# 打印mysql查询日志
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl