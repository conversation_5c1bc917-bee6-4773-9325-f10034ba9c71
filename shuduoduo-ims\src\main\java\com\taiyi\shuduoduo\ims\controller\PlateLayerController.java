package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.data.mysql.entity.MyQuery;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.entity.PlateLayer;
import com.taiyi.shuduoduo.ims.service.PlateLayerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/plate/layer")
@Validated
public class PlateLayerController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private PlateLayerService plateLayerService;

    /**
     * 新增数据
     *
     * @param t
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated PlateLayer t) {
        boolean f;
        try {
            f = plateLayerService.save(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            PlateLayer t = plateLayerService.getById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }


    @PutMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateById(@PathVariable("id") String id, @RequestBody @Validated PlateLayer t) {
        boolean f;
        try {
            f = plateLayerService.updateWithNull(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = plateLayerService.deleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @PostMapping("/page")
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody(required = false) MyQuery query) {
        try {
            PageResult page = plateLayerService.page(query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


    @PostMapping("/_exists")
    public ResponseEntity<ResponseVo.ResponseBean> exists(@RequestBody @Validated PlateLayer t) {
        boolean exists;
        try {
            exists = plateLayerService.exists(t);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (exists) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @PostMapping("{id}/_exists")
    public ResponseEntity<ResponseVo.ResponseBean> exists(@PathVariable String id, @RequestBody @Validated PlateLayer t) {
        try {
            plateLayerService.exists(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
            return ResponseVo.response(MessageCode.SUCCESS);
    }

   /**
     * 更新部分字段信息
     *
     * @param t
     * @return 
     */
    @PatchMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> patch(@PathVariable String id, @RequestBody @Validated PlateLayer t) {
        boolean f;
        try {
            f = plateLayerService.updateById(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @GetMapping("/plate/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getListByPlateId(@PathVariable("id") String id) {
        try {
            List<PlateLayer> t = plateLayerService.getListByPlateId(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }
}
