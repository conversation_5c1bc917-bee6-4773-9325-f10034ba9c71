package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.TopicShareDao;
import com.taiyi.shuduoduo.ims.entity.TopicShare;
import com.taiyi.shuduoduo.ims.vo.TopicShareVo;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.api.service.UserRpcService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TopicShareService extends CommonMysqlService<TopicShareDao, TopicShare> {
    @Override
    public Class<TopicShare> getEntityClass() {
        return TopicShare.class;
    }

    @Autowired
    private TopicShareDao topicShareDao;

    @Autowired
    private QueryTopicService queryTopicService;

    @Autowired
    private UserRpcService userRpcService;

    /**
     * 获取分享给我条数
     *
     * @param userId 我的ID
     * @param ifRead 是否已读
     * @return 总条数
     */
    public int getShareMeTotal(String userId, boolean ifRead) {
        QueryWrapper<TopicShare> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("to_user", userId).eq(!ifRead, "if_read", ifRead);
        return super.count(wrapper);
    }

    /**
     * 分页查询我的分享列表
     *
     * @param topicPageVo 查询参数
     * @param userId      用户ID
     * @return 主题列表
     */
    public PageResult<TopicShareVo.PageResultVo> pageList(TopicShareVo.TopicSharePageVo topicPageVo, String userId) {
        Page<TopicShareVo.PageResultVo> topicPage = new Page<>(topicPageVo.getPageNo(), topicPageVo.getPageSize());
        topicPage.setOptimizeCountSql(false);
        QueryWrapper<TopicShareVo.PageResultVo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("a.to_user", userId)
                .eq("a.if_deleted", false)
                .like(StringUtils.isNotBlank(topicPageVo.getKeyWord()), "b.subject", topicPageVo.getKeyWord());
        //排序
        if (StringUtils.isNotBlank(topicPageVo.getColumn()) && StringUtils.isNotBlank(topicPageVo.getSortType())) {
            queryWrapper.last("order by " + StrUtil.toSymbolCase(topicPageVo.getColumn(), '_') + " " + topicPageVo.getSortType());
        } else {
            queryWrapper.orderByDesc("a.create_time");
        }
        IPage<TopicShareVo.PageResultVo> pageVo = topicShareDao.page(topicPage, queryWrapper);
        for (TopicShareVo.PageResultVo resultVo : pageVo.getRecords()) {
            UserDTO userDTO = userRpcService.getUserInfoById(resultVo.getFromUser());
            if (null != userDTO){
                resultVo.setUsername(StringUtils.isNotBlank(userDTO.getRealName()) ? userDTO.getRealName() : userDTO.getThirdUserId());
                resultVo.setAvatarUri(userDTO.getAvatarUri());
            }
        }
        PageResult<TopicShareVo.PageResultVo> pageResult = new PageResult<>();
        pageResult.setPageNo(topicPage.getCurrent());
        pageResult.setTotal(pageVo.getTotal());
        pageResult.setPageSize(topicPage.getSize());
        pageResult.setList(pageVo.getRecords());
        return pageResult;
    }

    /**
     * 取消分享
     *
     * @param t        t
     * @param fromUser 分享人
     * @return bool
     */
    public boolean cancelShare(TopicShareVo.CancelShareParam t, String fromUser) {
        UpdateWrapper<TopicShare> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", true);
        wrapper.eq("from_user", fromUser).eq("topic_id", t.getTopicId()).in("to_user", t.getUsers());
        return super.update(wrapper);
    }

    /**
     * 查看/操作更新使用时间
     *
     * @param id id
     */
    public void updateLastTime(String id) {
        UpdateWrapper<TopicShare> wrapper = new UpdateWrapper<>();
        wrapper.set("last_time", new Date()).set("if_read", true).eq("id", id);
        super.update(wrapper);
    }

    /**
     * 获取详情
     *
     * @param id 分享ID
     * @return T
     */
    public TopicShareVo.ShareDetailResponse getShareDetailById(String id) {
        TopicShare topicShare = super.getById(id);
        TopicShareVo.ShareDetailResponse response = new TopicShareVo.ShareDetailResponse();
        response.setId(topicShare.getId());
        response.setTopicId(topicShare.getTopicId());
        response.setQueryTopic(queryTopicService.getById(topicShare.getTopicId()));
        return response;
    }

    /**
     * 获取分享用户列表
     *
     * @param topicId 自定义查询ID
     * @return users
     */
    public List<String> getUserListById(String topicId) {
        QueryWrapper<TopicShare> wrapper = new QueryWrapper<>();
        wrapper.eq("topic_id", topicId).eq("from_user", CurrentUserUtil.get().getId()).eq("if_deleted", false);
        List<TopicShare> shareList = super.list(wrapper);
        return shareList.stream().collect(ArrayList::new, (list, topicShare) -> list.add(topicShare.getToUser()), ArrayList::addAll);
    }

    /**
     * 批量删除
     *
     * @param ids ids
     * @return bool
     */
    public boolean logicDeleteBatchByIds(List<String> ids) {
        UpdateWrapper<TopicShare> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", true).in("id", ids);
        return super.update(wrapper);
    }

    /**
     * 获取最近使用的分享给我列表 前20条
     *
     * @param userId 用户ID
     * @return list
     */
    public List<TopicShareVo.RecentResponse> getShareRecentList(String userId) {
        List<TopicShareVo.RecentResponse> res = new ArrayList<>();
        QueryWrapper<TopicShare> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("to_user", userId)
                .eq("if_deleted", false)
                .isNotNull("last_time")
                .orderByDesc("last_time")
                .last("LIMIT 20");
        List<TopicShare> shareList = super.list(queryWrapper);
        for (TopicShare topicShare : shareList) {
            TopicShareVo.RecentResponse response = new TopicShareVo.RecentResponse();
            response.setId(topicShare.getId());
            response.setLastTime(topicShare.getLastTime());
            response.setType(2);
            response.setSubject(queryTopicService.getById(topicShare.getTopicId()).getSubject());
            UserDTO userDTO = userRpcService.getUserInfoById(topicShare.getFromUser());
            if (null != userDTO){
                response.setUsername(StringUtils.isNotBlank(userDTO.getRealName()) ? userDTO.getRealName() : userDTO.getThirdUserId());
                response.setAvatarUri(userDTO.getAvatarUri());
            }
            res.add(response);
        }
        return res;
    }

    /**
     * 获取分享给我列表
     *
     * @param userId 用户ID
     * @return list
     */
    public List<TopicShareVo.RecentResponse> getShareList(String userId) {
        List<TopicShareVo.RecentResponse> res = new ArrayList<>();
        QueryWrapper<TopicShare> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("to_user", userId)
                .eq("if_deleted", false)
                .orderByDesc("last_time");
        List<TopicShare> shareList = super.list(queryWrapper);
        for (TopicShare topicShare : shareList) {
            TopicShareVo.RecentResponse response = new TopicShareVo.RecentResponse();
            response.setId(topicShare.getId());
            response.setLastTime(topicShare.getLastTime());
            response.setType(2);
            response.setUserId(topicShare.getToUser());
            response.setSubject(queryTopicService.getById(topicShare.getTopicId()).getSubject());
            UserDTO userDTO = userRpcService.getUserInfoById(topicShare.getFromUser());
            if (null != userDTO){
                response.setUsername(StringUtils.isNotBlank(userDTO.getRealName()) ? userDTO.getRealName() : userDTO.getThirdUserId());
                response.setAvatarUri(userDTO.getAvatarUri());
            }
            res.add(response);
        }
        return res;
    }
}