package com.taiyi.shuduoduo.ums.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ums.entity.Role;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface RoleDao extends CommonMysqlMapper<Role> {


    /**
     * 分页查询
     *
     * @param page    分页参数
     * @param wrapper 过滤条件
     * @return IPage
     */
    @Select("select ur.id,ur.name from ums_role ur left join ums_company_role ucr on ur.id=ucr.role_id where ${ew.sqlSegment}")
    IPage<Role> selectPage(Page<Role> page, @Param("ew") Wrapper<Role> wrapper);

    /**
     * 判重
     *
     * @param wrapper 过滤条件
     * @return Role
     */
    @Override
    @Select("select ur.id,ur.name from ums_role ur left join ums_company_role ucr on ur.id=ucr.role_id where ${ew.sqlSegment}")
    Role selectOne(@Param("ew") Wrapper<Role> wrapper);
}