package com.taiyi.common.entity;

import lombok.Data;

/**
 * <AUTHOR>
 */
public class ServiceMessage<T> {
    public static Respond response(boolean result) {
        return new Respond<>(result);
    }

    public static Respond response(boolean result, String message) {
        return new Respond<>(result, message);
    }

    public Respond<T> response(boolean result, String message, T dataList) {
        return new Respond<>(result, message, dataList);
    }

    @Data
    public static class Respond<T> {
        private boolean result;
        private String message;
        private T data;

        public Respond(boolean result, String message, T data) {
            this.result = result;
            this.message = message;
            this.data = data;
        }

        public Respond(boolean result) {
            this.result = result;
        }

        public Respond(boolean result, String message) {
            this.result = result;
            this.message = message;
        }
    }
}
