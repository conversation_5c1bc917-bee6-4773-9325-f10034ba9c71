<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.2.RELEASE</version>
        <relativePath/>
    </parent>

    <groupId>com.taiyi</groupId>
    <artifactId>shuduoduo-v2</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>common</module>
        <module>common-auth</module>
        <module>common-data</module>
        <module>common-gateway</module>
        <module>common-connector</module>
        <module>shuduoduo-api</module>
        <module>shuduoduo-sms</module>
        <module>shuduoduo-ums</module>
        <module>shuduoduo-ims</module>
        <module>shuduoduo-nocodb</module>
        <!--<module>shuduoduo-fms</module>-->
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <shuduoduov2.version>0.0.1-SNAPSHOT</shuduoduov2.version>
        <spring-cloud.version>Hoxton.SR7</spring-cloud.version>
        <spring-cloud.alibaba.version>2.2.1.RELEASE</spring-cloud.alibaba.version>
        <java-jwt.version>3.10.3</java-jwt.version>
        <commons-lang3.version>3.8.1</commons-lang3.version>
        <hutool.version>5.4.1</hutool.version>
        <spring-boot-admin.version>2.3.0</spring-boot-admin.version>
        <logstash-logback-encoder.version>5.2</logstash-logback-encoder.version>
        <mybatis-plus-boot-starter.version>3.4.0</mybatis-plus-boot-starter.version>
        <oss.version>3.10.2</oss.version>
        <druid-starter>1.1.22</druid-starter>
        <poi.version>4.1.2</poi.version>
        <seata.version>1.3.0</seata.version>
        <docker-maven-plugin.version>1.2.2</docker-maven-plugin.version>
        <aliyun-java-sdk.version>4.5.3</aliyun-java-sdk.version>
        <netty.version>4.1.51.Final</netty.version>
        <dynamic.version>3.4.1</dynamic.version>
        <sqlite.version>3.27.2.1</sqlite.version>
        <jaspyt.version>3.0.5</jaspyt.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- spring cloud 依赖 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- spring cloud alibaba 依赖 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>central</id>
            <name>aliyun maven</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <layout>default</layout>
            <!-- 是否开启发布版构件下载 -->
            <releases>
                <enabled>true</enabled>
            </releases>
            <!-- 是否开启快照版构件下载 -->
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>shuduoduo-prod</id>
            <url>https://packages.aliyun.com/maven/repository/2158904-release-Lc3NI8/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>shuduoduo-dev</id>
            <url>https://packages.aliyun.com/maven/repository/2158904-snapshot-hFRREh/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.3</version>
                <configuration>
                    <generateBackupPoms>false</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
