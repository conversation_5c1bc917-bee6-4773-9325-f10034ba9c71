package com.taiyi.common.entity;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
public class ResponseVo {

    public static ResponseEntity<ResponseBean> response(MessageCode messageCode) {
        ResponseBean responseBean = new ResponseBean(messageCode);
        responseBean.setTips(messageCode.getMessage());
        return new ResponseEntity<>(responseBean, getHttpStatus(messageCode));
    }

    public static ResponseEntity<ResponseBean> response(MessageCode messageCode, String tips) {
        ResponseBean responseBean = new ResponseBean(messageCode);
        responseBean.setTips(tips);

        return new ResponseEntity<>(responseBean, getHttpStatus(messageCode));
    }

    public static ResponseEntity<ResponseBean> response(MessageCode messageCode, String tips, Object data) {
        ResponseBean responseBean = new ResponseBean(messageCode, data);
        responseBean.setTips(tips);
        return new ResponseEntity<>(responseBean, getHttpStatus(messageCode));
    }

    public static ResponseEntity<ResponseBean> response(MessageCode messageCode, Object data) {
        ResponseBean responseBean = new ResponseBean(messageCode, data);
        responseBean.setTips(messageCode.getMessage());
        return new ResponseEntity<>(responseBean, getHttpStatus(messageCode));
    }

    private static HttpStatus getHttpStatus(MessageCode messageCode) {
        switch (messageCode.getCode()) {
            case "A000":
                return HttpStatus.OK;

            case "B100":
            case "B300":
            case "B400":
            case "B500":
            case "B600":
            case "B700":
            case "B800":
                return HttpStatus.BAD_REQUEST;

            case "B200":
            case "B201":
                return HttpStatus.FORBIDDEN;

            case "C100":
            case "C200":
            case "C300":
                return HttpStatus.INTERNAL_SERVER_ERROR;

            default:
                return HttpStatus.NOT_FOUND;
        }

    }

    public static class ResponseBean {

        String code;
        String message;
        String tips;
        Object data;

        public ResponseBean(MessageCode messageCode, Object data) {
            this.code = messageCode.getCode();
            this.message = messageCode.getMessage();
            this.data = data;
        }

        public ResponseBean(MessageCode messageCode) {
            this.code = messageCode.getCode();
            this.message = messageCode.getMessage();
            this.data = new HashMap<>(1);
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }

        public String getTips() {
            return tips;
        }

        public void setTips(String tips) {
            this.tips = tips;
        }
    }
}
