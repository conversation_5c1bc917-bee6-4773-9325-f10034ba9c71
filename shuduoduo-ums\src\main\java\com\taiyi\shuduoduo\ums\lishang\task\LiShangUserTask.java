package com.taiyi.shuduoduo.ums.lishang.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.shuduoduo.ums.entity.Company;
import com.taiyi.shuduoduo.ums.entity.CompanyConstantsParam;
import com.taiyi.shuduoduo.ums.entity.User;
import com.taiyi.shuduoduo.ums.entity.UserCompany;
import com.taiyi.shuduoduo.ums.lishang.util.LiShang;
import com.taiyi.shuduoduo.ums.listener.TaskRunningSwitchListener;
import com.taiyi.shuduoduo.ums.service.CompanyService;
import com.taiyi.shuduoduo.ums.service.UserCompanyService;
import com.taiyi.shuduoduo.ums.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 同步丽尚组织架构
 *
 * <AUTHOR>
 */
@Component
@Async
public class LiShangUserTask {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private CompanyService companyService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserCompanyService userCompanyService;

    @Autowired
    TaskRunningSwitchListener taskRunningSwitchListener;

    /**
     * 分页参数
     */
    public static final Integer PAGENUM = 50;

    @Value("${spring.profiles.active}")
    private String active;

    @Value("${lishang.clientId:''}")
    private String clientId;

    @Value("${lishang.username:''}")
    private String username;

    @Value("${lishang.password:''}")
    private String password;

    @Value("${lishang.host:''}")
    private String host;


    /**
     * 定时任务  同步组织架构
     * cron 60分钟一次
     */
    @Scheduled(cron = "0 0/59 * * * ?")
    public void syncProdUserAndDept() {
        //判断当前环境，丽尚环境执行初始化
        if (!"lishang".equals(active)) {
            return;
        }
        if (!taskRunningSwitchListener.isTaskRunning()) {
            return;
        }
        long start = System.currentTimeMillis();
        logger.info("开始同步丽尚国潮组织架构:{}", DateUtil.now());
        //同步丽尚国潮组织架构
        Company company = companyService.getById(CompanyConstantsParam.COMPANY_LISHANG_ID);
        try {
            syncLiShangUser(company);
        } catch (Exception e) {
            logger.error("同步公司{}失败:{}", company.getName(), ExceptionUtil.stacktraceToString(e));
        }
        logger.info("同步丽尚国潮组织架构结束:{},总耗时:{}ms", DateUtil.now(), System.currentTimeMillis() - start);
    }

    /**
     * 定时任务  同步组织架构
     * cron 60分钟一次
     */
    @Scheduled(cron = "0 0/59 * * * ?")
    public void syncTestUserAndDept() {
        //判断当前环境，丽尚环境执行初始化
        if (!"lishangtest".equals(active)) {
            return;
        }
        if (!taskRunningSwitchListener.isTaskRunning()) {
            return;
        }
        long start = System.currentTimeMillis();
        logger.info("开始同步丽尚国潮组织架构:{}", DateUtil.now());
        //同步丽尚国潮组织架构
        Company company = companyService.getById(CompanyConstantsParam.COMPANY_LISHANG_TEST_ID);
        try {
            syncLiShangUser(company);
        } catch (Exception e) {
            logger.error("同步公司{}失败:{}", company.getName(), ExceptionUtil.stacktraceToString(e));
        }
        logger.info("同步丽尚国潮组织架构结束:{},总耗时:{}ms", DateUtil.now(), System.currentTimeMillis() - start);
    }

    static boolean exec_times = false;

    /**
     * 同步丽尚国潮组织架构信息
     *
     * @param company 公司信息
     */
    public void syncLiShangUser(Company company) {
        String startTime = null;
        if (exec_times) {
            startTime = DateUtil.format(DateUtil.yesterday(), "yyyy-MM-dd HH:mm:ss");
        }
        LiShang liShang = new LiShang(host, clientId, username, password);
        List<JSONObject> users = new ArrayList<>();
        for (int page = 1; ; page++) {
            List<JSONObject> objects = liShang.getUsers(page, PAGENUM, startTime);
            users.addAll(objects);
            if (objects.size() < PAGENUM) {
                break;
            }
        }
        //用户同步
        for (JSONObject userJson : users) {
            String openId = userJson.getString("id");
            String userId = userJson.getString("userid");
            String userName = userJson.getString("username");
            String mobile = userJson.getString("phone");
            String idCard = userJson.getString("idcard");
            boolean gender = "女".equals(userJson.getString("sex"));
            //  0 试用 1 正式 2 临时 3 试用延期 4 解聘 5 离职 6 退休 7 无效
            Integer status = userJson.getInteger("employstatus");
            // 设置 empStatus：1 在职，2 离职
            int empStatus = 2; // 默认为离职
            if (status != null && status < 4) {
                empStatus = 1; // 在职
            }
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("company_id", company.getId())
                    .eq("third_user_id", userJson.getString("userid"));
            User one = userService.getOne(wrapper);
            User user = new User();
            user.setCompanyId(company.getId());
            user.setMobile(mobile);
            user.setNickname(userName);
            user.setGender(gender);
            user.setRealName(userName);
            user.setOpenId(openId);
            user.setIdCard(idCard);
            user.setEmpStatus(empStatus);
            user.setThirdUserId(userId);
            if (one == null) {
                // 新增用户信息
                String uuid = IdUtil.simpleUUID();
                user.setId(uuid);
                user.setIfAdmin(false);
                user.setIfBackend(false);
                user.setIfLock(false);
                userService.save(user);
                logger.info("新增用户:{}", user.getRealName());
                //保存用户部门关联信息
                saveUserDeptInfo(company.getId(), uuid, CompanyConstantsParam.DEPARTMENT_LISHANG_ID);
            }else {
                // 更改用户信息
                userService.updateById(one.getId(), user);
                logger.info("更新用户:{}", one.getRealName());
            }
        }
        exec_times = true;
    }

    /**
     * 保存用户部门关联信息
     *
     * @param companyId 企业ID
     * @param userId    用户ID
     */
    public void saveUserDeptInfo(String companyId, String userId, String departmentId) {
        //用户部门关联表
        UserCompany userCompany = new UserCompany();
        userCompany.setUserId(userId);
        userCompany.setCompanyId(companyId);
        userCompany.setDepId(departmentId);
        //检查有无关联
        QueryWrapper<UserCompany> userCompanyQueryWrapper = new QueryWrapper<>();
        userCompanyQueryWrapper.setEntity(userCompany);
        UserCompany userCompanyServiceOne = userCompanyService.getOne(userCompanyQueryWrapper);
        if (userCompanyServiceOne == null) {
            userCompanyService.save(userCompany);
        }
    }

}
