package com.taiyi.common.util;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 强密码生成工具类
 *
 * <AUTHOR>
 */
public class StrongPasswordGenerator {

    // 密码字符集
    private static final String LOWER_CASE = "abcdefghijklmnopqrstuvwxyz";
    private static final String UPPER_CASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String DIGITS = "0123456789";
    private static final String SPECIAL_CHARS = "!@#$%^&*()-_=+[]{}|;:,.<>?/";

    // 合并所有字符集
    private static final String ALL_CHARS = LOWER_CASE + UPPER_CASE + DIGITS + SPECIAL_CHARS;

    // 安全随机数生成器
    private static final SecureRandom random = new SecureRandom();

    /**
     * 生成指定长度的强密码
     *
     * @param length 密码长度，建议至少 12 位
     * @return 强密码字符串
     */
    public static String generateStrongPassword(int length) {
        if (length < 8) {
            throw new IllegalArgumentException("密码长度必须大于等于 8");
        }

        StringBuilder password = new StringBuilder(length);

        // 确保密码中包含至少一个每种类型的字符
        password.append(randomChar(LOWER_CASE));     // 小写字母
        password.append(randomChar(UPPER_CASE));     // 大写字母
        password.append(randomChar(DIGITS));         // 数字
        password.append(randomChar(SPECIAL_CHARS));  // 特殊字符

        // 填充剩余字符
        for (int i = 4; i < length; i++) {
            password.append(randomChar(ALL_CHARS));
        }

        // 打乱顺序，避免前4位固定类型
        List<Character> chars = new ArrayList<>();
        for (char c : password.toString().toCharArray()) {
            chars.add(c);
        }
        Collections.shuffle(chars, random);

        StringBuilder shuffledPassword = new StringBuilder();
        for (char c : chars) {
            shuffledPassword.append(c);
        }

        return shuffledPassword.toString();
    }

    /**
     * 从指定字符集中随机选取一个字符
     */
    private static char randomChar(String charSet) {
        return charSet.charAt(random.nextInt(charSet.length()));
    }
}