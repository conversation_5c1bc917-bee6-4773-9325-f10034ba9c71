package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.FactForeignKeyDao;
import com.taiyi.shuduoduo.ims.entity.FactForeignKey;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FactForeignKeyService extends CommonMysqlService<FactForeignKeyDao, FactForeignKey> {
    @Override
    public Class<FactForeignKey> getEntityClass() {
        return FactForeignKey.class;
    }

    /**
     * 根据维度/事实表ID查询外键关联列表
     *
     * @param dimId 维度/事实表ID
     * @return 外键关联列表
     */
    public List<FactForeignKey> getListByDimIdAndType(String dimId, Integer type) {
        QueryWrapper<FactForeignKey> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("company_id", CurrentUserUtil.get().getCompanyId());
        if (type == 1) {
            //维度表
            wrapper.eq("dim_id", dimId);
        } else {
            // 事实表
            wrapper.eq("fact_id", dimId);
        }
        return super.list(wrapper);
    }
}