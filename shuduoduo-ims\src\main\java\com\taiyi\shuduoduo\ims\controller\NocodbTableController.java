package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.ImsCommonTips;
import com.taiyi.shuduoduo.ims.entity.NocodbTable;
import com.taiyi.shuduoduo.ims.service.NocodbTableService;
import com.taiyi.shuduoduo.ims.service.PublicStoreService;
import com.taiyi.shuduoduo.ims.vo.NocodbTableVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/nocodb/table")
@Validated
public class NocodbTableController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private NocodbTableService nocodbTableService;

    @Autowired
    private PublicStoreService publicStoreService;

    /**
     * 新增数据
     *
     * @param t
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated NocodbTableVo.EditRequest t) {
        NocodbTable nocodbTable = new NocodbTable();
        nocodbTable.setTableName(t.getName());
        nocodbTable.setProjectId(t.getProjectId());
        nocodbTable.setId(t.getId());
        if (nocodbTableService.exists(nocodbTable)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, ImsCommonTips.NAME_DUPLICATE);
        }
        boolean f;
        try {
            f = nocodbTableService.saveData(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            NocodbTable t = nocodbTableService.getById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }


    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = nocodbTableService.deleteData(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @PostMapping("/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody @Validated NocodbTableVo.NocodbTablePageVo pageVo) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, nocodbTableService.getList(pageVo));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


    @GetMapping("/refresh")
    public ResponseEntity<ResponseVo.ResponseBean> updateList(@RequestParam @Validated @NotBlank String projectId) {
        try {
            nocodbTableService.refresh(projectId);
            return ResponseVo.response(MessageCode.SUCCESS);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取nocodb信息
     *
     * @return T
     */
    @GetMapping("/info/{comId}")
    public ResponseEntity getUserNocoInfo(@PathVariable("comId") String companyId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, publicStoreService.getUserNocoInfo(companyId));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

}
