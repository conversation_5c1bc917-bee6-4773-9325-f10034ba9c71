package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.DimUserFilter;
import com.taiyi.shuduoduo.ims.service.DimUserFilterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 指标查询筛选条件
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/dim/user/filter")
@Validated
public class DimUserFilterController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DimUserFilterService dimUserFilterService;

    /**
     * 保存筛选条件
     *
     * @param t T
     * @return T
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated DimUserFilter t) {
        CurrentUserUtil.CurrentUser user = CurrentUserUtil.get();
        boolean f;
        try {
            t.setCompanyId(user.getCompanyId());
            f = dimUserFilterService.saveOrUpdate(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据维度ID 获取筛选条件
     *
     * @param dimId 维度ID
     * @return T
    @GetMapping("/{dimId}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("dimId") String dimId) {
        CurrentUserUtil.CurrentUser user = CurrentUserUtil.get();
        try {
            DimUserFilter t = dimUserFilterService.getFilterByDimId(user.getCompanyId(), user.getId(), dimId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }*/

    /**
     * 根据维度ID 获取筛选条件
     *
     * @param dimId 维度ID
     * @return T
     */
    @GetMapping("/{dimId}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("dimId") String dimId, @RequestParam("topicId") String topicId) {
        CurrentUserUtil.CurrentUser user = CurrentUserUtil.get();
        try {
            DimUserFilter t = dimUserFilterService.getFilterByDimId(user.getCompanyId(),  dimId, topicId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

}
