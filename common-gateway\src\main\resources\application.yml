server:
  port: 9999
spring:
  application:
    name: common-gateway
  main:
    allow-bean-definition-overriding: true
  # 选择开发环境或者生产环境
  profiles:
    active: local
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        - id: common-auth
          uri: lb://common-auth
          predicates:
            - Path=/common/auth/**

        - id: common-connector
          uri: lb://common-connector
          predicates:
            - Path=/common/connector/**

        - id: shuduoduo-sms
          uri: lb://shuduoduo-sms
          predicates:
            - Path=/shuduoduo/sms/**

        - id: shuduoduo-ums
          uri: lb://shuduoduo-ums
          predicates:
            - Path=/shuduoduo/ums/**

        - id: shuduoduo-ims
          uri: lb://shuduoduo-ims
          predicates:
            - Path=/shuduoduo/ims/**

        - id: shuduoduo-nocodb
          uri: lb://shuduoduo-nocodb
          predicates:
            - Path=/shuduoduo/nocodb/**

management:
  server:
    port: -1
  endpoints:
    web:
      exposure:
        include: '*'
        exclude: env,beans,nacos-discovery,heapdump,threaddump
      base-path: /shuduoduov2_monitor
  endpoint:
    health:
      show-details: always

logging:
  config: classpath:logback-spring.xml
