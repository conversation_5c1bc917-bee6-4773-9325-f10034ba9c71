package com.taiyi.shuduoduo.ums.vo;

import lombok.Data;

import java.util.List;

@Data
public class MenuVo {

    @Data
    public static class TreeList{

        private String id;

        /**
         * 菜单名称
         */
        private String name;

        /**
         * 排序
         */
        private Long orderBy;

        /**
         * 父级ID
         */
        private String pid;

        /**
         * 菜单路由
         */
        private String routeUrl;

        /**
         * 前端ID
         */
        private String webId;

        private Boolean disabled;

        /**
         * 子菜单
         */
        private List<MenuVo.TreeList> child;
    }

    @Data
    public static class SearchParam {

        private String id;

        /**
         * 1、部门 2、角色 3、用户
         */
        private Integer type;

    }
}
