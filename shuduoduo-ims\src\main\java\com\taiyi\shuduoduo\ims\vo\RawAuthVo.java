package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
public class RawAuthVo {

    @Data
    public static class PageVo extends CommonMySqlPageVo {

        /**
         * 台账ID
         */
        @NotBlank
        private String rawId;
    }

    @Data
    public static class PageResult {
        private String id;
        /**
         * 权限表ID
         */
        private String authTableId;
        private String authTableName;
        private boolean status;
        private String operator;
    }
}
