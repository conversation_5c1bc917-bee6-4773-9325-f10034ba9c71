package com.taiyi.shuduoduo.ums.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ums_department")
public class Department extends CommonMySqlEntity {
    /**
     * 企业id
     */
    private String umsCompanyId;

    /**
     * 父级id
     */
    private String pid;

    /**
     * 冗余字段(第三方企业id)
     */
    private String corpId;

    /**
     * 部门id(第三方部门id)
     */
    private String deptId;

    /**
     * 状态，0: 正常, 1: 停用
     */
    private Integer state;

    /**
     * 部门名称
     */
    private String name;

}