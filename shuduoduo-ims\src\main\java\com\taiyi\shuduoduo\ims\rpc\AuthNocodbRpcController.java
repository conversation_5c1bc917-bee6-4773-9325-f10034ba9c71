package com.taiyi.shuduoduo.ims.rpc;

import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ims.api.dto.AuthNocodbDTO;
import com.taiyi.shuduoduo.ims.entity.AuthNocodb;
import com.taiyi.shuduoduo.ims.service.AuthNocodbService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rpc/auth/nocodb")
public class AuthNocodbRpcController {
    @Autowired
    AuthNocodbService authNocodbService;

    /**
     * 根据板块ID获取维度列表
     *
     * @return 维度列表
     */
    @GetMapping("/getInfo")
    public AuthNocodbDTO getAuthNocodbInfoByCompanyId(@RequestParam("companyId") String companyId) {
        AuthNocodb authNocodb = authNocodbService.getAuthInfoByCompanyId(companyId);
        return BeanUtil.copy(authNocodb, AuthNocodbDTO.class);
    }

    /**
     * 根据板块ID获取维度列表
     *
     * @return 维度列表
     */
    @GetMapping("/insert/data")
    public Boolean InsertData(@RequestParam("projectName") String projectName, @RequestParam("tableName") String tableName, @RequestParam("body") String body) {
        return authNocodbService.insertData(projectName, tableName, body);
    }
}
