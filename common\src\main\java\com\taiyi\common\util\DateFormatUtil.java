package com.taiyi.common.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 日期工具类
 *
 * <AUTHOR>
 */
public class DateFormatUtil {

    public static String dateFormat(Date date) {
        String formatDate = "";
        String dateStr = date.toString();
        //去掉分隔符
        dateStr = dateStr
                // 去掉 "-"
                .replace(StrUtil.DASHED, StrUtil.EMPTY)
                // 去掉 "/"
                .replace(StrUtil.SLASH, StrUtil.EMPTY)
                // 去掉 " "
                .replace(StrUtil.SPACE, StrUtil.EMPTY)
                // 去掉 "."
                .replace(StrUtil.DOT, StrUtil.EMPTY)
                // 去掉 ":"
                .replace(StrUtil.COLON, StrUtil.EMPTY);
        int length = dateStr.length();
        if (length == 8) {
            //年月日
            formatDate = DateUtil.format(date, "yyyy-MM-dd");
        } else if (length == 12) {
            //年月日 时分
            formatDate = DateUtil.format(date, "yyyy-MM-dd HH:mm");
        } else if (length == 14) {
            //年月日 时分秒
            formatDate = DateUtil.format(date, "yyyy-MM-dd HH:mm:ss");
        } else if (length > 14) {
            //年月日 时分秒毫秒
            formatDate = DateUtil.format(date, "yyyy-MM-dd HH:mm:ss.SSS");
        } else {
            formatDate = date.toString();
        }
        return formatDate;
    }

    /**
     * 判断字符串是否为合法的日期格式
     *
     * @param dateStr 待判断的字符串
     * @return boolean
     */
    public static boolean isValidDate(String dateStr) {
        //判断结果 默认为true
        boolean judgeresult = true;
        //1、首先使用SimpleDateFormat初步进行判断，过滤掉注入 yyyy-01-32 或yyyy-00-0x等格式
        //此处可根据实际需求进行调整，如需判断yyyy/MM/dd格式将参数改掉即可
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            //增加强判断条件，否则 诸如2022-02-29也可判断出去
            format.setLenient(false);
            Date date = format.parse(dateStr);
            System.out.println(date);
        } catch (Exception e) {
            judgeresult = false;
        }
        //由于上述方法只能验证正常的日期格式，像诸如 0001-01-01、11-01-01，10001-01-01等无法校验，此处再添加校验年费是否合法
        String yearStr = dateStr.split("-")[0];
        if (yearStr.startsWith("0") || yearStr.length() != 4) {
            judgeresult = false;
        }
        return judgeresult;
    }
}
