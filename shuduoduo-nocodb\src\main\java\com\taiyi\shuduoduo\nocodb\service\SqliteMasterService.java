package com.taiyi.shuduoduo.nocodb.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.StringUtil;
import com.taiyi.shuduoduo.nocodb.entity.SqliteMaster;
import com.taiyi.shuduoduo.nocodb.vo.QueryVo;
import com.taiyi.shuduoduo.nocodb.dao.SqliteMasterDao;
import com.taiyi.shuduoduo.nocodb.entity.OpEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class SqliteMasterService extends CommonMysqlService<SqliteMasterDao, SqliteMaster> {

    @Autowired
    private SqliteMasterDao sqliteMasterDao;

    public List<SqliteMaster> showCompanyTableWithPrefix(String prefix) {
        QueryWrapper<SqliteMaster> wrapper = new QueryWrapper<>();
        wrapper.eq("type", "table");
        wrapper.likeRight("name", prefix);
        return sqliteMasterDao.showCompanyTableWithPrefix(wrapper);
    }

    @Override
    public Class<SqliteMaster> getEntityClass() {
        return SqliteMaster.class;
    }

    public List<String> showColumns(String table) {
        List<String> res = new ArrayList<>();
        List<Map<String, Object>> list = sqliteMasterDao.showColumns(table);
        list.forEach(r ->
                res.add(r.get("name").toString())
        );
        return res;
    }

    public List<Map<String, Object>> showDataMap(QueryVo.QueryParam queryParam) {
        List<String> columns = queryParam.getColumnName();
        QueryWrapper<Object> wrapper = new QueryWrapper<>();
        if (queryParam.getFilters() != null) {
            queryParam.getFilters().forEach(filter -> {
                if (filter.getOp() == OpEnum.EQ) {
                    wrapper.eq(filter.getColumn(), filter.getValue());
                } else if (filter.getOp() == OpEnum.IN) {
                    String[] strings = filter.getValue().toString().split(StrUtil.COMMA);
                    wrapper.in(filter.getColumn(), (Object) strings);
                } else if (filter.getOp() == OpEnum.LIKE) {
                    wrapper.like(filter.getColumn(), filter.getValue());
                } else {
                    throw new IllegalArgumentException();
                }
                if (!columns.contains(filter.getColumn())) {
                    columns.add(filter.getColumn());
                }
            });
        }
        return sqliteMasterDao.showDataMap(queryParam.getTableName(), StringUtil.listToString(columns, ","), wrapper);
    }

    public List<String> showData(QueryVo.QueryParam queryParam) {
        QueryWrapper<Object> wrapper = new QueryWrapper<>();
        if (queryParam.getFilters() != null) {
            queryParam.getFilters().forEach(filter -> {
                if (filter.getOp() == OpEnum.EQ) {
                    wrapper.eq(filter.getColumn(), filter.getValue());
                } else if (filter.getOp() == OpEnum.IN) {
                    List<String> params = Arrays.asList(filter.getValue().toString().split(StrUtil.COMMA));
                    wrapper.in(filter.getColumn(), params);
                } else if (filter.getOp() == OpEnum.LIKE) {
                    wrapper.like(filter.getColumn(), filter.getValue());
                } else {
                    throw new IllegalArgumentException();
                }
            });
        }
        List<Map<String, Object>> mapList = sqliteMasterDao.showDataMap(queryParam.getTableName(), queryParam.getColumnName().get(0), wrapper);
        List<String> res = new ArrayList<>();
        mapList.forEach(r -> {
            String value = r.get(queryParam.getColumnName().get(0)).toString();
            if (!res.contains(value)) {
                res.add(value);
            }
        });
        return res;
    }
}
