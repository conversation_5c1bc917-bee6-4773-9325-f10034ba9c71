package com.taiyi.shuduoduo.nocodb.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.nocodb.entity.NcProjects;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@DS("sqlite")
public interface NcProjectDao extends CommonMysqlMapper<NcProjects> {

    @Select("select id,title,prefix from nc_projects_v2 where ${ew.sqlSegment}")
    NcProjects getNcProjectInfo( @Param("ew") Wrapper<NcProjects> wrapper);
}