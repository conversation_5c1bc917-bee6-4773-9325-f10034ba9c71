package com.taiyi.shuduoduo.ims.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 台账表
 *
 * <AUTHOR>
 */
@Data
public class RawTableVo {

    @ApiModel("台账表参数")
    @Data
    public static class InsertParam {

        private String id;

        /**
         * 台账ID
         */
        @ApiModelProperty("台账ID")
        private String rawId;

        /**
         * 关联的表ID
         */
        @ApiModelProperty("关联的表ID")
        private String dimId;

        /**
         * 标记的指标表
         */
        private String dwsId;

        /**
         * 维度类型
         */
        private Integer dimType;

        /**
         * 板块ID
         */
        @ApiModelProperty("板块ID")
        private String plateId;

        /**
         * 数据域ID
         */
        @ApiModelProperty("数据域ID")
        private String dataFieldId;

        /**
         * 业务过程ID
         */
        @ApiModelProperty("业务过程ID")
        private String busProcessId;

        /**
         * 表名
         */
        @ApiModelProperty("表名")
        private String name;

        /**
         * 表英文名
         */
        @ApiModelProperty("表英文名")
        private String code;

        private Long orderBy;

        /**
         * 表字段列表
         */
        @ApiModelProperty("表字段列表")
        private List<RawTableAttrVo.InsertParam> rowTableAttrList;

    }

    /**
     * 浏览器台账可选字段参数
     */
    @Data
    public static class OptionParam {

        private String id;

        private String name;

        private String code;

        private Integer total;

        private Long orderBy;

        private List<RawTableAttrVo.DetailResponse> tableAttrs;

    }
}
