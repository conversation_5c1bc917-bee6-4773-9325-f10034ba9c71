package com.taiyi.common.util;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

/**
 * jwt 工具类
 *
 * <AUTHOR>
 */
public class JwtUtil {

    private static Logger logger = LoggerFactory.getLogger(JwtUtil.class);

    //设置token有效期 以分钟为单位(原单位为毫秒)
    private static final long EXPIRES_MINUTES = 60 * 1000;
    private static final String SECRET = "Dxe_d{]1=*a";

    private static final String OPEN_ID = "openId";

    private static final String EXPIRES_TIME = "expires_time";
    private static final String ID = "Id";
    private static final String TENANT_KEY = "tenant_key";
    private static final String THIRD_USER_ID = "thirdUserId";
    private static final String REAL_NAME = "real_name";
    private static final String NICK_NAME = "nick_name";

    private static final String AVATAR_URL = "avatar_url";

    private static final String COMPANY_ID = "company_id";
    private static final String IF_BACKEND = "if_backend";
    private static final String ROLES = "roles";
    private static final String ACCESS_TOKEN = "access_token";
    private static final String EXPIRES_IN = "expires_in";
    private static final String REFRESH_TOKEN = "refresh_token";
    private static final String REFRESH_EXPIRES_IN = "refresh_expires_in";
    private static final String TOKEN_TYPE = "token_type";

    /**
     * 根据用户信息加密
     *
     * @param user 用户信息
     * @return token
     */
    public static String getToken(CurrentUserUtil.CurrentUser user) {
        Algorithm algorithm = Algorithm.HMAC256(SECRET);
        String token = JWT.create()
                .withExpiresAt(new Date(System.currentTimeMillis() + EXPIRES_MINUTES * user.getExpiresIn()))
                .withClaim(OPEN_ID, user.getOpenId())
                .withClaim(ID, user.getId())
                .withClaim(TENANT_KEY, user.getTenantKey())
                .withClaim(THIRD_USER_ID, user.getThirdUserId())
                .withClaim(REAL_NAME, user.getRealName())
                .withClaim(NICK_NAME, user.getNickName())
                .withClaim(IF_BACKEND, user.getIfBackend())
                .withClaim(COMPANY_ID, user.getCompanyId())
                .withClaim(ROLES, user.getRoles())
                .withClaim(ACCESS_TOKEN, user.getAccessToken())
                .withClaim(EXPIRES_IN, user.getExpiresIn())
                .withClaim(REFRESH_TOKEN, user.getRefreshToken())
                .withClaim(REFRESH_EXPIRES_IN, user.getRefreshExpiresIn())
                .withClaim(TOKEN_TYPE, user.getTokenType())
                .sign(algorithm);
        return token;
    }

    /**
     * 根据Token解密用户信息
     *
     * @param token token
     * @return 用户信息
     */
    public static CurrentUserUtil.CurrentUser decodeToken(String token) {
        CurrentUserUtil.CurrentUser user = new CurrentUserUtil.CurrentUser();

        try {
            DecodedJWT jwt = JWT.decode(token);
            String openId = jwt.getClaim(OPEN_ID).asString();
            String id = jwt.getClaim(ID).asString();
            String tenantKey = jwt.getClaim(TENANT_KEY).asString();
            String thirdUserId = jwt.getClaim(THIRD_USER_ID).asString();
            String realName = jwt.getClaim(REAL_NAME).asString();
            String nickName = jwt.getClaim(NICK_NAME).asString();
            String avatarUrl = jwt.getClaim(AVATAR_URL).asString();
            String companyId = jwt.getClaim(COMPANY_ID).asString();
            boolean ifBackend = jwt.getClaim(IF_BACKEND).asBoolean();
            List<String> roles = jwt.getClaim(ROLES).asList(String.class);
            String accessToken = jwt.getClaim(ACCESS_TOKEN).asString();
            long expiresIn = jwt.getClaim(EXPIRES_IN).asLong();
            String refreshToken = jwt.getClaim(REFRESH_TOKEN).asString();
            long refreshExpiresIn = jwt.getClaim(REFRESH_EXPIRES_IN).asLong();
            String tokenType = jwt.getClaim(TOKEN_TYPE).asString();

            user.setOpenId(openId);
            user.setId(id);
            user.setTenantKey(tenantKey);
            user.setThirdUserId(thirdUserId);
            user.setRealName(realName);
            user.setNickName(nickName);
            user.setAvatarUrl(avatarUrl);
            user.setCompanyId(companyId);
            user.setIfBackend(ifBackend);
            user.setRoles(roles);
            user.setAccessToken(accessToken);
            user.setExpiresIn(expiresIn);
            user.setRefreshToken(refreshToken);
            user.setRefreshExpiresIn(refreshExpiresIn);
            user.setTokenType(tokenType);

        } catch (Exception e) {
            logger.warn("Jwt decode fail. Exception: {}", ExceptionUtil.stacktraceToString(e));
        }

        return user;
    }

    /**
     * 验证token
     */
    public static boolean verifyToken(String token) {
        if (StringUtils.isEmpty(token)) {
            return false;
        }
        Algorithm algorithm = Algorithm.HMAC256(SECRET);
        JWTVerifier verifier = JWT.require(algorithm).build();
        try {
            verifier.verify(token);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public static void main(String[] args) {
        CurrentUserUtil.CurrentUser user = decodeToken("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImxpdXJyIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjczMjgzNjQ4Mzk4ZTQxZjZiNGE3ZWM5NzJjZTFhZjNjIiwiYXZhdGFyX2JpZyI6IiIsInJvbGVzIjpbXSwiYXZhdGFyX3RodW1iIjoiIiwiYXZhdGFyX21pZGRsZSI6IiIsInJlYWxfbmFtZSI6IuWImOiKrueRniIsInRva2VuX3R5cGUiOiIiLCJhY2Nlc3NfdG9rZW4iOiIiLCJyZWZyZXNoX3Rva2VuIjoiIiwiaWZfYmFja2VuZCI6ZmFsc2UsImF2YXRhcl91cmwiOiIiLCJyZWZyZXNoX2V4cGlyZXNfaW4iOjAsIm5pY2tfbmFtZSI6ImxpdXJyIiwiSWQiOiI3MzI4MzY0ODM5OGU0MWY2YjRhN2VjOTcyY2UxYWYzYyIsImV4cCI6MTcxNzU5MTg0MCwiZXhwaXJlc19pbiI6NjIyMDgwMDB9.cY6H2eUzzVx0nFbwY9IU59SiYjWSnvj9MdVjbCUk3zU");
        System.out.println(user + "|" + user.getIfBackend() + "|" + user.getNickName() + "|" + user.getThirdUserId());
    }

}
