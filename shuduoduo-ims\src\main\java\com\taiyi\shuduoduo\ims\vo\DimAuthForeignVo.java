package com.taiyi.shuduoduo.ims.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DimAuthForeignVo {

    @Data
    public static class InsertVo {
        @NotBlank
        private String dimAuthId;
        private Integer authAgent;
        private String authColumn;
        private String operator;
        /**
         * 状态 0、未开启1、开启
         */
        private Boolean status;
        private List<ForeignVo> foreigns;
    }

    @Data
    public static class ForeignVo {
        /**
         * 关联字段
         */
        private String rawColumn;

        /**
         * 权限表关联字段
         */
        private String tableColumn;
    }

    @Data
    public static class DetailVo {

        private String id;
        private Integer authAgent;
        private String authColumn;
        private List<ForeignVo> foreigns;
    }
}
