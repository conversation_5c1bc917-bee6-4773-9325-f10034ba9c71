package com.taiyi.common.connector.query;

import cn.hutool.core.util.ObjectUtil;
import com.taiyi.common.connector.db.ConnectionPool;
import com.taiyi.common.connector.db.DbConnection;
import com.taiyi.common.connector.entity.DbEntity;
import com.taiyi.common.connector.entity.PostgreEntity;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;

/**
 * Postgre数据库处理
 *
 * <AUTHOR>
 */
public class PostgreQuery implements DbQuery {

    private static Logger logger = LoggerFactory.getLogger(PostgreQuery.class);

    private PostgreEntity entity;

    private ResultSet resultSet;

    public PostgreQuery(DbDto dbDto) {
        entity = BeanUtil.copy(dbDto, PostgreEntity.class);
    }

    DbConnection<DbEntity> dbConnection = new ConnectionPool<>();

    public PostgreQuery() {

    }

    /**
     * 关闭结果集
     *
     * @throws SQLException e
     */
    @Override
    public void close() throws SQLException {
        if (ObjectUtil.isNotEmpty(resultSet)) {
            logger.info("关闭resultSet{}", resultSet);
            resultSet.close();
            logger.info("resultSet closed...");
        }
    }

    /**
     * 获取数仓连接
     *
     * @return boolean
     */
    @Override
    public boolean getConnection() {
        return ObjectUtil.isNotEmpty(dbConnection.getConnection(entity));
    }

    /**
     * 获取表
     *
     * @return list
     */
    @Override
    public List<String> getTables() throws SQLException {
        logger.info("获取数据库表,链接地址为{},数据库为{},schema为{},数据库类型为{}", entity.getHost(), entity.getDatabase(), entity.getSchema(), entity.getType());
        List<String> list = new ArrayList<>();
        String sql = "SELECT tablename FROM pg_tables " +
                "WHERE tablename NOT LIKE 'pg%' " +
                "AND tablename NOT LIKE 'sql_%' " +
                "AND schemaname ='" + entity.getSchema() + "'" +
                " UNION ALL " +
                "SELECT viewname as tablename FROM pg_views " +
                "WHERE schemaname ='" + entity.getSchema() + "'" +
                " ORDER BY tablename";
        resultSet = dbConnection.getConnection(entity).createStatement().executeQuery(sql);
        while (resultSet.next()) {
            list.add(resultSet.getString(1));
        }
        close();
        return list;
    }

    /**
     * 获取数据库表字段
     *
     * @param tableName 表
     * @return List
     * @throws SQLException e
     */
    @Override
    public List<String> getColumns(String tableName) throws SQLException {
        logger.info("获取数据库表字段,链接地址为{},数据库为{},表名为{}", entity.getHost(), entity.getDatabase(), tableName);
        List<String> list = new ArrayList<>();
        resultSet = dbConnection.getConnection(entity).createStatement().executeQuery(
                "SELECT column_name FROM information_schema.columns " +
                        "WHERE table_schema= '" + entity.getSchema() +
                        "' AND table_name = '" + tableName + "'");
        while (resultSet.next()) {
            list.add(resultSet.getString("column_name"));
        }
        close();
        return list;
    }

    /**
     * 获取数据库表字段及字段类型
     *
     * @param tableName 表名
     * @return List
     * @throws SQLException e
     */
    @Override
    public List<Map<String, Object>> getColumnsAndType(String tableName) throws SQLException {
        logger.info("getColumnsAndType获取数据库表字段及字段类型,链接地址为{},数据库为{},表名为{}", entity.getHost(), entity.getDatabase(), tableName);
        List<Map<String, Object>> list = new ArrayList<>();
        resultSet = dbConnection.getConnection(entity).createStatement().executeQuery(
                "SELECT t0.column_name,t0.data_type,t3.comments FROM information_schema.COLUMNS AS t0 " +
                        "LEFT JOIN " +
                        "(SELECT t1.attname AS column_name,col_description(t1.attrelid,t1.attnum) AS comments " +
                        "FROM pg_attribute AS t1 " +
                        "LEFT JOIN pg_class AS t2 ON t1.attrelid = t2.oid  " +
                        "WHERE t1.attnum >= 0 AND t2.relname = '" + tableName + "'" +
                        ") as t3 " +
                        "ON t0.column_name = t3.column_name " +
                        "WHERE t0.table_schema = '" + entity.getSchema() + "' AND t0.table_name = '" + tableName + "'");
        while (resultSet.next()) {
            Map<String, Object> map = new HashMap<>();
            map.put("columnName", resultSet.getString("column_name"));
            map.put("dataType", resultSet.getString("data_type"));
            map.put("comments", resultSet.getString("comments"));
            list.add(map);
        }
        close();
        return list;
    }

    /**
     * 条件查询
     *
     * @param tableName   表名
     * @param queryColumn 查询列
     * @param queryParam  查询条件
     * @param sortParam   排序条件
     * @return list
     * @throws SQLException sqlError
     */
    @Override
    public List<Map<String, Object>> query(String tableName, List<String> queryColumn, List<Map<String, Object>> queryParam, List<Map<String, Object>> sortParam) throws SQLException {
        logger.info("数据库表查询,数据库地址为{},数据库为{},查询表为{}", entity.getHost(), entity.getDatabase(), tableName);
        StringBuilder builder = new StringBuilder();
        builder.append("select ");
        if (ObjectUtil.isEmpty(queryColumn)) {
            builder.append("* ");
        } else {
            for (String s : queryColumn) {
                builder.append(s).append(",");
            }
            builder.deleteCharAt(builder.length() - 1);
        }
        builder.append(" from ").append(tableName);
        if (ObjectUtil.isNotEmpty(queryParam)) {
            for (Map<String, Object> map : queryParam) {
                for (String key : map.keySet()) {
                    builder.append(" where ");
                    if (key.contains("like") || key.contains("LIKE")) {
                        builder.append(key).append(" '%").append(map.get(key)).append("%' ");
                    } else {
                        builder.append(key).append("'").append(map.get(key)).append("' ");
                    }
                }
            }
        }
        if (ObjectUtil.isNotEmpty(sortParam)) {
            for (Map<String, Object> map : sortParam) {
                builder.append("order by ");
                for (String key : map.keySet()) {
                    builder.append(key).append(" ").append(map.get(key));
                }
            }
        }
        logger.info("sql:{}", builder.toString());
        resultSet = dbConnection.getConnection(entity).createStatement().executeQuery(builder.toString());
        return convertList(resultSet);
    }

    /**
     * 数据查询
     *
     * @param sql sql
     * @return List
     */
    @Override
    public List<Map<String, Object>> querySql(String sql) throws SQLException {
        logger.info("数据库表查询,数据库地址为{},数据库为{},查询SQL为{}", entity.getHost(), entity.getDatabase(), sql);
        resultSet = dbConnection.getConnection(entity).createStatement().executeQuery(sql);
        return convertList(resultSet);
    }

    /**
     * 数据集处理
     *
     * @param resultSet 结果集
     * @return List
     * @throws SQLException e
     */
    public List<Map<String, Object>> convertList(ResultSet resultSet) throws SQLException {
        List<Map<String, Object>> result = new ArrayList<>();
        ResultSetMetaData metaData = resultSet.getMetaData();
        int count = metaData.getColumnCount();
        while (resultSet.next()) {
            Map<String, Object> map = new LinkedHashMap<>();
            for (int i = 1; i <= count; i++) {
                map.put(metaData.getColumnName(i), resultSet.getObject(i));
            }
            result.add(map);
        }
        close();
        return result;
    }

    public static void main(String[] args) throws SQLException {

        DbDto dbDto = new DbDto();
        dbDto.setHost("pgm-bp126pxhjsf986tj3o.pg.rds.aliyuncs.com");
        dbDto.setPort(1921);
        dbDto.setDatabase("shuduoduo_data");
        dbDto.setSchema("public");
        dbDto.setType("POSTGRE");
        dbDto.setUsername("shujiajia");
        dbDto.setPassword("shujiajia1999!");
        PostgreQuery postgreQuery = new PostgreQuery(dbDto);
        List<String> tables = postgreQuery.getTables();
        for (String table : tables) {
            System.out.println(table);
            List<String> columns = postgreQuery.getColumns(table);
            System.out.println(columns);
            List<Map<String, Object>> list = postgreQuery.getColumnsAndType(table);
        list.forEach(System.out::println);
        }
        /*List<String> queryColumn = new ArrayList<>();
        queryColumn.add("userid");
        queryColumn.add("usersf");
        queryColumn.add("phonenumber");
        queryColumn.add("username");
        queryColumn.add("sign");
        queryColumn.add("email");
        List<Map<String, Object>> queryParam = new ArrayList<>();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("userid like", "v-");
        queryParam.add(queryMap);
        List<Map<String, Object>> sortParam = new ArrayList<>();
        Map<String, Object> sortMap = new HashMap<>();
        sortMap.put("userid", "desc");
        sortParam.add(sortMap);
        List<Map<String, Object>> mapList = postgreQuery.query("dim_user", queryColumn, queryParam, sortParam);
        for (Map<String, Object> map : mapList) {
            map.values().forEach(System.out::println);
        }*/
    }
}
