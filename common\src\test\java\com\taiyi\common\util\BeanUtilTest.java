package com.taiyi.common.util;

import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;

public class BeanUtilTest {

    @Test
    void copy() {
        String from = "from";
        String to = BeanUtil.copy(from, String.class);
        Assertions.assertTrue(to.equals(from));
    }

    @Test
    void copyNull() {
        String from = "from";
        String to = BeanUtil.copy(from, String.class);
        Assertions.assertNotNull(to);
    }

    @Test
    void copyBean() {
        A a = new A("a", true);
        B b = BeanUtil.copy(a, B.class);

        Assertions.assertTrue(b.name.equals(a.name));
        Assertions.assertTrue(b.bool.equals(a.bool));
        Assertions.assertNull(b.i);

    }

    @Test
    void copyList() {
        A a = new A("a", true);
        List<A> list = new ArrayList<>();
        list.add(a);
        List<B> result = BeanUtil.copyList(list, B.class);
        Assertions.assertTrue(result.size() == 1);
        B b = result.get(0);
        Assertions.assertTrue(b.name.equals(a.name));
        Assertions.assertTrue(b.bool.equals(a.bool));
        Assertions.assertNull(b.i);
    }


    @Test
    void copyExtends(){
        ChildA a = new ChildA();

        a.setPriChild("priChild");
        a.setPubChild("pubChild");
        a.setPri("pri");
        a.setPub("pub");

        ChildB b = BeanUtil.copy(a,ChildB.class);
        System.out.println(JSON.toJSONString(b));
        Assertions.assertTrue(a.getPri().equals(b.getPri()));
        Assertions.assertTrue(a.getPub().equals(b.getPub()));
    }

}

class A {
    String name;
    Boolean bool;

    public A(String name, Boolean bool) {
        this.name = name;
        this.bool = bool;
    }
}

class B {
    String name;
    Boolean bool;
    Integer i;
}

class Parent {

    public String pub;

    private String pri;

    public String getPub() {
        return pub;
    }

    public void setPub(String pub) {
        this.pub = pub;
    }

    public String getPri() {
        return pri;
    }

    public void setPri(String pri) {
        this.pri = pri;
    }
}

class ChildA extends Parent{

    public String pub;

    public String pubChild;

    private String priChild;

    @Override
    public String getPub() {
        return pub;
    }

    @Override
    public void setPub(String pub) {
        this.pub = pub;
    }

    public String getPubChild() {
        return pubChild;
    }

    public void setPubChild(String pubChild) {
        this.pubChild = pubChild;
    }

    public String getPriChild() {
        return priChild;
    }

    public void setPriChild(String priChild) {
        this.priChild = priChild;
    }
}

class ChildB extends Parent{

    public String pubChild;

    private String priChild;

    public String getPubChild() {
        return pubChild;
    }

    public void setPubChild(String pubChild) {
        this.pubChild = pubChild;
    }

    public String getPriChild() {
        return priChild;
    }

    public void setPriChild(String priChild) {
        this.priChild = priChild;
    }


}