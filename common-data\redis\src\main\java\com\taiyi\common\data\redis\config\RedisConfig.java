package com.taiyi.common.data.redis.config;


import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPoolConfig;

/**
 * <AUTHOR>
 */
@Configuration
public class RedisConfig  {

    /**
     * redisTemplate配置，使用jedis连接
     * @param jedisConnectionFactory
     * @return
     */
    @Bean
    public RedisTemplate redisTemplate(JedisConnectionFactory jedisConnectionFactory){
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer=new Jackson2JsonRedisSerializer(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        RedisTemplate rt=new RedisTemplate();
        rt.setConnectionFactory(jedisConnectionFactory);
        RedisSerializer rs=new StringRedisSerializer();
        rt.setKeySerializer(rs);
        rt.setValueSerializer(jackson2JsonRedisSerializer);
        rt.setHashKeySerializer(rs);
        rt.setHashValueSerializer(jackson2JsonRedisSerializer);
        rt.afterPropertiesSet();
        return rt;

    }
    @Configuration
    public class JedisConfig {
        @Value("${spring.redis.host}")
        public String host;
        @Value("${spring.redis.password}")
        public String password;
        @Value("${spring.redis.port}")
        public int port;

        @Value("${spring.redis.jedis.pool.max-idle}")
        public int maxIdle;
        @Value("${spring.redis.jedis.pool.min-idle}")
        public int minIdle;
        @Value("${spring.redis.jedis.pool.max-active}")
        public int maxActive;
        @Value("${spring.redis.jedis.pool.max-wait}")
        public String maxWait;
        @Value("${spring.redis.timeout}")
        public String timeout;

        @Bean
        public JedisConnectionFactory jedisConnectionFactory (){
            RedisStandaloneConfiguration rf=new RedisStandaloneConfiguration();
            rf.setHostName(host);
            rf.setPort(port);
            rf.setPassword(password);
            JedisClientConfiguration.JedisPoolingClientConfigurationBuilder jpb=
                    (JedisClientConfiguration.JedisPoolingClientConfigurationBuilder)JedisClientConfiguration.builder();
            JedisPoolConfig jedisPoolConfig=new JedisPoolConfig();
            jedisPoolConfig.setMaxIdle(maxIdle);
            jedisPoolConfig.setMinIdle(minIdle);
            jedisPoolConfig.setMaxTotal(maxActive);
            int l=Integer.parseInt(maxWait.substring(0,maxWait.length()-2));
            jedisPoolConfig.setMaxWaitMillis(l);
            jpb.poolConfig(jedisPoolConfig);
            JedisConnectionFactory jedisConnectionFactory=new JedisConnectionFactory(rf,jpb.build());
            return jedisConnectionFactory;
        }
    }

}
