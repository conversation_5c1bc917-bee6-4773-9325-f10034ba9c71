package com.taiyi.shuduoduo.ims.entity;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ims_query_topic", autoResultMap = true)
public class QueryTopic extends CommonMySqlEntity {
    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

    /**
     * 台账ID
     */
    private String rawId;

    /**
     * 所选查询列
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray selectedAttr;

    /**
     * 所选筛选条件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray selectedFilter;

    /**
     * 所选排序条件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray selectedOrder;

    /**
     * 所选数据格式转换
     */
    @JsonRawValue
    private String dataParse;

    /**
     * 透视表配置
     */
    @JsonRawValue
    private String pivotTableConfig;

    /**
     * 标题
     */
    private String subject;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 最后一次使用时间
     */
    private Date lastTime;

}