package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.TopicApiDao;
import com.taiyi.shuduoduo.ims.entity.TopicApi;
import com.taiyi.shuduoduo.ims.vo.TopicApiVo;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.api.service.UserRpcService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class TopicApiService extends CommonMysqlService<TopicApiDao, TopicApi> {
    @Override
    public Class<TopicApi> getEntityClass() {
        return TopicApi.class;
    }

    @Autowired
    private TopicApiLogService topicApiLogService;

    @Autowired
    private UserRpcService userRpcService;

    public PageResult selectPage(TopicApiVo.PageVo pageVo) {
        Page<TopicApi> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        QueryWrapper<TopicApi> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false);
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.like("name", pageVo.getKeyWord());
        }
        if (StringUtils.isNotBlank(pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getSortType())) {
            wrapper.last("order by " + StrUtil.toSymbolCase(pageVo.getColumn(), '_') + " " + pageVo.getSortType());
        } else {
            wrapper.orderByDesc("create_time");
        }
        IPage<TopicApi> iPage = super.page(page, wrapper);
        List<TopicApiVo.PageResponse> responses = BeanUtil.copyList(iPage.getRecords(), TopicApiVo.PageResponse.class);
        for (TopicApiVo.PageResponse response : responses) {
            response.setCount(topicApiLogService.getCount(response.getId()));
            if (StringUtils.isNotBlank(response.getCreateBy())) {
                UserDTO userDTO = userRpcService.getUserInfoById(response.getCreateBy());
                response.setUsername(null == userDTO ? "" : userDTO.getRealName());
            }
        }
        PageResult<TopicApiVo.PageResponse> result = new PageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setList(responses);
        result.setTotal(iPage.getTotal());
        return result;
    }

    public int getApiCount() {
        QueryWrapper<TopicApi> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false);
        return super.count(wrapper);
    }
}