package com.taiyi.common.connector.netty.utils;

import com.taiyi.common.connector.netty.vo.DataEngineMsg;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public class DataMessageDecode extends ByteToMessageDecoder {

    protected final Logger logger = LoggerFactory.getLogger(DataMessageDecode.class);


    @Override
    protected void decode(ChannelHandlerContext channelHandlerContext, ByteBuf in, List<Object> list) {
        try {
            if (in.readableBytes() < 4) {
                return;
            }
            //标记当前位置
            in.markReaderIndex();
            //读取数据长度
            int dataLength = in.readInt();
            if (in.readableBytes() < dataLength) {
                //数据不够 继续读取
                in.resetReaderIndex();
                return;
            }
            byte[] body = new byte[dataLength];
            in.readBytes(body);
            DataEngineMsg obj = ProtostuffUtil.deserialize(body, DataEngineMsg.class);
            list.add(obj);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
