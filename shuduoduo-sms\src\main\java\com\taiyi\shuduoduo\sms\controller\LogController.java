package com.taiyi.shuduoduo.sms.controller;

import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.sms.entity.Log;
import com.taiyi.shuduoduo.sms.service.LogService;
import com.taiyi.shuduoduo.sms.vo.LogVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 日志采集
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/log")
public class LogController {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private LogService logService;

    /**
     * 保存日志
     *
     * @param logVo 添加信息
     * @return bool
     */
    @PostMapping
    private void saveLog(@RequestBody @Validated LogVo.AddLog logVo, HttpServletRequest req) {
        logger.info("记录日志,{}", req.getRemoteHost());
        Log log = BeanUtil.copy(logVo, Log.class);
        logService.saveToFile(log, req.getRemoteHost());
    }
}
