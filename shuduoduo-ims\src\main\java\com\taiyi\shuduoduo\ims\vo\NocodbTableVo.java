package com.taiyi.shuduoduo.ims.vo;


import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
public class NocodbTableVo {

    @Data
    public static class EditRequest {

        /**
         * 主键ID
         */
        private String id;

        private String baseId;
        /**
         * 项目ID
         */
        @NotBlank
        private String projectId;

        /**
         * 名称
         */
        @NotBlank
        private String name;

        /**
         * 描述
         */
        private String desc;

        /**
         * 表数据
         */
        private Map<String, Object> body;
    }

    @Data
    public static class NocodbTablePageVo extends CommonMySqlPageVo{

        @NotBlank
        private String projectId;

        private String baseId;

    }

}
