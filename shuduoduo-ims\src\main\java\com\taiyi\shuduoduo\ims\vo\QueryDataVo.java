package com.taiyi.shuduoduo.ims.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 查询参数实体
 *
 * <AUTHOR>
 */
@Data
public class QueryDataVo {

    /**
     * 台账查询
     */
    @Data
    public static class QueryRequest {

        private Integer pageNo;

        private Integer pageSize;

        /**
         * 类型： 1、台账 2、维度事实表
         */
        private Integer type;

        /**
         * 计算函数
         * (SUM,MIN,MAX,AVG)
         */
        private String calcFunc;

        /**
         * 台账ID
         */
        private String rawId;

        /**
         * 查询字段ID集合
         */
        private List<String> tableAttrIds;

        /**
         * 自定义列ID集合
         */
        private List<String> fieldIds;

        /**
         * 筛选条件
         */
        private List<SqlBuilderVo.Filter> filters;

        /**
         * 排序条件
         */
        private List<SqlBuilderVo.Order> orders;

        /**
         * 请求来源 1、前端查询 2、API调用
         */
        private Integer requestFrom = 1;

        /**
         * API id
         */
        private String apiId = "";

        /**
         * 导出参数
         */
        private String fileName;
        private Boolean ifShow;
        private String headerContext;

    }

    /**
     * 快捷API查询
     */
    @Data
    public static class QueryApiRequest {

        private Integer pageNo;

        private Integer pageSize;

        /**
         * 筛选条件
         */
        private List<SqlBuilderVo.Filter> filters;
    }

    /**
     * 查询in条件列表
     */
    @Data
    public static class InQueryRequest {

        private Integer pageNo;

        private Integer pageSize;

        /**
         * 台账ID
         */
        private String rawId;

        /**
         * 查询字段ID
         */
        private String tableAttrId;

        /**
         * 筛选字段
         */
        private String keyWord;

        /**
         * 排序条件
         */
        private List<SqlBuilderVo.Order> orders;

    }

}
