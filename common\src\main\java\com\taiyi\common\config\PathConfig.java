package com.taiyi.common.config;

import java.util.ArrayList;
import java.util.List;

/**
 * 路径相关的配置
 *
 * <AUTHOR>
 */
public class PathConfig {

    /**
     * 权限允许的Path
     *
     * @return List
     */
    public static List<String> authAllowPathList() {
        List<String> list = new ArrayList<>();

        //特殊处理的接口
        list.add("/shuduoduo/ums/api/**");

        // 任务相关接口
        list.add("/shuduoduo/*/task/**");

        //开发API接口
        list.add("/shuduoduo/ims/openapi/**");
        list.add("/shuduoduo/ums/openapi/**");
        list.add("/shuduoduo/sms/openapi/**");
        list.add("/shuduoduo/nocodb/openapi/**");

        // 获取nocodb信息接口
        list.add("/shuduoduo/ims/nocodb/table/**");
        // 获取权限nocodb信息接口
        list.add("/shuduoduo/ims/auth/nocodb/info/**");
        //登录页查询公司列表接口
        list.add("/shuduoduo/ums/company/**");
        //登录接口
        list.add("/shuduoduo/ums/login/**");
        //文件导出接口
        list.add("/shuduoduo/ims/query/data/export/**");
        //企微回调地址
        list.add("/shuduoduo/ums/api/wx/**");

        //测试专用
        list.add("/shuduoduo/ums/test/**");
        list.add("/shuduoduo/sms/test/**");
        list.add("/shuduoduo/ims/test/**");

        //接口文档
        list.add("/shuduoduo/ums/swagger-ui/**");
        list.add("/shuduoduo/sms/swagger-ui/**");
        list.add("/shuduoduo/ims/swagger-ui/**");

        return list;
    }


    /**
     * rpc对应的Path
     *
     * @return List
     */
    public static List<String> rpcPathList() {

        List<String> list = new ArrayList<>();
        list.add("/shuduoduo/*/rpc/**");
        list.add("/common/*/rpc/**");
        //企微回调地址
        list.add("/shuduoduo/ums/api/wx/**");
        return list;
    }

}
