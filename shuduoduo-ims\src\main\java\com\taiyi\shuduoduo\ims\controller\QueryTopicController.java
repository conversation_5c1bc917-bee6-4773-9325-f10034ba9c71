package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.aspect.ApiLogPointCut;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.QueryTopic;
import com.taiyi.shuduoduo.ims.service.QueryTopicService;
import com.taiyi.shuduoduo.ims.vo.PressVo;
import com.taiyi.shuduoduo.ims.vo.QueryTopicVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import com.taiyi.shuduoduo.ims.vo.TopicShareVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 查询主题
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/query/topic")
@Validated
public class QueryTopicController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private QueryTopicService queryTopicService;

    /**
     * 新增数据
     *
     * @param param 主题的内容
     * @return ResponseEntity
     */
    @ApiLogPointCut(name = "保存主题", description = "用户保存主题")
    @PostMapping
    public ResponseEntity save(@RequestBody @Validated QueryTopicVo.EditTopicParam param) {
        QueryTopic topic = BeanUtil.copy(param, QueryTopic.class);
        //检查重名
        if (queryTopicService.isNameDuplicate(topic)) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.NAME_DUPLICATE);
        }
        try {
            if (queryTopicService.add(topic)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, "保存主题失败");
            }
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 根据id查找主题
     *
     * @param id 主题ID
     * @return T
     */
    @GetMapping("/{id}")
    public ResponseEntity getById(@PathVariable("id") String id) {
        try {
            queryTopicService.updateLastTime(id);
            return ResponseVo.response(MessageCode.SUCCESS, queryTopicService.getById(id));
        } catch (Exception e) {
            logger.error("查找主题失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 根据id删除主题
     *
     * @param id 主题ID
     * @return T
     */
    @ApiLogPointCut(name = "删除主题", description = "用户删除主题")
    @DeleteMapping("/{id}")
    public ResponseEntity deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = queryTopicService.logicDeleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 自定义查询批量删除
     *
     * @param t 自定义查询ID集合
     * @return T
     */
    @DeleteMapping("/deleteBatch")
    public ResponseEntity<ResponseVo.ResponseBean> deleteBatch(@RequestBody TopicShareVo.DeleteParam t) {
        boolean f;
        try {
            f = queryTopicService.logicDeleteBatchByIds(t.getIds());
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 主题分页查询
     *
     * @param pageVo 查询参数
     * @return 主题列表
     */
    @PostMapping("/page")
    public ResponseEntity page(@RequestBody @Validated QueryTopicVo.QueryTopicPageVo pageVo) {
        //从token获取用户id
        String userId = CurrentUserUtil.get().getId();
        if (StringUtils.isEmpty(userId)) {
            return ResponseVo.response(MessageCode.TOKEN_EXPIRE, TipsConstant.USER_NOT_FOUND);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, queryTopicService.pageList(pageVo, userId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 查看-我的查询
     *
     * @return ResponseEntity
     */
    @ApiLogPointCut(name = "查看-我的查询", description = "用户查看-我的查询")
    @PostMapping("/query")
    public ResponseEntity queryTopic(@RequestBody PressVo pressVo) {
        return ResponseVo.response(MessageCode.SUCCESS);
    }

    /**
     * 编辑-我的查询
     *
     * @return ResponseEntity
     */
    @ApiLogPointCut(name = "编辑-我的查询", description = "用户编辑-我的查询")
    @PostMapping("/updateTopic")
    public ResponseEntity updateTopic(@RequestBody PressVo pressVo) {
        return ResponseVo.response(MessageCode.SUCCESS);
    }

    /**
     * 总条数统计
     *
     * @return T
     */
    @GetMapping("/total")
    public ResponseEntity getTotal() {
        try {
            Map<String, Integer> res = queryTopicService.getTotal(CurrentUserUtil.get().getId());
            return ResponseVo.response(MessageCode.SUCCESS, res);
        } catch (Exception e) {
            logger.error("获取自定义查询总条数失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 最近使用列表
     *
     * @return T
     */
    @GetMapping("/recent")
    public ResponseEntity getRecentList() {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, queryTopicService.getRecentList(CurrentUserUtil.get().getId()));
        } catch (Exception e) {
            logger.error("获取最近使用列表失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 根据用户ID获取自定义查询列表
     *
     * @param keyWord 模糊查询
     * @return T
     */
    @GetMapping("/user/list")
    public ResponseEntity<ResponseVo.ResponseBean> getListByUser(@RequestParam(value = "keyWord", required = false) String keyWord) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, queryTopicService.getTopicListByUserId(CurrentUserUtil.get().getId(), keyWord));
        } catch (Exception e) {
            logger.error("获取由用户创建的自定义报表列表失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    @GetMapping("/all")
    public ResponseEntity<ResponseVo.ResponseBean> getAllByUser(@RequestParam(value = "keyWord", required = false) String keyWord) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, queryTopicService.getTopicAllByUserId(CurrentUserUtil.get().getId(),keyWord));
        } catch (Exception e) {
            logger.error("获取用户所有的自定义报表失败:{}",ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

}
