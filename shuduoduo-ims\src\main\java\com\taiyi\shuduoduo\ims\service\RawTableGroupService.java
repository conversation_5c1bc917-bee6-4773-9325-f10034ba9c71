package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.RawTableGroupDao;
import com.taiyi.shuduoduo.ims.entity.RawTableGroup;
import com.taiyi.shuduoduo.ims.vo.RawTableAttrVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RawTableGroupService extends CommonMysqlService<RawTableGroupDao, RawTableGroup> {
    @Override
    public Class<RawTableGroup> getEntityClass() {
        return RawTableGroup.class;
    }

    @Autowired
    private RawTableGroupAttrService tableGroupAttrService;

    @Autowired
    private RawTableGroupDao dao;

    public List<RawTableGroup> listByRawTableId(String rawTableId) {
        QueryWrapper<RawTableGroup> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(rawTableId), "raw_table_id", rawTableId).eq("if_deleted", false).orderByAsc("order_by");
        return super.list(wrapper);
    }

    public boolean logicDeleteByRawTableId(String rawTableId) {
        List<RawTableGroup> tables = listByRawTableId(rawTableId);
        if (tables.isEmpty()) {
            return true;
        }
        for (RawTableGroup table : tables) {
            // 删除表分组及字段分组
            tableGroupAttrService.logicDeleteByRawTableGroupId(table.getId());
        }
        UpdateWrapper<RawTableGroup> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", 1)
                .eq("raw_table_id", rawTableId);
        return this.update(wrapper);
    }


    /**
     * 根据台账表ID、台账表字段ID 批量保存字段分组信息
     *
     * @param rawTableId     台账表ID
     * @param rawTableAttrId 台账表字段ID
     * @param param          字段分组信息
     * @return bool
     */
    public boolean saveBatchByRawTableId(String companyId, String rawTableId, String rawTableAttrId, RawTableAttrVo.InsertParam param) {
        boolean f;
        RawTableGroup tableGroup = new RawTableGroup();
        tableGroup.setCompanyId(companyId);
        tableGroup.setRawTableId(rawTableId);
        tableGroup.setName(param.getGroupName());
        if (super.exists(tableGroup)) {
            tableGroup = this.getOneByEntity(tableGroup);
        } else {
            super.save(tableGroup);
        }
        // 保存表分组字段关联
        return tableGroupAttrService.saveBatchByRawTableId(companyId, rawTableAttrId, tableGroup.getId(), param);
    }

    /**
     * 根据分组信息查询分组对象
     *
     * @param tableGroup 分组信息
     * @return 分组对象
     */
    private RawTableGroup getOneByEntity(RawTableGroup tableGroup) {
        QueryWrapper<RawTableGroup> wrapper = new QueryWrapper<>();
        wrapper.setEntity(tableGroup).eq("if_deleted", false).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 根据台账表ID和表字段ID查询台账分组对象
     *
     * @param rawTableId     台账表ID
     * @param rawTableAttrId 表字段ID
     * @return 台账分组对象
     */
    public RawTableGroup getNameByRawTableIdAndTableAttrId(String rawTableId, String rawTableAttrId) {
        QueryWrapper<RawTableGroup> wrapper = new QueryWrapper<>();
        wrapper.eq("a.if_deleted", false).eq("b.if_deleted", false)
                .eq("a.raw_table_id", rawTableId).eq("b.raw_table_attr_id", rawTableAttrId)
                .orderByDesc("order_by", "create_time");
        return dao.getNameByRawTableIdAndTableAttrId(wrapper);
    }
}