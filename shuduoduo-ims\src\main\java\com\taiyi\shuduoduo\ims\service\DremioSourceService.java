package com.taiyi.shuduoduo.ims.service;

import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.AesUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.StrongPasswordGenerator;
import com.taiyi.shuduoduo.ims.dao.DremioSourceDao;
import com.taiyi.shuduoduo.ims.dremio.vo.DremioVo;
import com.taiyi.shuduoduo.ims.dremio.service.DremioApiService;
import com.taiyi.shuduoduo.ims.entity.DremioSource;
import com.taiyi.shuduoduo.ims.exceptions.DremioException;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyDremioRpcService;
import com.taiyi.shuduoduo.ums.api.service.UserRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Dremio 数据源
 *
 * <AUTHOR>
 */
@Service
public class DremioSourceService extends CommonMysqlService<DremioSourceDao, DremioSource> {
    @Override
    public Class<DremioSource> getEntityClass() {
        return DremioSource.class;
    }

    @Autowired
    private DremioApiService dremioApiService;

    @Autowired
    private CompanyDremioRpcService companyDremioRpcService;

    @Autowired
    private UserRpcService userRpcService;

    /**
     * 获取Dremio数据源列表
     *
     * @return 数据源列表
     */
    public List<DremioVo.DremioSourceResponse> getList() {
        CompanyDremioDTO dremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == dremioDTO) {
            return new ArrayList<>();
        }
        return dremioApiService.getSourceList(dremioDTO.getDremioUsername(), dremioDTO.getDremioPasswd(), dremioDTO.getDremioApiUri());
    }

    /**
     * 获取Dremio服务Token
     *
     * @return Token
     */
    public DremioVo.DremioLoginResponseVo getDremioToken() {
        CompanyDremioDTO dremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        UserDTO userDTO = userRpcService.getUserInfoById(CurrentUserUtil.get().getId());
        if (null == dremioDTO || null == userDTO) {
            return null;
        }
        String username = userDTO.getThirdUserId();
        String baseUri = dremioDTO.getDremioApiUri();
        String userDremioPwdDigest = userDTO.getDremioPwdDigest();
        // 检查用户是否有数据湖账号
        if (null == userDremioPwdDigest) {
            // 生成强密码
            String pwd = StrongPasswordGenerator.generateStrongPassword(10);
            logger.info("为用户 {} 生成强密码", username);
            // 创建用户数据湖账号
            boolean created = dremioApiService.createUser(dremioDTO.getDremioUsername(), dremioDTO.getDremioPasswd(), baseUri, username, pwd, userDTO.getRealName());
            if (!created) {
                logger.error("为用户 {} 创建 Dremio 账号失败", username);
                throw new DremioException("创建 Dremio 账号失败");
            }
            // 将密码加密存储在数据库中
            String encrypt = AesUtil.encrypt(pwd, AesUtil.USER_PASSWORD_KEY_PREFIX);
            userDremioPwdDigest = encrypt;
            UserDTO.DremioDTO userDremio = new UserDTO.DremioDTO();
            userDremio.setId(userDTO.getId());
            userDremio.setDremioPwdDigest(encrypt);
            boolean updated = userRpcService.updateUserDremioInfoById(userDremio);
            if (!updated) {
                logger.error("更新用户 {} 数据湖账号失败", username);
                throw new DremioException("更新用户数据湖账号失败");
            }
        }
        // 解密密码并获取 Token
        try {
            if (null != userDremioPwdDigest) {
                String decryptedPassword = AesUtil.decrypt(userDremioPwdDigest, AesUtil.USER_PASSWORD_KEY_PREFIX);
                return dremioApiService.getToken(username, decryptedPassword, baseUri);
            }
        } catch (Exception e) {
            logger.error("解密 Dremio 密码失败: {}", e.getMessage(), e);
            throw new RuntimeException("解密密码失败", e);
        }
        return null;
    }
}