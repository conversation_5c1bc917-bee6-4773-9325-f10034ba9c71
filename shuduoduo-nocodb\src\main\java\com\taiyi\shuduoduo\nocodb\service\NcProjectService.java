package com.taiyi.shuduoduo.nocodb.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.nocodb.dao.NcProjectDao;
import com.taiyi.shuduoduo.nocodb.entity.NcProjects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class NcProjectService extends CommonMysqlService<NcProjectDao, NcProjects> {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private NcProjectDao ncProjectDao;

    @Override
    public Class<NcProjects> getEntityClass() {
        return NcProjects.class;
    }

    /**
     * 根据项目名称 查询数据
     *
     * @param name 项目名称
     * @return 项目信息
     */
    public NcProjects getNcProjectInfo(String name){
        QueryWrapper<NcProjects> wrapper = new QueryWrapper<>();
        wrapper.eq("title",name);
        NcProjects info = ncProjectDao.getNcProjectInfo(wrapper);
        return info;
    }



}