package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw_object")
public class RawObject extends CommonMySqlEntity {
    private Boolean ifDeleted;

    /**
     * 对象名称
     */
    private String name;

    private Long orderBy;

    /**
     * 台账ID
     */
    private String rawId;

    private String companyId;

}