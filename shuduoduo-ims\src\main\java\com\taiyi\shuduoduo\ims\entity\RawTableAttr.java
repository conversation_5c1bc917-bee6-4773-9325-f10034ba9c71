package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw_table_attr")
public class RawTableAttr extends CommonMySqlEntity {
    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 台账表ID
     */
    private String rawTableId;

    /**
     * 维度/事实表字段ID
     */
    private String dimAttrId;

    /**
     * 指标表ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String dwsId;

    /**
     * 是否台账显示
     */
    private Boolean ifShow;

    /**
     * 字段中文名
     */
    private String name;

    /**
     * 字段别名
     */
    private String aliasName;

    /**
     * 字段英文名
     */
    private String code;

    /**
     * 显示格式
     */
    private String dataFormat;

    /**
     * 是否默认查询列
     */
    private Boolean ifDefault;

    private Long orderBy;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

}