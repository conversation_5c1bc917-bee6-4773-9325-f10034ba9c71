package com.taiyi.shuduoduo.ims.api.service;


import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.ims.api.dto.BusinessProcessDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 业务过程RPC
 *
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoIms.SERVER_NAME)
public interface BusinessProcessRpcService {

    /**
     * 获取所有板块
     *
     * @return 板块列表
     */
    @GetMapping(value = MicroServer.ShuduoduoIms.SERVER_PREFIX + "/rpc/busProc/getPlateIds")
    List<String> getPlateIds();

    /**
     * 根据板块ID获取业务过程列表
     *
     * @return 业务过程列表
     */
    @GetMapping(value = MicroServer.ShuduoduoIms.SERVER_PREFIX + "/rpc/busProc/getBusinessProcessListByPlateId")
    List<BusinessProcessDTO> getBusinessProcessListByPlateId(@RequestParam("plateId") String plateId);
}
