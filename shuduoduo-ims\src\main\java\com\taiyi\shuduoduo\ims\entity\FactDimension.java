package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_fact_dimension")
public class FactDimension extends CommonMySqlEntity {
    private String companyId;

    /**
     * 事实表ID
     */
    private String factId;
    /**
     * 事实表字段ID
     */
    private String factAttrId;

    /**
     * 维度表ID
     */
    private String dimId;

    /**
     * 维度表字段ID
     */
    private String dimAttrId;

    /**
     * 维度类型 1、统计维度 2、统计周期
     */
    private Integer dimType;

    /**
     * 统计周期标识
     */
    private String periodUnit;

    /**
     * 统计周期格式
     */
    private String periodFormat;

    private Long orderBy;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

}