package com.taiyi.shuduoduo.ims.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.Dimension;
import com.taiyi.shuduoduo.ims.vo.DimensionVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public interface DimensionDao extends CommonMysqlMapper<Dimension> {
    /*@Select("SELECT  id,name,code,attrName,attrCode FROM \n" +
            "( \n" +
            "SELECT \n" +
            " id,name,code,attrName,attrCode ,row_number() over(partition by id,name) as rn \n" +
            "FROM \n" +
            "( \n" +
            "SELECT id, name,code,null as attrName,null as attrCode \n" +
            "FROM \n" +
            "ims_dimension \n" +
            "${ew.customSqlSegment} \n" +
            "UNION ALL  \n" +
            "SELECT distinct r.id,r.name as name,r.code as code,a.name as attrName,a.code as attrCode \n" +
            "FROM \n" +
            "ims_dimension_attr AS a \n" +
            "LEFT JOIN \n" +
            "ims_dimension AS r ON a.dim_id = r.id \n" +
            "WHERE a.if_deleted = FALSE AND a.company_id = '${companyId}' AND r.data_type = ${dataType} " +
            "AND ((a.name LIKE '%${keyWord}%'  OR r.name LIKE '%${keyWord}%') " +
            "OR " +
            "(a.code LIKE '%${keyWord}%'  OR r.code LIKE '%${keyWord}%')) \n" +
            "AND r.if_deleted = FALSE AND r.company_id = '${companyId}' \n" +
            ") a \n" +
            ") b \n" +
            "WHERE rn=1 \n" +
            "ORDER BY  attrName ,id \n")*/
    List<Map<String, Object>> selectSearchDimensionList(@Param("companyId") String companyId,
                                                        @Param("dataType") Integer dataType,
                                                        @Param("keyword") String keyword);
}