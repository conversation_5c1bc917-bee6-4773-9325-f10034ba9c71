package com.taiyi.common.aspect;

import com.alibaba.fastjson.JSON;
import com.taiyi.common.entity.HttpHeaderConstant;
import com.taiyi.common.util.CurrentUserUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * API切面
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class WebApiLogAspect {

    private Logger logger = LoggerFactory.getLogger(WebApiLogAspect.class);


    @Pointcut("execution(public * com.taiyi..controller.*.*(..))")
    public void apiLogPointCut() {
    }


    @Before("apiLogPointCut()")
    public void before(JoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        String requestId = request.getHeader(HttpHeaderConstant.REQUEST_ID);
        String device = request.getHeader(HttpHeaderConstant.DEVICE);

        String requestJson = null;
        try {
            requestJson = JSON.toJSONString(joinPoint.getArgs());
        } catch (Exception e) {

        }
        logger.debug("api user: {}, request :{}, device: {}, path: {}, method: {}, request params :{}, request json: {}",
                CurrentUserUtil.get().getRealName(), requestId, device, request.getContextPath() + request.getServletPath(),
                request.getMethod(), Arrays.toString(joinPoint.getArgs()), requestJson);

    }


    @AfterReturning(returning = "ret", pointcut = "apiLogPointCut()")
    public void afterReturn(Object ret) throws Throwable {

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        String requestId = request.getHeader(HttpHeaderConstant.REQUEST_ID);
        String device = request.getHeader(HttpHeaderConstant.DEVICE);
        logger.debug("api request :{}, device: {}, response data: {}", requestId, device, JSON.toJSONString(ret).length() > 220 ? JSON.toJSONString(ret).substring(0, 200) : JSON.toJSONString(ret));
    }

}
