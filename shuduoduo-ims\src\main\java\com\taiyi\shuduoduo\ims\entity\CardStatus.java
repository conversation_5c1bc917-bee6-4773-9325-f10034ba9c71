package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_card_status")
public class CardStatus extends CommonMySqlEntity {
    private String companyId;

    private String name;

    private String color;

    private Integer type;

    /**
     * 历史信息
     */
    private String historical;

    private Long orderBy;

    private Boolean ifDeleted;

}