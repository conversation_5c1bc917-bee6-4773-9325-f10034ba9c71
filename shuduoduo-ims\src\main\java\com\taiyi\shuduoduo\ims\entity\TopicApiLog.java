package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_topic_api_log")
public class TopicApiLog extends CommonMySqlEntity {
    /**
     * 报表API ID
     */
    private String apiId;

    /**
     * 调用人
     */
    private String useBy;

    /**
     * 调用状态
     */
    private String invokeStatus;

    /**
     * 调用结果
     */
    private String invokeMsg;

}