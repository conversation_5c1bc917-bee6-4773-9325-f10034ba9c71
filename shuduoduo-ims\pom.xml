<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.taiyi</groupId>
        <artifactId>shuduoduo-v2</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>shuduoduo-ims</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>common-data-mysql</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>common-data-redis</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-ums-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-ims-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-sms-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.23</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-common-connector-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-nocodb-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.squareup.okio/okio -->
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
            <version>2.10.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.java-websocket/Java-WebSocket -->
        <dependency>
            <groupId>org.java-websocket</groupId>
            <artifactId>Java-WebSocket</artifactId>
            <version>1.3.8</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.5</version>
        </dependency>

        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.25.0-GA</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>docker-maven-plugin</artifactId>-->
<!--                <version>${docker-maven-plugin.version}</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>build-images</id>-->
<!--                        <phase>install</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <imageName>shuduoduo-v2/${project.artifactId}:${project.version}</imageName>-->
<!--                    <dockerHost>http://127.0.0.1:2375</dockerHost>-->
<!--                    <dockerDirectory>${basedir}</dockerDirectory>-->
<!--                    <resources>-->
<!--                        <resource>-->
<!--                            <targetPath>/</targetPath>-->
<!--                            <directory>${project.build.directory}</directory>-->
<!--                            <include>${project.build.finalName}.jar</include>-->
<!--                        </resource>-->
<!--                    </resources>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>

</project>
