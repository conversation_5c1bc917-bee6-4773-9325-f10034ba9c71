package com.taiyi.shuduoduo.ums.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ums.dao.UserCompanyDao;
import com.taiyi.shuduoduo.ums.entity.UserCompany;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户部门关联业务层
 *
 * <AUTHOR>
 */
@Service
public class UserCompanyService extends CommonMysqlService<UserCompanyDao, UserCompany> {
    @Autowired
    private UserCompanyDao userCompanyDao;

    @Override
    public Class<UserCompany> getEntityClass() {
        return UserCompany.class;
    }

    @Autowired
    private DepartmentService departmentService;

    /**
     * 根据公司id，用户名查找用户
     *
     * @param companyId 公司id
     * @return 用户id列表
     */
    public List<String> getCompanyUserList(String companyId) {
        QueryWrapper<UserCompany> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId);
        List<UserCompany> userCompanies = userCompanyDao.selectList(wrapper);
        if (userCompanies != null) {
            return userCompanies.stream().map(UserCompany::getUserId).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 根据公司ID、用户ID查询部门名称
     *
     * @param companyId 公司ID
     * @param userId    用户ID
     * @return 部门名称
     */
    public List<String> getDeptName(String companyId, String userId) {
        QueryWrapper<UserCompany> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId).eq("user_id", userId);
        List<UserCompany> userCompanies = userCompanyDao.selectList(wrapper);
        List<String> list = new ArrayList<>();
        for (UserCompany userCompany : userCompanies) {
            list.add(departmentService.getDeptNameById(userCompany.getDepId(), new ArrayList<>()));
        }
        return list;
    }

    public List<String> getUserListByDeptId(String companyId, String deptId) {
        QueryWrapper<UserCompany> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId).eq("dep_id", deptId);
        List<UserCompany> userCompanies = userCompanyDao.selectList(wrapper);
        List<String> list = new ArrayList<>();
        for (UserCompany userCompany : userCompanies) {
            list.add(userCompany.getUserId());
        }
        return list;
    }
}