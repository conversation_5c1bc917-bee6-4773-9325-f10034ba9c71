package com.taiyi.shuduoduo.sms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.sms.api.dto.DictDTO;
import com.taiyi.shuduoduo.sms.dao.DictDao;
import com.taiyi.shuduoduo.sms.entity.Dict;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DictService extends CommonMysqlService<DictDao, Dict> {
    @Override
    public Class<Dict> getEntityClass() {
        return Dict.class;
    }

    public List<Dict> listByType(String dictType) {
        QueryWrapper<Dict> wrapper = new QueryWrapper<>();
        wrapper.eq("dict_type", dictType)
                .orderByDesc("order_by", "create_time");
        return super.list(wrapper);
    }

    public Dict getNameByTypeAndValue(String dictType, Integer dictValue) {
        QueryWrapper<Dict> wrapper = new QueryWrapper<>();
        wrapper.eq("dict_type", dictType).eq("dict_value", dictValue).eq("if_deleted", false);
        return super.getOne(wrapper);
    }

    public Dict getValueByTypeAndName(String dictType, String dictName) {
        QueryWrapper<Dict> wrapper = new QueryWrapper<>();
        wrapper.eq("dict_type", dictType).eq("dict_name", dictName).eq("if_deleted", false);
        return super.getOne(wrapper);
    }
}