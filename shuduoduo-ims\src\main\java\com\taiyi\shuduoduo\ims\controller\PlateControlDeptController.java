package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.PlateControlDept;
import com.taiyi.shuduoduo.ims.service.PlateControlDeptService;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 板块下--管理部门控制层
 *
 * <AUTHOR>
 * @Description: 在台账中配置
 */
@RestController
@RequestMapping("/plate/control/dept")
@Validated
public class PlateControlDeptController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private PlateControlDeptService plateControlDeptService;

    /**
     * 编辑
     *
     * @param t t
     * @return T
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated PlateControlDept t) {
        boolean f;
        try {
            t.setCompanyId(CurrentUserUtil.get().getCompanyId());
            if (plateControlDeptService.exists(t)) {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.NAME_DUPLICATE);
            }
            f = plateControlDeptService.save(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 修改
     *
     * @param t
     * @return ResponseEntity
     */
    @PutMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateById(@PathVariable("id") String id, @RequestBody @Validated PlateControlDept t) {
        if (plateControlDeptService.isNameDuplicate(t, id)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.NAME_DUPLICATE);
        }
        boolean f;
        try {
            t.setId(id);
            t.setCompanyId(CurrentUserUtil.get().getCompanyId());
            f = plateControlDeptService.updateById(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 详情
     *
     * @param id id
     * @return T
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            PlateControlDept t = plateControlDeptService.getById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 删除
     *
     * @param id id
     * @return T
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = plateControlDeptService.deleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取板块下管理部门列表
     *
     * @param plateId 板块ID
     * @return T
     */
    @GetMapping("/list")
    public ResponseEntity<ResponseVo.ResponseBean> list(@RequestParam(value = "plateId", required = false) String plateId) {
        try {
            List<PlateControlDept> list = plateControlDeptService.getList(plateId);
            return ResponseVo.response(MessageCode.SUCCESS, list);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 上移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/up/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> up(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (plateControlDeptService.sequence(id, orderBy, PlateControlDept.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 下移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/down/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> down(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (plateControlDeptService.sequence(id, orderBy, PlateControlDept.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


}
