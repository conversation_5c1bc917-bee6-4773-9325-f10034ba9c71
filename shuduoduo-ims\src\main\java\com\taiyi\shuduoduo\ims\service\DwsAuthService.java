package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.DwsAuthDao;
import com.taiyi.shuduoduo.ims.entity.DwsAuth;
import com.taiyi.shuduoduo.ims.vo.DwsAuthVo;
import com.taiyi.shuduoduo.ums.api.service.RoleRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 指标权限服务
 *
 * <AUTHOR>
 */
@Service
public class DwsAuthService extends CommonMysqlService<DwsAuthDao, DwsAuth> {
    @Override
    public Class<DwsAuth> getEntityClass() {
        return DwsAuth.class;
    }

    @Autowired
    private RoleRpcService roleRpcService;

    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchDwsAuth(DwsAuthVo.InsertRequest t) {
        if (t == null || t.getDwsId() == null || t.getDwsId().isEmpty()) {
            return true;
        }

        String companyId = CurrentUserUtil.get().getCompanyId();
        List<DwsAuth> dwsAuthsToInsert = new ArrayList<>(t.getDwsId().size());
        List<DwsAuth> dwsAuthsToUpdate = new ArrayList<>(t.getDwsId().size());

        // 批量查询已存在的授权记录
        Map<String, DwsAuth> existingAuths = getExistingAuths(t.getRoleId(), t.getDwsId());

        for (String dwsId : t.getDwsId()) {
            DwsAuth dwsAuth = existingAuths.get(dwsId);
            if (dwsAuth != null) {
                dwsAuth.setIfAuthed(t.getIfAuthed());
                dwsAuthsToUpdate.add(dwsAuth);
            } else {
                dwsAuth = new DwsAuth();
                dwsAuth.setCompanyId(companyId);
                dwsAuth.setDwsId(dwsId);
                dwsAuth.setRoleId(t.getRoleId());
                dwsAuth.setIfAuthed(t.getIfAuthed());
                dwsAuthsToInsert.add(dwsAuth);
            }
        }

        boolean success = true;
        if (!dwsAuthsToUpdate.isEmpty()) {
            success &= updateBatchById(dwsAuthsToUpdate);
        } else if (!dwsAuthsToInsert.isEmpty()) {
            success &= saveBatch(dwsAuthsToInsert);
        }

        return success;
    }

    /**
     * 根据角色ID和指标ID集合批量查询角色权限
     *
     * @param roleId 角色ID
     * @param dwsIds 指标ID集合
     * @return Map
     */
    private Map<String, DwsAuth> getExistingAuths(String roleId, List<String> dwsIds) {
        QueryWrapper<DwsAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("role_id", roleId)
                .in("dws_id", dwsIds)
                .eq("if_deleted", false)
                .eq("company_id", CurrentUserUtil.get().getCompanyId());
        return list(wrapper)
                .stream()
                .collect(Collectors.toMap(DwsAuth::getDwsId, Function.identity()));
    }

    /**
     * 根据角色ID获取角色对应的DwsAuth信息
     *
     * @param roleId 角色ID
     * @return List
     */
    public List<DwsAuth> getAuthListByRoleId(String roleId) {
        QueryWrapper<DwsAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("role_id", roleId)
                .eq("if_deleted", false)
                .eq("if_authed", true)
                .eq("company_id", CurrentUserUtil.get().getCompanyId());
        return list(wrapper);
    }

    /**
     * 根据角色ID和指标ID获取DwsAuth信息
     *
     * @param roleId 角色ID
     * @param dwsId  指标ID
     * @return DwsAuth信息
     */
    public DwsAuth getByRoleIdAndDwsId(String roleId, String dwsId) {
        QueryWrapper<DwsAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("role_id", roleId)
                .eq("dws_id", dwsId)
                .eq("if_deleted", false)
                .eq("company_id", CurrentUserUtil.get().getCompanyId());
        return getOne(wrapper);
    }

    /**
     * 根据用户ID获取用户对应的DwsAuth信息
     * 用户ID在多个角色中
     * 角色和指标绑定
     */
    public Set<String> getDwsIdsByUserId(String userId) {
        // 获取用户所在的角色列表
        List<String> roleIds = roleRpcService.getRoleIdListByUserId(userId);
        if (roleIds == null || roleIds.isEmpty()) {
            return Collections.emptySet();
        }

        // 批量获取 DwsAuth 信息
        QueryWrapper<DwsAuth> wrapper = new QueryWrapper<>();
        wrapper.in("role_id", roleIds)
                .eq("if_deleted", false)
                .eq("if_authed", true)
                .eq("company_id", CurrentUserUtil.get().getCompanyId());
        List<DwsAuth> dwsAuthList = list(wrapper);

        // 提取指标ID集合
        Set<String> dwsIds = new HashSet<>();
        for (DwsAuth dwsAuth : dwsAuthList) {
            dwsIds.add(dwsAuth.getDwsId());
        }

        return dwsIds;
    }
}