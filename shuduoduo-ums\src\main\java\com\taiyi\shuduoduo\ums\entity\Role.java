package com.taiyi.shuduoduo.ums.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ums_role")
public class Role extends CommonMySqlEntity {
    /**
     * 是否内置角色，0: 否, 1:是
     */
    private Boolean ifBuiltIn;

    /**
     * 名称
     */
    private String name;

    /**
     * 角色说明
     */
    private String remark;

    /**
     * 是否删除，0: 否, 1:是
     */
    private Boolean ifDeleted;

    private String companyId;

}