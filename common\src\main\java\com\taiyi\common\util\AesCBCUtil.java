package com.taiyi.common.util;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES-256-CBC加密
 *
 * <AUTHOR>
 */
public class AesCBCUtil {

    private static final String ENCODING = "UTF-8";
    public static final String KEY_ALGORITHM = "AES/CBC/NOPADDING";
    public static final String SIGN_ALGORITHMS = "SHA-256";


    private static byte[] keyBs;

    /**
     * sha-256 key
     *
     * @param key key
     * @throws UnsupportedEncodingException e
     */
    public static void decodeSha256(String key) throws UnsupportedEncodingException {
        MessageDigest digest = null;
        try {
            digest = MessageDigest.getInstance(SIGN_ALGORITHMS);
        } catch (NoSuchAlgorithmException e) {
            // won't happen
        }
        keyBs = digest.digest(key.getBytes(ENCODING));
    }

    /**
     * 解密
     *
     * @param base64
     * @return
     * @throws Exception
     */
    public static String decrypt(String base64) throws Exception {
        byte[] decode = Base64.getDecoder().decode(base64);
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        byte[] iv = new byte[16];
        System.arraycopy(decode, 0, iv, 0, 16);
        byte[] data = new byte[decode.length - 16];
        System.arraycopy(decode, 16, data, 0, data.length);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(keyBs, KEY_ALGORITHM), new IvParameterSpec(iv));
        byte[] r = cipher.doFinal(data);
        if (r.length > 0) {
            int p = r.length - 1;
            for (; p >= 0 && r[p] <= 16; p--) {
            }
            if (p != r.length - 1) {
                byte[] rr = new byte[p + 1];
                System.arraycopy(r, 0, rr, 0, p + 1);
                r = rr;
            }
        }
        return new String(r, ENCODING);
    }

    /**
     * 加密
     *
     * @param content 加密内容
     * @return 加密后字符串
     */
    public static String encrypt(String content) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(keyBs, KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(KEY_ALGORITHM);
        byte[] byteContent = content.getBytes(ENCODING);
        byte[] iv = new byte[16];
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, new IvParameterSpec(iv));
        byte[] r = cipher.doFinal(byteContent);
        return new String(r, ENCODING);
    }
}
