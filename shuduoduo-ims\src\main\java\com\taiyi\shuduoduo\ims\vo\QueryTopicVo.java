package com.taiyi.shuduoduo.ims.vo;

import com.alibaba.fastjson.JSONArray;
import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class QueryTopicVo {

    @Data
    public static class EditTopicParam {

        private String id;

        /**
         * 台账ID
         */
        private String rawId;

        /**
         * 标题
         */
        private String subject;

        /**
         * 所选查询列
         */
        private JSONArray selectedAttr;

        /**
         * 所选筛选条件
         */
        private JSONArray selectedFilter;

        /**
         * 所选排序条件
         */
        private JSONArray selectedOrder;

        /**
         * 所选数据格式转换
         */
        private String dataParse;

        /**
         * 透视表配置
         */
        private String pivotTableConfig;

    }

    @Data
    public static class PageResultVo {

        private String id;
        /**
         * 台账ID
         */
        private String rawId;

        /**
         * 标题
         */
        private String subject;

        /**
         * 创建时间
         */
        private Date createTime;

        private String userId;

        private String username;


        /**
         * 用户头像
         */
        private String avatarUri;

        /**
         * 所选查询列
         */
        private JSONArray selectedAttr;

        /**
         * 所选筛选条件
         */
        private JSONArray selectedFilter;

        /**
         * 所选排序条件
         */
        private JSONArray selectedOrder;
    }

    @Data
    public static class QueryTopicPageVo extends CommonMySqlPageVo {

    }
}
