spring:
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
        group: DEFAULT_GROUP
        file-extension: yaml
        refresh-enabled: true
        extension-configs:
          - data-id: ${spring.application.name}-${spring.profiles.active}.yaml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        server-addr: 127.0.0.1:8848
      username: nacos
      password: ENC(b765fzbfiKxnELMx3HsbaQ==)


cas:
  enable: false