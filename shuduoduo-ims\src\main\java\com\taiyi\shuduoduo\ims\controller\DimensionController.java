package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.ClassUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.Dimension;
import com.taiyi.shuduoduo.ims.entity.ImsCommonTips;
import com.taiyi.shuduoduo.ims.entity.Plate;
import com.taiyi.shuduoduo.ims.entity.PlateLayer;
import com.taiyi.shuduoduo.ims.exceptions.DremioException;
import com.taiyi.shuduoduo.ims.service.DimensionService;
import com.taiyi.shuduoduo.ims.service.PlateLayerService;
import com.taiyi.shuduoduo.ims.service.PlateService;
import com.taiyi.shuduoduo.ims.util.SqlBuilderUtil;
import com.taiyi.shuduoduo.ims.vo.DimensionVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyDremioRpcService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 维度控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dimension")
@Validated
public class DimensionController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * 线程池
     */
    private static final ExecutorService executor = new ThreadPoolExecutor(5, 10,
            3, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(20));

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private CompanyDremioRpcService dremioRpcService;

    @Autowired
    private PlateService plateService;

    @Autowired
    private PlateLayerService plateLayerService;

    /**
     * 维度/事实表-新增
     *
     * @param addDimension 维度类
     * @return ResponseEntity<ResponseVo.ResponseBean>
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated DimensionVo.AddDimension addDimension) {
        //检查重名
        Dimension dimension = BeanUtil.copy(addDimension, Dimension.class);
        if (dimensionService.isNameDuplicate(dimension, dimension.getName())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.NAME_DUPLICATE);
        }

        if (dimensionService.isCodeDuplicate(dimension, dimension.getCode())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.CODE_DUPLICATE);
        }

        if (dimensionService.isCodeLegal(dimension.getCode())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ImsCommonTips.CODE_LEGAL);
        }

        if (ObjectUtil.isNotEmpty(addDimension.getAttrList())) {
            if (dimensionService.isCodeLegal1(addDimension.getAttrList())) {
                return ResponseVo.response(MessageCode.REQUEST_ERROR, ImsCommonTips.CODE_LEGAL);
            }
        }

        if (null == addDimension.getAttrList()) {
            addDimension.setAttrList(new ArrayList());
        }

        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, dimensionService.addDimension(addDimension));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 维度/事实表-编辑
     *
     * @param id           id
     * @param addDimension 对象信息
     * @return T
     */
    @PutMapping("{id}")
    public ResponseEntity<ResponseVo.ResponseBean> edit(@PathVariable("id") String id, @RequestBody @Validated DimensionVo.AddDimension addDimension) {
        addDimension.setId(id);
        //检查重名
        Dimension dimension = BeanUtil.copy(addDimension, Dimension.class);
        if (dimensionService.isNameDuplicate(dimension, dimension.getName())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.NAME_DUPLICATE);
        }

        if (dimensionService.isCodeDuplicate(dimension, dimension.getCode())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.CODE_DUPLICATE);
        }

        if (dimensionService.isCodeLegal(dimension.getCode())) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ImsCommonTips.CODE_LEGAL);
        }

        if (ObjectUtil.isNotEmpty(addDimension.getAttrList())) {
            if (dimensionService.isCodeLegal1(addDimension.getAttrList())) {
                return ResponseVo.response(MessageCode.REQUEST_ERROR, ImsCommonTips.CODE_LEGAL);
            }
        }

        try {
            if (dimensionService.editDimension(addDimension)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 维度/事实表-删除
     *
     * @param id 维度/事实表-id
     * @return ResponseEntity<ResponseVo.ResponseBean>
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        try {
            if (dimensionService.delete(id)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }

    }

    /**
     * 维度/事实表-详情
     *
     * @param id 维度/事实表-id
     * @return ResponseEntity<ResponseVo.ResponseBean>
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> detail(@PathVariable("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.detail(id));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 维度/事实表-分页查询
     *
     * @param pageVo 分页参数
     * @return ResponseEntity<ResponseVo.ResponseBean>
     */
    @PostMapping("/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody @Validated DimensionVo.PageVo pageVo) {
        if (!ClassUtil.containsField(dimensionService.getEntityClass(), pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getColumn())) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.SELECTED_FIELD_NOT_ALLOWED);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.myPage(pageVo));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 维度/事实表-分页查询
     *
     * @param pageVo 分页参数
     * @return ResponseEntity<ResponseVo.ResponseBean>
     */
    @PostMapping("/model/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> dataModelPage(@RequestBody @Validated DimensionVo.DataModelPageVo pageVo) {
        if (!ClassUtil.containsField(dimensionService.getEntityClass(), pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getColumn())) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.SELECTED_FIELD_NOT_ALLOWED);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.getPageByDataModel(pageVo));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据类型查询维度/事实表列表
     *
     * @param type    板块ID
     * @param keyWord 关键字搜索
     * @return 维度列表
     */
    @GetMapping("/getByType/{type}")
    public ResponseEntity<ResponseVo.ResponseBean> getByType(@PathVariable("type") @NotBlank String type, @RequestParam("keyWord") String keyWord) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.getListByType(Integer.valueOf(type), keyWord, CurrentUserUtil.get().getCompanyId()));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 上移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/up/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> up(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (dimensionService.sequence(id, orderBy, Dimension.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 下移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/down/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> down(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (dimensionService.sequence(id, orderBy, Dimension.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 表、表字段同步
     *
     * @param dimId 表ID
     * @return T
     */
    @PostMapping("/syncData/{dimId}")
    public ResponseEntity<ResponseVo.ResponseBean> syncData(@PathVariable("dimId") String dimId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.syncDimension(dimId, CurrentUserUtil.get().getCompanyId()));
        } catch (Exception e) {
            logger.error("开通数据失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 获取统计周期列表
     *
     * @return T
     */
    @GetMapping("/period")
    public ResponseEntity<ResponseVo.ResponseBean> periodList() {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.getPeriodList());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 获取统计周期名称
     *
     * @param dictValue 值
     * @return T
     */
    @GetMapping("/period/{dictValue}")
    public ResponseEntity<ResponseVo.ResponseBean> getPeriodByValue(@PathVariable("dictValue") @Validated @NotNull String dictValue) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.getPeriodByValue(dictValue));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    @GetMapping("/getList/{foreignId}")
    public ResponseEntity<ResponseVo.ResponseBean> getListByForeignIdAndType(@RequestParam("type") @Validated @NotBlank String type, @PathVariable String foreignId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.getListByForeignIdAndType(foreignId, Integer.valueOf(type)));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 模型检查--修改检查状态
     *
     * @param field 检查参数
     * @return T
     */
    @PostMapping("/updateCheckStatus")
    public ResponseEntity<ResponseVo.ResponseBean> updateCheckStatus(@RequestBody @Validated DimensionVo.CheckField field) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.updateCheckStatus(field));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 模型检查
     *
     * @param field 检查参数
     * @return T
     */
    @PostMapping("/checkFields")
    public ResponseEntity<ResponseVo.ResponseBean> checkFields(@RequestBody @Validated DimensionVo.CheckField field) {
        CompanyDremioDTO dremioDTO = dremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == dremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        String companyId = CurrentUserUtil.get().getCompanyId();
        try {
            executor.execute(() -> dimensionService.checkFields(field, dremioDTO, companyId));
            return ResponseVo.response(MessageCode.SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 根据板块获取维度表列表
     *
     * @param plateId 板块ID
     * @return T
     */
    @GetMapping("/tableList/{plateId}")
    public ResponseEntity<ResponseVo.ResponseBean> getListByPlateId(@PathVariable String plateId, @RequestParam(value = "keyWord", required = false) String keyWord) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.getDimListByUnionId(plateId, 1, keyWord));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 获取维度字段列表
     *
     * @param id
     * @return
     */
    @GetMapping("/optional/list/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> optionalList(@PathVariable("id") String id) {
        Dimension dimension = dimensionService.getById(id);
        if (null == dimension) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DIMENSION_DETAIL_NOT_EXIST);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.optionListByDim(dimension));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @PostMapping("/create/view/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> checkFields(@PathVariable("id") String id) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        CompanyDremioDTO dremioDTO = dremioRpcService.getCompanyById(companyId);
        if (null == dremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.syncDimension(id, companyId));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 获取SQL
     *
     * @param id 维度ID
     * @return
     */
    @GetMapping("/sync/sql/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getDremioSqlByDimension(@PathVariable("id") String id) {
        Dimension dimension = dimensionService.getById(id);
        if (null == dimension) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DIMENSION_DETAIL_NOT_EXIST);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, dimensionService.getDremioSqlByDimId(dimension, CurrentUserUtil.get().getCompanyId()));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 同步sql到数据湖
     *
     * @param id 维度/事实表ID
     * @return bool
     */
    @PostMapping("/sync/sql/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> syncSqlToDremio(@PathVariable("id") String id, @RequestBody DimensionVo.SqlParam param) {
        Dimension dimension = dimensionService.getById(id);
        if (null == dimension) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DIMENSION_DETAIL_NOT_EXIST);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, dimensionService.syncSqlToDremio(dimension, CurrentUserUtil.get().getCompanyId(), param.getSql()));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 检测虚拟数据湖有无此表
     *
     * @param id 维度ID
     * @return bool
     */
    @GetMapping("/check/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> checkDremioHasThisDimension(@PathVariable("id") @Validated String id) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Dimension dimension = dimensionService.getById(id);
        if (null == dimension) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DIMENSION_DETAIL_NOT_EXIST);
        }
        Plate plate = plateService.getById(dimension.getPlateId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.PLATE_NOT_FOUND);
        }
        PlateLayer plateLayer = plateLayerService.getById(dimension.getPlateLayerId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.PLATE_LAYER_NOT_FOUND);
        }
        CompanyDremioDTO dremioDTO = dremioRpcService.getCompanyById(companyId);
        if (null == dremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, dimensionService.checkDremioHasThisDimension(dimension, plate, plateLayer, dremioDTO));
        } catch (DremioException e) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, e.getMessage());
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 搜索
     *
     * @param query 搜索参数
     * @return T
     */
    @PostMapping("/search/list")
    public ResponseEntity<ResponseVo.ResponseBean> searchList(@RequestBody @Validated DimensionVo.SearchParam query) {
        if (query != null && StringUtils.isNotBlank(query.getKeyWord())) {
            if (SqlBuilderUtil.sqlValidate(query.getKeyWord()) || query.getKeyWord().contains("'")) {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.FILTER_PARAM_NOT_ALLOWED);
            }
        }
        try {
            assert query != null;
            List<Map<String, Object>> list = dimensionService.getSearchDimensionList(query, CurrentUserUtil.get().getCompanyId());
            return ResponseVo.response(MessageCode.SUCCESS, list);
        } catch (Exception e) {
            logger.error("维度表字段搜索失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取数据湖虚拟表列字段
     *
     * @param id 维度ID
     * @return List
     */
    @GetMapping("/view/fields/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getViewFields(@PathVariable("id") @Validated String id) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Dimension dimension = dimensionService.getById(id);
        if (null == dimension) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DIMENSION_DETAIL_NOT_EXIST);
        }
        if (StringUtils.isBlank(dimension.getDatasetId())) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DREMIO_VIEW_NOT_EXIST);
        }
        Plate plate = plateService.getById(dimension.getPlateId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.PLATE_NOT_FOUND);
        }
        PlateLayer plateLayer = plateLayerService.getById(dimension.getPlateLayerId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.PLATE_LAYER_NOT_FOUND);
        }
        CompanyDremioDTO dremioDTO = dremioRpcService.getCompanyById(companyId);
        if (null == dremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, dimensionService.getViewFields(dimension, dremioDTO));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 更新表文档
     *
     * @param id  表ID
     * @param doc 文档
     * @return T
     */
    @PatchMapping("/doc/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateDimensionDoc(@PathVariable("id") @Validated String id, @RequestBody DimensionVo.DocParam doc) {
        Dimension dimension = dimensionService.getById(id);
        if (null == dimension) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DIMENSION_DETAIL_NOT_EXIST);
        }
        try {
            if (dimensionService.updateDoc(id, doc.getDoc())) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }
}
