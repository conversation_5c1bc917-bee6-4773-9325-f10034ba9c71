package com.taiyi.shuduoduo.ims.kestra.service;

import com.taiyi.shuduoduo.ims.kestra.KestraResponse;
import com.taiyi.shuduoduo.ims.kestra.api.KestraApi;
import com.taiyi.shuduoduo.ims.kestra.vo.KestraVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Dremio API 业务层
 *
 * <AUTHOR>
 */
@Service
public class KestraApiService {


    @Autowired
    private KestraApi kestraApi;

    /**
     * 执行流程
     *
     * @param webHookUri uri
     */
    public KestraResponse executeFlow(String webHookUri) {
        return kestraApi.executeFlow(webHookUri, null);
    }

    public KestraResponse updateFlow(String namespace, String flowId, String flow) {
        return kestraApi.updateFlow(namespace, flowId, flow);
    }

    public KestraResponse searchFlow(String namespace, String flowId) {
        return kestraApi.searchFlow(namespace, flowId);
    }

    public KestraResponse flowList(String namespace) {
        return kestraApi.flowList(namespace);
    }

    public KestraResponse createFlow(String flow) {
        return kestraApi.createFlow(flow);
    }

    public KestraResponse executeFlowByApi(KestraVo vo) {
        return kestraApi.executeFlowByApi(vo);
    }

    public KestraResponse getExecutionStatus(String executionId) {
        return kestraApi.getExecutionStatus(executionId);
    }

    public KestraResponse getPluginList() {
        return kestraApi.getPluginList();
    }

    public KestraResponse getExecuteLog(String executionId) {
        return kestraApi.getExecuteLog(executionId);
    }

    public KestraResponse validFlow(String flow) {
        return kestraApi.validFlow(flow);
    }

    public KestraResponse getExecuteInfo(String executionId) {
        return kestraApi.getExecuteInfo(executionId);
    }

    public KestraResponse getOutputData(String executionId, String path) {
        return kestraApi.getOutputData(executionId, path);
    }
}
