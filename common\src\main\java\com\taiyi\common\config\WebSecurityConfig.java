package com.taiyi.common.config;

import cn.hutool.http.ContentType;
import com.alibaba.fastjson.JSON;
import com.taiyi.common.entity.HttpHeaderConstant;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.ResponseEntity;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

/**
 * 安全配置类
 *
 * <AUTHOR>
 */
@Configuration
public class WebSecurityConfig implements WebMvcConfigurer {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Bean
    public GlobalInterceptor getInterceptor() {
        return new GlobalInterceptor();
    }

    /**
     * 添加拦截器
     *
     * @param registry *
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(getInterceptor())
                .addPathPatterns("/**")
                // 忽略这些路径 以便admin-client注册
                .excludePathPatterns("/instances/**")
                .excludePathPatterns("/actuator/**");
    }

    /**
     * 全局拦截器
     */
    public class GlobalInterceptor extends HandlerInterceptorAdapter {

        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
            String requestId = request.getHeader(HttpHeaderConstant.REQUEST_ID);
            long start = System.currentTimeMillis();
            String token = request.getHeader(HttpHeaderConstant.HEADER_TOKEN);
            boolean verify = JwtUtil.verifyToken(token);
            if (!verify) {
                if (isExcludeRequest(request)) {
                    return super.preHandle(request, response, handler);
                } else {
                    // 无权限返回
                    noAuthResponse(response);
                    return false;
                }
            }

            // 解析 token 添加currentUser
            CurrentUserUtil.CurrentUser user = JwtUtil.decodeToken(token);
            logger.debug("request:{}, decode jwt token: {}, url: {}, method: {}",
                    requestId, user.toString(), request.getRequestURI(), request.getMethod());

            CurrentUserUtil.put(user);
            boolean flag = super.preHandle(request, response, handler);
            logger.debug("request:{} path: {}，method:{}，花费时间：{}ms",
                    requestId, request.getRequestURI(), request.getMethod(), (System.currentTimeMillis() - start));
            return flag;
        }

        @Override
        public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
            CurrentUserUtil.remove();
            super.afterCompletion(request, response, handler, ex);
        }

        /**
         * 无权限返回
         *
         * @param response response
         * @throws IOException e
         */
        private void noAuthResponse(HttpServletResponse response) throws IOException {
            ResponseEntity responseEntity = ResponseVo.response(MessageCode.NO_AUTHORITY);
            response.setStatus(responseEntity.getStatusCodeValue());
            response.setContentType(ContentType.JSON.getValue());
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(JSON.toJSONString(responseEntity.getBody()));
        }

        /**
         * 校验请求路径是否被允许
         *
         * @param request 请求
         * @return bool
         */
        private boolean isExcludeRequest(HttpServletRequest request) {
            String path = request.getContextPath() + request.getServletPath();
            logger.info("request path: {}", path);
            Set<String> allowPaths = new HashSet<>();

            allowPaths.addAll(PathConfig.rpcPathList());
            allowPaths.addAll(PathConfig.authAllowPathList());

            AntPathMatcher matcher = new AntPathMatcher();
            for (String allowPath : allowPaths) {
                boolean flag = matcher.match(allowPath, path);
                if (flag) {
                    return true;
                }
            }
            return false;
        }

    }
}
