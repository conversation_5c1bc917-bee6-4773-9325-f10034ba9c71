package com.taiyi.common.connector.util;

/**
 * IP地址工具类
 *
 * <AUTHOR>
 */
public class IpUtil {

    /**
     * 校验IP是否为内网IP
     *
     * @param ip 需要验证的IP地址
     * @return boolean
     */
    public static boolean ipIsValid(String ip) {
        return ("10.").equals(ip.substring(0, 3)) || ("172.").equals(ip.substring(0, 4)) || ("192.").equals(ip.substring(0, 4)) || ("127.").equals(ip.substring(0, 4));
    }

}
