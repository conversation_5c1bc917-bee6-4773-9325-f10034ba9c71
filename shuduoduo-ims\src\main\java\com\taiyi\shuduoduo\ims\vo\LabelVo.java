package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.Date;

@Data
public class LabelVo {

    private String id;

    /**
     * 标签编码
     */
    private String code;

    /**
     * 标签名称
     */
    private String name;

    private Integer type;

    private String description;

    /**
     * 排序
     */
    private Long orderBy;

    private String createdBy;

    private String updatedBy;

    private String createUserName;

    private String updateUserName;

    /**
     * 创建时间
     */
    public Date createTime;

    /**
     * 最后更新时间
     */
    public Date updateTime;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

    @Data
    public static class LabelPageVo extends CommonMySqlPageVo {

        private Integer type;

    }

    @Data
    public static class PageResponseVo {
        private String id;


        /**
         * 标签名称
         */
        private String name;

        private Integer type;

        private String description;

        /**
         * 排序
         */
        private Long orderBy;

        private String createdBy;

        private String updatedBy;

        private String createUserName;

        private String updateUserName;

        /**
         * 创建时间
         */
        public Date createTime;

        /**
         * 最后更新时间
         */
        public Date updateTime;

    }

    @Data
    public static class LabelListVo {

        private Integer type;

        /**
         * 标签名称
         */
        private String keyWord;
    }

    @Data
    public static class ListResponse {
        private String id;

        /**
         * 标签名称
         */
        private String name;

        private Integer type;

        private String description;

        /**
         * 排序
         */
        private Long orderBy;
    }

    @Data
    public static class DwsDetail {
        /**
         * 关联ID
         */
        private String unionId;
        /**
         * 指标ID
         */
        private String dwsId;
        /**
         * 标签ID
         */
        private String labelId;
        /**
         * 指标/标签名称
         */
        private String name;
        /**
         * 标签类型
         */
        private Integer type;
        /**
         * 用户名
         */
        private String userName;

        /**
         * 创建时间
         */
        public Date createTime;

        /**
         * 最后更新时间
         */
        public Date updateTime;
    }
}
