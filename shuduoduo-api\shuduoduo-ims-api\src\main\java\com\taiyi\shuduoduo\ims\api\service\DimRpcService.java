package com.taiyi.shuduoduo.ims.api.service;

import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.ims.api.dto.DimDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 维度RPC
 *
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoIms.SERVER_NAME)
public interface DimRpcService {

    /**
     * 根据板块ID获取维度列表
     *
     * @return 维度列表
     */
    @GetMapping(value = MicroServer.ShuduoduoIms.SERVER_PREFIX + "/rpc/dim/getDimListByPlateId")
    List<DimDTO> getDimListByPlateId(@RequestParam("plateId") String plateId);
}
