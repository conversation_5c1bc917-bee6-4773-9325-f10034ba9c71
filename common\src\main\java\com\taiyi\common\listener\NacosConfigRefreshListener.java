package com.taiyi.common.listener;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.listener.Listener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Nacos配置动态监听器
 *
 * <AUTHOR>
 */
@Component
@RefreshScope
public class NacosConfigRefreshListener implements ApplicationListener<ApplicationReadyEvent> {

    private static final Logger log = LoggerFactory.getLogger(NacosConfigRefreshListener.class);

    @Autowired
    private ConfigurableApplicationContext context;

    @Value("${spring.cloud.nacos.config.server-addr}")
    private String serverAddr;

    @Value("${spring.application.name}")
    private String appName;

    @Value("${spring.profiles.active}")
    private String profile;

    @PostConstruct
    public void init() {
        log.info("[配置监听] Nacos配置动态监听器已加载");
    }

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        try {
            ConfigService configService = NacosFactory.createConfigService(serverAddr);
            String dataId = appName + "-" + profile + ".yaml";
            String group = "DEFAULT_GROUP";

            configService.addListener(dataId, group, new Listener() {
                @Override
                public Executor getExecutor() {
                    return Executors.newSingleThreadExecutor();
                }

                @Override
                public void receiveConfigInfo(String configInfo) {
                    log.info("[配置监听] Nacos配置变更，开始处理...");
                    // 分发事件
                    context.publishEvent(new NacosConfigUpdateEvent(configInfo));
                }
            });
        } catch (Exception e) {
            log.error("[配置监听] Nacos监听失败", e);
        }
    }

    public static class NacosConfigUpdateEvent extends ApplicationEvent {

        public NacosConfigUpdateEvent(String yamlContent) {
            super(yamlContent);
        }

        public String getContent() {
            return (String) getSource();
        }
    }
}
