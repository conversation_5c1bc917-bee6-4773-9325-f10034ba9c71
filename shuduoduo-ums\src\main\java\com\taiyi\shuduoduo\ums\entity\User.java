package com.taiyi.shuduoduo.ums.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

import java.util.Date;

/**
 * 用户实体类
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ums_user")
public class User extends CommonMySqlEntity {

    private String companyId;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 性别，0: 男 1: 女
     */
    private Boolean gender;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 是否为管理员
     */
    private Boolean ifAdmin;

    /**
     * 是否为后台账号
     */
    private Boolean ifBackend;

    /**
     * 是否锁定
     */
    private Boolean ifLock;

    /**
     * 手机号
     */
    private String mobile;
    private String openId;

    private String thirdUserId;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 登录密码摘要
     */
    private String pwdDigest;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 用户头像
     */
    private String avatarUri;

    /**
     * 账号状态
     */
    private Integer empStatus;

    /**
     * 数据湖密码摘要
     */
    private String dremioPwdDigest;

}