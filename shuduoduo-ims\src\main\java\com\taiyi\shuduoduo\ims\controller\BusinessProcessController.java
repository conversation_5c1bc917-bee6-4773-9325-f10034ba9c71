package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.BusinessProcess;
import com.taiyi.shuduoduo.ims.service.BusinessProcessService;
import com.taiyi.shuduoduo.ims.service.DimensionService;
import com.taiyi.shuduoduo.ims.service.DwsService;
import com.taiyi.shuduoduo.ims.service.RawService;
import com.taiyi.shuduoduo.ims.vo.BusinessProcessVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 业务过程控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/business/process")
@Validated
public class BusinessProcessController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private BusinessProcessService businessProcessService;

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private RawService rawService;

    @Autowired
    private DwsService dwsService;

    /**
     * 新增数据
     *
     * @param addBusinessProcess 业务过程
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated BusinessProcessVo.AddBusinessProcess addBusinessProcess) {
        BusinessProcess businessProcess = BeanUtil.copy(addBusinessProcess, BusinessProcess.class);
        if (businessProcessService.isNameExists(businessProcess)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.NAME_DUPLICATE);
        }
        if (businessProcessService.isCodeExists(businessProcess)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.CODE_DUPLICATE);
        }

        boolean f;
        try {
            f = businessProcessService.addBusinessProcess(businessProcess);
        } catch (Exception e) {
            logger.error("新增业务过程失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 修改数据
     *
     * @param id                 id
     * @param addBusinessProcess 业务过程
     * @return bool
     */
    @PutMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateById(@PathVariable("id") String id, @RequestBody @Validated BusinessProcessVo.AddBusinessProcess addBusinessProcess) {
        BusinessProcess businessProcess = BeanUtil.copy(addBusinessProcess, BusinessProcess.class);
        boolean f;
        try {
            businessProcess.setId(id);
            f = businessProcessService.addBusinessProcess(businessProcess);
        } catch (Exception e) {
            logger.error("修改业务过程失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据ID删除业务过程
     *
     * @param id id
     * @return bool
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        // 校验板块下是否有维度
        if (ObjectUtil.isNotEmpty(dimensionService.getDimListByUnionId(id, 1, null))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_DIMENSION_LIST_IN_THIS_PLATE);
        }
        // 校验板块下是否有台账
        if (ObjectUtil.isNotEmpty(rawService.getListByUnionIdAndType(id, 1, null))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_RAW_LIST_IN_THIS_PLATE);
        }
        // 校验板块下是否有指标
        if (ObjectUtil.isNotEmpty(dwsService.getList(id, 1, null))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_METRIC_LIST_IN_THIS_PLATE);
        }
        boolean f;
        try {
            f = businessProcessService.logicDeleteById(id);
        } catch (Exception e) {
            logger.error("删除业务过程失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 分页查询
     *
     * @param pageVo 分页参数
     * @return T
     */
    @PostMapping("/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody @Validated BusinessProcessVo.PageVo pageVo) {
        try {
            PageResult page = businessProcessService.myPage(pageVo);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            logger.error("分页查询业务过程失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e);
        }
    }

    /**
     * 通过数据域联动查询业务过程
     *
     * @param dataFieldId 数据域id
     * @return 返回业务过程列表
     */
    @GetMapping("/queryBusinessProcess")
    public ResponseEntity<ResponseVo.ResponseBean> queryProcessPlate(@RequestParam String dataFieldId) {
        try {
            List<Map<String, String>> list = businessProcessService.queryProcessPlate(dataFieldId, CurrentUserUtil.get().getCompanyId());
            return ResponseVo.response(MessageCode.SUCCESS, list);
        } catch (Exception e) {
            logger.error("查询业务过程失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, "查询异常");
        }
    }

    /**
     * 通过数据域联动查询业务过程
     *
     * @param dataFieldId 数据域id
     * @return 返回业务过程列表
     */
    @GetMapping("/dws/queryBusinessProcess")
    public ResponseEntity<ResponseVo.ResponseBean> queryProcessPlateByDws(@RequestParam String dataFieldId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, businessProcessService.queryProcessAndDwsCountByDataFieldId(dataFieldId));
        } catch (Exception e) {
            logger.error("查询业务过程失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, "查询异常");
        }
    }

    /**
     * 详情
     *
     * @param id id
     * @return T
     */
    @GetMapping("{id}")
    public ResponseEntity<ResponseVo.ResponseBean> detail(@PathVariable("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, businessProcessService.detail(id));
        } catch (Exception e) {
            logger.error("详情业务过程失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 上移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/up/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> up(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (businessProcessService.sequence(id, orderBy, BusinessProcess.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            logger.error("上移业务过程失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 下移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/down/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> down(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (businessProcessService.sequence(id, orderBy, BusinessProcess.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            logger.error("下移业务过程失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
