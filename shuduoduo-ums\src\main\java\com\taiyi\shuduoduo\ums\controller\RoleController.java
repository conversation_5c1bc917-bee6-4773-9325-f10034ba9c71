package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.CommonTips;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ums.entity.Role;
import com.taiyi.shuduoduo.ums.vo.RoleVo;
import com.taiyi.shuduoduo.ums.service.RoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 角色
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/role")
@Validated
public class RoleController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private RoleService roleService;

    /**
     * 新增数据
     *
     * @param t t
     * @return bool
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated RoleVo t) {
        boolean f;
        // 判重
        if (roleService.exists(BeanUtil.copy(t, Role.class))) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, CommonTips.HAS_ROLE_IN_COMPANY);
        }
        try {
            f = roleService.save(BeanUtil.copy(t, Role.class));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 修改数据
     *
     * @param id 角色ID
     * @param t  角色信息
     * @return bool
     */
    @PutMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateById(@PathVariable("id") String id, @RequestBody @Validated RoleVo t) {
        boolean f;
        try {
            f = roleService.updateById(id, BeanUtil.copy(t, Role.class));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 删除数据
     *
     * @param id 角色ID
     * @return bool
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = roleService.logicDeleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 分页查询角色列表
     *
     * @param param 分页参数
     * @return ResponseEntity
     */
    @PostMapping("/page")
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody(required = false) RoleVo.PageParam param) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, roleService.selectPage(param));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 查询角色列表
     *
     * @param keyWord 参数
     * @return ResponseEntity
     */
    @GetMapping("/list")
    public ResponseEntity<ResponseVo.ResponseBean> list(@RequestParam("keyWord") String keyWord) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, roleService.getList(keyWord));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
