package com.taiyi.common.data.mysql.entity;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <AUTHOR>
 */
public interface Query {

    /**
     * 返回 MP 需要的查询排序条件
     *
     * @return
     */
    public QueryWrapper getQueryWrapper();


    /**
     * 获取 MP 需要的分页信息
     *
     * @return
     */
    public Page getPage();

}
