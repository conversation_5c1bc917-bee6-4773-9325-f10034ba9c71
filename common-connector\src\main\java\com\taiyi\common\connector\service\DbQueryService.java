package com.taiyi.common.connector.service;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.taiyi.common.entity.DbType;
import com.taiyi.common.connector.entity.DbVo;
import com.taiyi.common.connector.netty.server.NettyServer;
import com.taiyi.common.connector.netty.vo.DataEngineMsg;
import com.taiyi.common.connector.netty.vo.MsgType;
import com.taiyi.common.connector.query.*;
import com.taiyi.common.connector.util.IpUtil;
import com.taiyi.common.connector.util.SyncFutureUtil;
import com.taiyi.common.data.redis.util.RedisRepository;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Component
public class DbQueryService {

    private static final Logger logger = LoggerFactory.getLogger(DbQueryService.class);

    private static final int EXPORT_EXPIRE_TIME = 5 * 60;

    /**
     * 引擎端口
     */
    private static final int PORT = 44460;

    @Value("${server.port}")
    private int port;

    @Autowired
    private RedisRepository repository;

    @Autowired
    NettyServer nettyServer;


    /**
     * 缓存接口这里是LoadingCache，LoadingCache在缓存项不存在时可以自动加载缓存
     */
    private static LoadingCache<String, SyncFutureUtil<DataEngineMsg>> futureCache = CacheBuilder.newBuilder()
            //设置缓存容器的初始容量为100
            .initialCapacity(100)
            // maximumSize 设置缓存大小
            .maximumSize(1000)
            //设置并发级别为20，并发级别是指可以同时写缓存的线程数
            .concurrencyLevel(50)
            // expireAfterWrite设置写缓存后5秒钟过期
            .expireAfterWrite(5, TimeUnit.SECONDS)
            //设置缓存的移除通知
            .removalListener(notification -> logger.debug("LoadingCache: {} was removed, cause is {}", notification.getKey(), notification.getCause()))
            //build方法中可以指定CacheLoader，在缓存不存在时通过CacheLoader的实现自动加载缓存
            .build(new CacheLoader<String, SyncFutureUtil<DataEngineMsg>>() {
                @Override
                public SyncFutureUtil load(String key) throws Exception {
                    // 当获取key的缓存不存在时，不需要自动添加
                    return null;
                }
            });

    /**
     * 数据库测试连接
     *
     * @param dbDto 连接信息
     * @return boolean
     */
    public boolean testConnect(DbDto dbDto) {
        boolean result = false;
        if (IpUtil.ipIsValid(dbDto.getHost())) {
            DataEngineMsg msg = this.sendMessage(BeanUtil.copy(dbDto, DbVo.class), MsgType.CONNECT, "connect", false);
            logger.warn("引擎返回参数{}", consoleLog(msg));
            if (msg == null) {
                result = false;
            } else if (!"True".equalsIgnoreCase(msg.getResponse().toString()) && !"False".equalsIgnoreCase(msg.getResponse().toString())) {
                result = false;
            } else {
                result = (Boolean) msg.getResponse();
            }
        } else {
            DbQuery dbQuery = null;
            if (dbDto.getType().equalsIgnoreCase(DbType.POSTGRE.toString())) {
                dbQuery = new PostgreQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.MYSQL.toString())) {
                dbQuery = new MysqlQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.ORACLE.toString())) {
                dbQuery = new OracleQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.INCEPTOR.toString())) {
                dbQuery = new InceptorQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.DREMIO.toString())) {
                dbQuery = new DremioQuery(dbDto);
            } else {
                //TODO... 其他数据库
            }
            if (null != dbQuery) {
                result = dbQuery.getConnection();
            }
        }
        return result;
    }

    /**
     * 数据库连接
     *
     * @param dbDto 连接信息
     * @return boolean
     */
    public boolean connect(DbDto dbDto) {
        return testConnect(dbDto);
    }

    /**
     * 获取数据库表
     *
     * @param dbDto 连接信息
     * @return List
     */
    public List<String> getTables(DbDto dbDto) {
        List<String> result = new ArrayList<>();
        if (IpUtil.ipIsValid(dbDto.getHost())) {
            DataEngineMsg msg = this.sendMessage(BeanUtil.copy(dbDto, DbVo.class), MsgType.QUERY, "getTables", result);
            logger.warn("引擎返回参数{}", consoleLog(msg));
            if (this.checkResponse(msg)) {
                result = (List<String>) msg.getResponse();
            }
        } else {
            DbQuery dbQuery = null;
            if (dbDto.getType().equalsIgnoreCase(DbType.POSTGRE.toString())) {
                //查询表时,如未指定schema,则返回空
                if (StringUtils.isBlank(dbDto.getSchema())) {
                    return result;
                }
                dbQuery = new PostgreQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.MYSQL.toString())) {
                dbQuery = new MysqlQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.ORACLE.toString())) {
                dbQuery = new OracleQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.INCEPTOR.toString())) {
                dbQuery = new InceptorQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.DREMIO.toString())) {
                dbQuery = new DremioQuery(dbDto);
            } else {
                //TODO... 其他数据库
            }
            try {
                if (null != dbQuery) {
                    result = dbQuery.getTables();
                }
            } catch (SQLException e) {
                logger.error(ExceptionUtil.stacktraceToString(e));
            }
        }
        return result;
    }

    /**
     * 获取表字段
     *
     * @param dbDto 连接信息&表名
     * @return List
     */
    public List<String> getColumns(DbDto.DbQuery dbDto) {
        List<String> result = new ArrayList<>();
        if (IpUtil.ipIsValid(dbDto.getHost())) {
            DataEngineMsg msg = this.sendMessage(BeanUtil.copy(dbDto, DbVo.class), MsgType.QUERY, "getColumns", result);
            logger.warn("引擎返回参数{}", consoleLog(msg));
            if (this.checkResponse(msg)) {
                result = (List<String>) msg.getResponse();
            }
        } else {
            DbQuery dbQuery = null;
            if (dbDto.getType().equalsIgnoreCase(DbType.POSTGRE.toString())) {
                dbQuery = new PostgreQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.MYSQL.toString())) {
                dbQuery = new MysqlQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.ORACLE.toString())) {
                dbQuery = new OracleQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.INCEPTOR.toString())) {
                dbQuery = new InceptorQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.DREMIO.toString())) {
                dbQuery = new DremioQuery(dbDto);
            } else {
                //TODO... 其他数据库
            }
            try {
                if (null != dbQuery) {
                    result = dbQuery.getColumns(dbDto.getTableName());
                }
            } catch (SQLException e) {
                logger.error(ExceptionUtil.stacktraceToString(e));
            }
        }
        return result;
    }

    /**
     * 获取数据库表字段及字段类型
     *
     * @param dbDto 连接信息&表名
     * @return List
     */
    public List<Map<String, Object>> getColumnsAndType(DbDto.DbQuery dbDto) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (IpUtil.ipIsValid(dbDto.getHost())) {
            DataEngineMsg msg = this.sendMessage(BeanUtil.copy(dbDto, DbVo.class), MsgType.QUERY, "getColumnsAndType", result);
            logger.warn("引擎返回参数{}", consoleLog(msg));
            if (this.checkResponse(msg)) {
                result = (List<Map<String, Object>>) msg.getResponse();
            }
        } else {
            DbQuery dbQuery = null;
            if (dbDto.getType().equalsIgnoreCase(DbType.POSTGRE.toString())) {
                dbQuery = new PostgreQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.MYSQL.toString())) {
                dbQuery = new MysqlQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.ORACLE.toString())) {
                dbQuery = new OracleQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.INCEPTOR.toString())) {
                dbQuery = new InceptorQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.DREMIO.toString())) {
                dbQuery = new DremioQuery(dbDto);
            } else {
                //TODO... 其他数据库
            }
            try {
                if (null != dbQuery) {
                    result = dbQuery.getColumnsAndType(dbDto.getTableName());
                }
            } catch (SQLException sqlException) {
                logger.error(ExceptionUtil.stacktraceToString(sqlException));
            }
        }
        return result;
    }

    /**
     * 数据查询
     *
     * @param dbDto 数据库连接参数&表名&条件参数
     * @return List
     */
    public List<Map<String, Object>> query(DbDto.DbQuery dbDto) {
        List<Map<String, Object>> result = new ArrayList<>();
        if (IpUtil.ipIsValid(dbDto.getHost())) {
            DataEngineMsg msg = this.sendMessage(BeanUtil.copy(dbDto, DbVo.class), MsgType.QUERY, "query", result);
            logger.warn("引擎返回参数{}", consoleLog(msg));
            if (this.checkResponse(msg)) {
                result = (List<Map<String, Object>>) msg.getResponse();
            }
        } else {
            DbQuery dbQuery = null;
            if (dbDto.getType().equalsIgnoreCase(DbType.POSTGRE.toString())) {
                dbQuery = new PostgreQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.MYSQL.toString())) {
                dbQuery = new MysqlQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.ORACLE.toString())) {
                dbQuery = new OracleQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.INCEPTOR.toString())) {
                dbQuery = new InceptorQuery(dbDto);
            } else if (dbDto.getType().equalsIgnoreCase(DbType.DREMIO.toString())) {
                dbQuery = new DremioQuery(dbDto);
            } else {
                // TODO: 2022/7/1 其他数据库
            }
            try {
                if (null != dbQuery) {
                    result = dbQuery.query(dbDto.getTableName(), dbDto.getColumns(), dbDto.getQueryParam(), dbDto.getSortParam());
                }
            } catch (SQLException e) {
                logger.error(ExceptionUtil.stacktraceToString(e));
            }
        }
        return result;
    }

    /**
     * 数据查询
     *
     * @param dbDto sql
     * @return List
     */
    public List<Map<String, Object>> queryBySql(DbDto.DbQueryBySql dbDto) {
        List<Map<String, Object>> result = new ArrayList<>();
        DbQuery dbQuery = null;
        if (dbDto.getType().equalsIgnoreCase(DbType.POSTGRE.toString())) {
            dbQuery = new PostgreQuery(dbDto);
        } else if (dbDto.getType().equalsIgnoreCase(DbType.MYSQL.toString())) {
            dbQuery = new MysqlQuery(dbDto);
        } else if (dbDto.getType().equalsIgnoreCase(DbType.ORACLE.toString())) {
            dbQuery = new OracleQuery(dbDto);
        } else if (dbDto.getType().equalsIgnoreCase(DbType.INCEPTOR.toString())) {
            dbQuery = new InceptorQuery(dbDto);
        } else if (dbDto.getType().equalsIgnoreCase(DbType.DREMIO.toString())) {
            dbQuery = new DremioQuery(dbDto);
        } else {
            //TODO... 其他数据库
        }
        try {
            if (null != dbQuery) {
                result = dbQuery.querySql(dbDto.getSql());
            }
        } catch (Exception e) {
            logger.error(ExceptionUtil.stacktraceToString(e));
        }
        return result;
    }

    /**
     * 获取当前IP地址
     *
     * @return url
     */
    public String getHttpUrl(String ip, int port) {
        return "http://" + ip + ":" + port;
    }

    /**
     * 获取导出请求路径
     *
     * @return url
     */
    public String getExportUrl(DbDto.DbExport dbExport) {
        String key = "export_" + System.currentTimeMillis() + IdUtil.simpleUUID();
        JSONObject jsonObject = (JSONObject) JSON.toJSON(dbExport);
        repository.setExpire(key, jsonObject, EXPORT_EXPIRE_TIME);
        return key;
    }

    /**
     * 获取导出参数
     *
     * @param key redis
     * @return 导出参数
     */
    public JSONObject getExportParam(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }
        Object o = repository.get(key);
        if (ObjectUtil.isEmpty(o)) {
            return null;
        }
        return (JSONObject) o;
    }

    /**
     * 发送数仓消息
     *
     * @param dbDto 参数
     * @return DataEngineMsg
     */
    public DataEngineMsg sendMessage(DbVo dbDto, MsgType type, String methodName, Object response) {
        DataEngineMsg msg = new DataEngineMsg(IdUtil.simpleUUID(), type, dbDto, methodName, response);
        SyncFutureUtil<DataEngineMsg> syncFuture = new SyncFutureUtil<>();
        futureCache.put(msg.getId(), syncFuture);
        msg = nettyServer.sendSyncMsg(msg, syncFuture);
        return msg;
    }

    /**
     * 回填消息
     *
     * @param msg 消息
     */
    public void ackSyncMsg(DataEngineMsg msg) {
        String key = msg.getId();
        // 从缓存中获取数据
        SyncFutureUtil<DataEngineMsg> syncFuture = futureCache.getIfPresent(key);
        if (syncFuture != null) {
            syncFuture.setResponse(msg);
            futureCache.invalidate(key);
        }
    }

    /**
     * 返回消息校验
     *
     * @param msg 消息
     * @return Boolean
     */
    public boolean checkResponse(DataEngineMsg msg) {
        if (ObjectUtil.isNull(msg) || null == msg.getResponse()) {
            return false;
        }
        return !msg.getResponse().toString().equals("远程主机未连接");
    }

    /**
     * 控制台日志输出
     *
     * @param t 输出信息
     * @return 输出信息
     */
    public static String consoleLog(Object t) {
        return JSON.toJSONString(t).length() > 255 ? JSON.toJSONString(t).substring(0, 255) : JSON.toJSONString(t);
    }

}
