package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.RawLicenseDao;
import com.taiyi.shuduoduo.ims.entity.RawLicense;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class RawLicenseService extends CommonMysqlService<RawLicenseDao, RawLicense> {
    @Override
    public Class<RawLicense> getEntityClass() {
        return RawLicense.class;
    }

    /**
     * 取消认证
     *
     * @param id id
     * @return bool
     */
    public boolean cancelLicense(String id) {
        UpdateWrapper<RawLicense> wrapper = new UpdateWrapper<>();
        wrapper.set("if_cancel", true).eq("id", id);
        return super.update(wrapper);
    }

    /**
     * 根据台账ID和用户ID 获取台账认证信息
     *
     * @param rawId  台账ID
     * @param userId 用户ID
     * @return 台账认证信息
     */
    public RawLicense getByRawIdAndUserId(String rawId, String userId) {
        QueryWrapper<RawLicense> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("raw_id", rawId)
                .eq(StrUtil.isNotBlank(userId), "user_id", userId)
                .orderByDesc("create_time").last("LIMIT 1");
        return super.getOne(wrapper);
    }
}