package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_dws_basic_auth")
public class DwsBasicAuth extends CommonMySqlEntity {
    /**
     * 指标ID
     */
    private String dwsId;

    /**
     * 认证人
     */
    private String authedBy;

    /**
     * 认证状态
     */
    private Boolean status;

    /**
     * 认证意见
     */
    private String comment;

}