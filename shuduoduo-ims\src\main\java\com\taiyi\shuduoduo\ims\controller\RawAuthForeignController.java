package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.service.RawAuthForeignService;
import com.taiyi.shuduoduo.ims.vo.RawAuthForeignVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/raw/auth/foreign")
@Validated
public class RawAuthForeignController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private RawAuthForeignService rawAuthForeignService;

    /**
     * 新增数据
     *
     * @param t
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated RawAuthForeignVo.InsertVo t) {
        boolean f;
        try {
            f = rawAuthForeignService.saveRawAuth(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @GetMapping("/{rawAuthId}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("rawAuthId") String rawAuthId) {
        try {
            RawAuthForeignVo.DetailVo t = rawAuthForeignService.getDetail(rawAuthId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = rawAuthForeignService.deleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

}
