package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * 维度权限表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_dimension_auth")
public class DimensionAuth extends CommonMySqlEntity {
    /**
     * 维度表ID
     */
    private String dimId;

    /**
     * 权限表ID
     */
    private String authTableId;

    /**
     * 权限关系符
     */
    private String operator;

    /**
     * 状态 0、未开启1、开启
     */
    private Boolean status;

    private Boolean ifDeleted;

}