package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * 维度/事实表关联指标表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_dim_dws")
public class DimDws extends CommonMySqlEntity {
    /**
     * 关联维度/事实表字段ID
     */
    private String dimAttrId;

    /**
     * 关联维度/事实表ID
     */
    private String dimId;

    /**
     * 关联指标ID
     */
    private String dwsId;

    /**
     * 排序
     */
    private Long orderBy;

    /**
     * 统计周期
     */
    private String periodValue;

    /**
     * 统计维度
     */
    private String dimension;

    /**
     * 挂接类型
     */
    private String unionType;

    /**
     * 可用分析
     */
    private String analysis;

}