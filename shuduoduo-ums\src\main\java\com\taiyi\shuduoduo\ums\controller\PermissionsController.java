package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ums.entity.Permissions;
import com.taiyi.shuduoduo.ums.service.PermissionsService;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.entity.MyQuery;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ums.vo.MenuVo;
import com.taiyi.shuduoduo.ums.vo.PermissionsVo;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 权限
 */
@RestController
@RequestMapping("/permissions")
@Validated
public class PermissionsController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private PermissionsService permissionsService;

    /**
     * 新增
     *
     * @param t
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated PermissionsVo.AddParam t) {
        boolean f;
        try {
            t.setCompanyId(CurrentUserUtil.get().getCompanyId());
            f = permissionsService.savePermission(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 删除
     *
     * @param t
     * @return
     */
    @PostMapping("/remove")
    public ResponseEntity<ResponseVo.ResponseBean> removePermission(@RequestBody @Validated PermissionsVo.AddParam t) {
        boolean f;
        try {
            List<Permissions> permissionsList = new ArrayList<>();
            for (String s : t.getMenuIds().split(StrUtil.COMMA)) {
                Permissions pm = new Permissions();
                pm.setRoleId(t.getRoleId());
                pm.setCompanyId(t.getCompanyId());
                pm.setMenuId(s);
                permissionsList.add(pm);
            }
            f = permissionsService.removePermissions(t.getRoleId(), permissionsList);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            Permissions t = permissionsService.getById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }


    @PutMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateById(@PathVariable("id") String id, @RequestBody @Validated Permissions t) {
        boolean f;
        try {
            f = permissionsService.updateWithNull(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = permissionsService.deleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @PostMapping("/page")
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody(required = false) MyQuery query) {
        try {
            PageResult page = permissionsService.page(query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @GetMapping("/permissions/{roleId}")
    public ResponseEntity<ResponseVo.ResponseBean> permissionList(@PathVariable("roleId") String roleId) {
        try {
            List<Permissions> list = permissionsService.getPermissionListByRoleId(CurrentUserUtil.get().getCompanyId(), roleId);
            return ResponseVo.response(MessageCode.SUCCESS, list);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

}
