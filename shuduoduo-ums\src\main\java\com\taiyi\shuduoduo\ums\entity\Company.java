package com.taiyi.shuduoduo.ums.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ums_company")
public class Company extends CommonMySqlEntity {
    /**
     * 应用id
     */
    private String agentId;

    /**
     * 企微id
     */
    private String corpId;

    /**
     * 飞书id
     */
    private String displayId;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 企业秘钥
     */
    private String secret;

    /**
     * 状态，0: 正常, 1: 停用
     */
    private Integer state;

    /**
     * 企业用户标识
     */
    private String tenantKey;

    /**
     * 1.企业微信 2.钉钉 3.飞书 4.其他
     */
    private Integer type;

    /**
     * 是否已授权dws
     */
    private Boolean ifDwsAuthed;

}