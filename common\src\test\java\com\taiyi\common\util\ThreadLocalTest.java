package com.taiyi.common.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class ThreadLocalTest {

    @Test
    void threadLocal() throws InterruptedException {

        Process process = new Process();
        Thread thread1 = new Thread(() -> {
            String value = "thread1";
            ThreadLocalUtil.put(value);
            Assertions.assertTrue(value.equals(ThreadLocalUtil.get()));
            Assertions.assertTrue(value.equals(process.getValue()));
        });

        Thread thread2 = new Thread(() -> {
            String value = "thread2";
            ThreadLocalUtil.put(value);
            Assertions.assertTrue(value.equals(ThreadLocalUtil.get()));
            Assertions.assertTrue(value.equals(process.getValue()));
        });

        thread1.start();
        thread2.start();

        thread1.join();
        thread2.join();
    }
}


class ThreadLocalUtil {

    private static final ThreadLocal<String> threadLocal = new ThreadLocal<>();

    public static void put(String value) {
        threadLocal.set(value);
    }

    public static String get() {
        return threadLocal.get();
    }

    public static void remove() {
        threadLocal.remove();
    }

}

class Process {
    public String getValue() {
        return ThreadLocalUtil.get();
    }
}