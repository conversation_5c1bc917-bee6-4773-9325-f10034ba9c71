package com.taiyi.shuduoduo.ims.aspect;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.TopicApiLog;
import com.taiyi.shuduoduo.ims.service.TopicApiLogService;
import com.taiyi.shuduoduo.ims.vo.QueryDataVo;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executor;

/**
 * Topic接口日志切面
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class TopicApiLogAspect {
    private Logger logger = LoggerFactory.getLogger(TopicApiLogAspect.class);

    @Autowired
    private TopicApiLogService topicApiLogService;

    @Autowired
    private Executor taskExecutor;


    @Before("@annotation(TopicApiLogPointCut)")
    public void before(JoinPoint joinPoint) {
        logger.info("请求参数：joinPoint:{},attributes:{}", joinPoint.getArgs());
        // 获取传入方法的参数
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            logger.debug("无方法参数，跳过日志记录");
            return;
        }
        // 获取方法的参数注解类型，找到 @RequestBody 对应的参数
        QueryDataVo.QueryRequest param = null;
        for (Object arg : args) {
            if (arg instanceof QueryDataVo.QueryRequest) {
                param = (QueryDataVo.QueryRequest) arg;
                break;
            }
        }
        if (param == null || StringUtils.isEmpty(param.getApiId())) {
            logger.warn("未找到 QueryRequest 或 apiId 为空，跳过日志记录");
            return;
        }
        // 动态获取 param 中的 appId 参数
        String appId = param.getApiId();
        // 记录调用日志
        TopicApiLog topicApiLog = new TopicApiLog();
        topicApiLog.setUseBy(CurrentUserUtil.get().getId());
        topicApiLog.setApiId(appId);
        try {
            taskExecutor.execute(() -> topicApiLogService.save(topicApiLog));
        } catch (Exception e) {
            logger.error("保存TopicApiLog日志失败,{}", ExceptionUtil.stacktraceToString(e));
        }

    }
}
