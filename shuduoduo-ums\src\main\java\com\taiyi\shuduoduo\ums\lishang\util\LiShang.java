package com.taiyi.shuduoduo.ums.lishang.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 天立教育
 *
 * <AUTHOR>
 */
public class LiShang {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    /**
     * http://dev.cas.tianlieducation.com:8010
     * AppId: OAUB34C6F5456A04F7C8828C0883E3416E7TH2
     * AppSecret: igSLnFw5cUBpg3c6mKJH8ygxDWnqlqlUdHf2MVeqDPZcZ1tnDN5hEwEhXogZ
     */

    /**
     * 获取token
     */
    private final static String APP_ACCESS_TOKEN_URI = "/api/system/oauth/accessToken";

    /**
     * 获取当前授权用户
     */
    public static final String GET_LOGIN_USER_INFO_URL = "https://sso-uat.lsguochao.com/cas/validate";

    /**
     * 获取用户列表
     */
    private final static String USER_LIST_URI = "/api/system/sync/user";

    /**
     * 请求头
     */
    public static final Map<String, String> DEFAULT_HEADER = new HashMap<String, String>() {
        {
            put("Content-Type", "application/json; charset=utf-8");
        }
    };

    /**
     * 企业id
     */
    private String appId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 接口地址
     */
    private String host;

    public LiShang(String host, String appId, String username, String password) {
        this.appId = appId;
        this.username = username;
        this.password = password;
        this.host = host;
    }

    /**
     * 获取带token的请求头
     *
     * @return map
     */
    public Map<String, String> getTokenHeader() {
        String appToken = authorizeAppAccessToken();
        Map<String, String> heardMap = new HashMap<>();
        heardMap.put("Authorization", appToken);
        heardMap.put("ClientId", appId);
        heardMap.put("OperationCode", "access_token");
        return heardMap;
    }

    /**
     * 向飞书发送get请求
     *
     * @return 结果
     */
    public JSONObject get(String url, Map<String, String> header, String pathParam, String behindUrl, Map<String, Object> param) {
        url = url + pathParam + behindUrl;
        return get(url, header, param);
    }

    /**
     * 向飞书发送get请求
     *
     * @return 结果
     */
    public JSONObject get(String url, Map<String, String> header, String pathParam) {
        url = url + pathParam;
        return get(url, header);
    }

    /**
     * 向飞书发送get请求
     *
     * @return 结果
     */
    public JSONObject get(String url, Map<String, String> header) {
        return get(url, header, new HashMap<>());
    }

    /**
     * 向飞书发送get请求
     *
     * @return 结果
     */
    public JSONObject get(String url, Map<String, String> header, Map<String, Object> param) {
        url = genUrl(url);
        HttpRequest httpRequest = HttpRequest.get(url).addHeaders(header);
        if (param.size() > 0) {
            /*
             * hutool关于HttpRequest的get请求
             * 当form表单传递的是map时，参数是拼接到url上面
             * 当form表单传递的是json字符串的时候，参数是拼接到请求的主体里面
             */
            httpRequest = httpRequest.form(param);
        }
        String body = httpRequest.execute().body();
        return JSON.parseObject(body);
    }

    /**
     * 格式化url
     *
     * @param url url
     * @return url
     */
    protected String genUrl(String url) {
        return String.format("%s%s", host, url);
    }

    /**
     * 向飞书发送post请求
     *
     * @return 结果
     */
    public JSONObject post(String url, Map<String, String> header, Map<String, String> body) {
        if (header == null) {
            header = DEFAULT_HEADER;
        } else {
            header.putAll(DEFAULT_HEADER);
        }

        url = genUrl(url);
        String res = HttpRequest.post(url)
                .addHeaders(header)
                .body(JSONObject.toJSONString(body))
                .execute()
                .body();
        return JSON.parseObject(res);
    }

    /**
     * 校验api是否调用成功
     *
     * @param json 返回结果
     * @return boolean
     */
    public boolean isSucceed(JSONObject json) {
        if (!json.containsKey("code")) {
            return false;
        }
        try {
            return json.getInteger("code") != 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取AppAccessToken
     *
     * @return AppAccessToken
     */
    public String authorizeAppAccessToken() {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("ClientId", appId);
        paramMap.put("username", username);
        paramMap.put("password", password);
        paramMap.put("OperationCode", "access_token");
        logger.info("请求参数：{}", paramMap);
        JSONObject json = post(APP_ACCESS_TOKEN_URI, null, paramMap);
        if (isSucceed(json)) {
            logger.error("获取token失败:{}", json.getString("message"));
            return null;
        }
        return json.getJSONObject("data").getString("token");
    }

    /**
     * 获取访问用户身份
     *
     * @param token token
     * @return 用户身份
     */
    public String getLoginUserInfo(String token, String callbackUri) {
        String url = GET_LOGIN_USER_INFO_URL + "?service=" + URLEncoder.encode(callbackUri) + "&ticket=" + token;
        HttpRequest httpRequest = HttpRequest.get(url);
        String body = httpRequest.execute().body();
        logger.warn("获取访问用户身份,{},{}", url, body);
        Pattern pattern = Pattern.compile("yes\n(.*?)\n");
        Matcher matcher = pattern.matcher(body);
        if (!matcher.find()) {
            logger.error("获取访问用户身份失败:{}", body);
            return null;
        }
        return matcher.group(1);
    }

    /**
     * 获取用户列表
     */
    public List<JSONObject> getUsers(Integer pageNum, Integer pageSize, String startTime) {
        List<JSONObject> resList = new ArrayList<>();
        Map<String, Object> param = new HashMap<>();
        param.put("pageNum", pageNum);
        param.put("pageSize", pageSize);
        param.put("startTime", StringUtils.isBlank(startTime) ? "1970-03-01 10:00:00" : startTime);
        param.put("endTime", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        param.put("appId", this.appId);
        logger.info("请求参数：{}", param);
        JSONObject json = get(USER_LIST_URI, getTokenHeader(), param);
        logger.info("返回结果:{}", json);
        JSONArray users = json.getJSONObject("data").getJSONArray("records");

        for (Object user : users) {
            resList.add((JSONObject) user);
        }
        /*if (data.getBoolean("has_more") && depts.size() == 50) {
            pageToken = data.getString("page_token");
            getDepts(deptId, pageToken, resList);
        }*/
        return resList;
    }


    public static void main(String[] args) {
        String json = "yes<\\n>" +
                "00001<\\n>";
        Pattern pattern = Pattern.compile("yes\n(.*?)\n");
        Matcher matcher = pattern.matcher(json);
        if (!matcher.find()) {
            System.out.println(0);
        }
        System.out.println(matcher.group(1));
    }
}
