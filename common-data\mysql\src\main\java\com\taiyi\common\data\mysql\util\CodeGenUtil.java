package com.taiyi.common.data.mysql.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 通用代码生成工具类
 *
 * <AUTHOR>
 */
public class CodeGenUtil {

    private static String DB_NAME;

    private static Map<String, Set<String>> mapType = new HashMap<>();

    public static void main(String[] args) {
        try {
            //传入所需要的包名
            String demoPackage = "com.taiyi.shuduoduo.ims";
            //生成所有表的 实体类 调用此方法
            //new CodeBron().gardenOfEden(demoPackage);
            //【注意】: 生成 自定义表 实体类 调用此方法,第二个参数为表名称集合
            new CodeGenUtil().gardenOfEden(demoPackage, Arrays.asList("ims_report_custom","ims_report_template_relation","ims_template_market"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取文件生成路径
     *
     * @return 路径
     */
    private String getGenBasePath() {
        return System.getProperty("user.dir") + System.getProperty("file.separator") + "gen";
    }

    /**
     * 获取链接的数据库名称
     *
     * @return 返回数据库名称
     * @throws SQLException 异常
     */
    private static String getDb() throws SQLException {
        List<Entity> query = Db.use().query("SELECT DATABASE() AS DB ");
        return query.get(0).get("db").toString();
    }

    /**
     * 获取库里表名称
     *
     * @return 表名称集合
     * @throws SQLException 异常
     */
    private List<String> getTNames(String packageName) throws SQLException {
        DB_NAME = getDb();
        String str = getPrefix(packageName) + "%";
        List<Entity> result = Db.use().query("SHOW TABLES FROM " + DB_NAME + " like \"" + str + "\"");
        return result.stream().map(entity -> entity.getStr("tables_in_" + DB_NAME + " (" + str + ")"))
                .collect(Collectors.toList());
    }

    /**
     * 获取 表名和 字段名称 的映射
     * <tableName,fields>
     *
     * @return 映射
     * @throws SQLException 异常
     */
    public Map<String, List<Kid>> genTableAndFieldMap(String packageName, List<String> singleNames) throws SQLException {
        List<String> tableNames;
        if (!CollectionUtil.isEmpty(singleNames)) {
            tableNames = singleNames;
        } else {
            tableNames = getTNames(packageName);
        }

        Map<String, List<Kid>> tableMap = Maps.newHashMap();
        List<String> surplusList = getSurplusList();
        for (String tableName : tableNames) {
            List<Entity> tableResult = Db.use().query("SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name=?", tableName);
            List<Kid> list = Lists.newArrayList();
            Set<String> typeSet = Sets.newHashSet();
            for (Entity entity : tableResult) {
                if (surplusList.contains(entity.getStr("column_name"))) {
                    continue;
                }
                Kid kid = new Kid();
                kid.setName(entity.getStr("column_name"));
                kid.setType(entity.getStr("data_type"));
                kid.setDoc(entity.getStr("column_comment"));
                typeSet.add(entity.getStr("data_type"));
                list.add(kid);
            }
            tableMap.put(tableName, list);
            mapType.put(tableName, typeSet);
        }
        return tableMap;
    }

    /**
     * 生成所有表的实体类
     *
     * @param packageName 包名
     * @throws SQLException 异常
     * @throws IOException  异常
     */
    private void gardenOfEden(String packageName) throws SQLException, IOException {
        createJava(packageName, null);
    }

    /**
     * 生成自定义 表的  实体类
     *
     * @param packageName 包名
     * @param singleNames 表名称集合
     * @throws SQLException 异常
     * @throws IOException  异常
     */
    private void gardenOfEden(String packageName, List<String> singleNames) throws SQLException, IOException {
        createJava(packageName, singleNames);
    }

    /**
     * 生成Java类
     * pojo、dao、service
     *
     * @throws SQLException 异常
     */
    private void createJava(String packageName, List<String> singleNames) throws SQLException, IOException {
        Map<String, List<Kid>> gen = genTableAndFieldMap(packageName, singleNames);
        String prefix = getPrefix(packageName);
        for (Map.Entry<String, List<Kid>> map : gen.entrySet()) {
            String url = map.getKey().replace(prefix + "_", "");
            String tableName = getTableName(url);
            createPojo(packageName, tableName, map);
            createDao(packageName, tableName);
            createService(packageName, tableName);
            createController(packageName, tableName, url);
        }
        System.out.println("------------------end----------------------");
    }

    /**
     * 获取表前缀
     *
     * @param packageName 包名
     * @return 前缀
     */
    private String getPrefix(String packageName) {
        String[] split = packageName.split("\\.");
        return split[split.length - 1];
    }

    /**
     * 创建 实体类
     *
     * @param tableName 表名
     * @param map       映射
     */
    private void createPojo(String packageName, String tableName, Map.Entry<String, List<Kid>> map) {
        String path = getGenBasePath() + File.separator + packageName + File.separator + "entity";
        File file = new File(path + File.separator + tableName);
        if (file.exists()) {
            System.out.println("[" + tableName + "]" + "此文件已经存在！");
            return;
        }
        FileWriter writer = new FileWriter(file);
        writer.write("package " + packageName + ".entity;\n\r");
        writer.append("import com.baomidou.mybatisplus.annotation.TableName;\n");
        writer.append("import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;\n");
        writer.append("import lombok.Data;\n\r");
        setImport(map.getKey(), writer);
        setAuthor(writer);
        writer.append("@Data\n");
        writer.append("@TableName(value = " + "\"" + map.getKey() + "\"" + ")\n");
        writer.append("public class " + tableName.replace(".java", "") +
                " extends CommonMySqlEntity {\n");
        setBody(writer, map.getValue());
    }

    /**
     * 设置 author
     *
     * @param writer writher
     */
    private void setAuthor(FileWriter writer) {
        writer.append("/**\n");
        writer.append(" * <AUTHOR> + System.getProperty("user.name") + "\n");
        writer.append(" */\n");
    }

    /**
     * 导入实体类所需要的包
     *
     * @param tableName 表明
     * @param writer    writer
     */
    private void setImport(String tableName, FileWriter writer) {
        Set<String> set = mapType.get(tableName);
        AtomicInteger flag = new AtomicInteger(0);
        set.forEach((a) -> {
            switch (a) {
                case "decimal":
                    flag.set(1);
                    writer.append("import java.math.BigDecimal;\n");
                    break;
                case "date":
                case "year":
                case "datetime":
                    flag.set(1);
                    writer.append("import java.util.Date;\n");
                    break;
                case "time":
                    flag.set(1);
                    writer.append("import java.sql.Time;\n");
                    break;
                case "json":
                    flag.set(1);
                    writer.append("import com.alibaba.fastjson.JSON;\n");
                    break;
                default:
                    break;
            }
        });
        if (flag.get() == 1) {
            writer.append("\r");
        }
    }

    /**
     * 获取 实体类名称
     *
     * @param tableName 表明
     * @return 类名
     */
    private String getPojoName(String tableName) {
        return tableName.replace(".java", "");
    }

    /**
     * 创建 service 类
     *
     * @param tableName 表明
     */
    private void createService(String packageName, String tableName) throws IOException {
        String name = getPojoName(tableName);
        tableName = name + "Service.java";
        String vp = "service";
        String content = serviceStr.replace("packageName", packageName)
                .replace("Demo", name).replace("spence", System.getProperty("user.name"));
        writeJava(packageName, vp, tableName, content);
    }

    /**
     * 创建 controller
     *
     * @param packageName 包名
     * @param tableName   表名
     * @param url         表名转路径
     * @throws IOException 异常
     */
    private void createController(String packageName, String tableName, String url) throws IOException {
        String name = getPojoName(tableName);
        tableName = name + "Controller.java";
        String vp = "controller";
        String content = controllerStr.replace("packageName", packageName)
                .replace("Demo", name)
                .replace("demo", StrUtil.lowerFirst(name))
                .replace("god", url.replace("_", "/"))
                .replace("spence", System.getProperty("user.name"));
        writeJava(packageName, vp, tableName, content);
    }

    /**
     * 创建dao类
     *
     * @param tableName 表名称
     */
    private void createDao(String packageName, String tableName) throws IOException {
        String name = getPojoName(tableName);
        tableName = name + "Dao.java";
        String vp = "dao";
        String content = daoStr.replace("packageName", packageName)
                .replace("Demo", name).replace("spence", System.getProperty("user.name"));
        writeJava(packageName, vp, tableName, content);
    }

    /**
     * 输出文件
     *
     * @param vp        路径
     * @param tableName 表名
     * @param content   修改后内容
     */
    private void writeJava(String packageName, String vp, String tableName, String content) throws IOException {
        String path = getGenBasePath() + File.separator + packageName + File.separator + vp;
        File file = new File(path + File.separator + tableName);
        if (file.exists()) {
            System.out.println("[" + tableName + "]" + "此文件已经存在！");
            return;
        }
        FileUtils.writeStringToFile(file, content);
    }

    /**
     * 不需要生成的字段集合
     *
     * @return 集合
     */
    private List<String> getSurplusList() {
        String[] surplus = {"version", "create_time", "update_time", "id"};
        return Arrays.asList(surplus);
    }

    /**
     * 设置实体类的内容
     *
     * @param writer writer
     * @param kids   kids
     */
    private void setBody(FileWriter writer, List<Kid> kids) {
        Map<String, String> typeMap = getTypeMap();
        for (Kid kid : kids) {
            String str = StrUtil.toCamelCase(kid.getName());
            String name = str.substring(0, 1).toLowerCase() + str.substring(1);
            setDoc(kid, writer);
            writer.append("    private " + typeMap.get(kid.getType()) + " " + name + ";\n\r");
        }
        writer.append("}");
    }

    /**
     * 生成注释
     *
     * @param kid    kid
     * @param writer writer
     */
    private void setDoc(Kid kid, FileWriter writer) {
        if (StringUtils.isNoneEmpty(kid.getDoc())) {
            writer.append("    /**\n");
            writer.append("     * " + kid.getDoc() + "\n");
            writer.append("     */\n");
        }
    }

    /**
     * 将表里的字段 驼峰命名
     *
     * @param table 表字段
     * @return 返回驼峰命名
     */
    private String getTableName(String table) {
        return StrUtil.upperFirst(StrUtil.toCamelCase(table)) + ".java";
    }

    /**
     * 建立表中的数据类型和
     * java数据类型的映射关系
     *
     * @return 返回映射关系
     */
    private Map<String, String> getTypeMap() {
        Map<String, String> map = new HashMap<>(17);
        map.put("char", "String");
        map.put("varchar", "String");
        map.put("decimal", "BigDecimal");
        map.put("tinyint", "Boolean");
        map.put("smallint", "Integer");
        map.put("mediumint", "Integer");
        map.put("bigint", "Long");
        map.put("int", "Integer");
        map.put("integer", "Integer");
        map.put("double", "Double");
        map.put("date", "Date");
        map.put("time", "Time");
        map.put("year", "Date");
        map.put("datetime", "Date");
        map.put("json", "JSON");
        return map;
    }


    static class Kid {
        private String name;
        private String type;
        private String doc;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getDoc() {
            return doc;
        }

        public void setDoc(String doc) {
            this.doc = doc;
        }
    }

    private String daoStr = "package packageName.dao;\n" +
            "\n" +
            "import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;\n" +
            "import packageName.entity.Demo;\n" +
            "import org.springframework.stereotype.Component;\n" +
            "\n" +
            "/**\n" +
            " * <AUTHOR> +
            " */\n" +
            "@Component\n" +
            "public interface DemoDao extends CommonMysqlMapper<Demo> {}";


    private String serviceStr = "package packageName.service;\n" +
            "\n" +
            "import com.taiyi.common.data.mysql.service.CommonMysqlService;\n" +
            "import packageName.dao.DemoDao;\n" +
            "import packageName.entity.Demo;\n" +
            "import org.springframework.stereotype.Service;\n" +
            "\n" +
            "/**\n" +
            " * <AUTHOR> +
            " */\n" +
            "@Service\n" +
            "public class DemoService extends CommonMysqlService<DemoDao,Demo> {\n" +
            "    @Override\n" +
            "    public Class<Demo> getEntityClass() {\n" +
            "        return Demo.class;\n" +
            "    }\n" +
            "}";

    private String controllerStr = "package packageName.controller;\n" +
            "\n" +
            "import cn.hutool.core.exceptions.ExceptionUtil;\n" +
            "import packageName.entity.Demo;\n" +
            "import packageName.service.DemoService;\n" +
            "import com.taiyi.common.data.mysql.entity.PageResult;\n" +
            "import com.taiyi.common.data.mysql.entity.MyQuery;\n" +
            "import com.taiyi.common.entity.MessageCode;\n" +
            "import com.taiyi.common.entity.ResponseVo;\n" +
            "import org.slf4j.Logger;\n" +
            "import org.slf4j.LoggerFactory;\n" +
            "import org.springframework.beans.factory.annotation.Autowired;\n" +
            "import org.springframework.http.ResponseEntity;\n" +
            "import org.springframework.validation.annotation.Validated;\n" +
            "import org.springframework.web.bind.annotation.*;\n" +
            "\n" +
            "/**\n" +
            " * <AUTHOR> +
            " * @Description: 提供基础的 restful风格的WEB服务\n" +
            " * 包含简单的单表的  增 删 改 查 以及分页查询\n" +
            " */\n" +
            "@RestController\n" +
            "@RequestMapping(\"/god\")\n" +
            "@Validated\n" +
            "public class DemoController {\n" +
            "\n" +
            "    protected final Logger logger = LoggerFactory.getLogger(getClass());\n" +
            "\n" +
            "    @Autowired\n" +
            "    private DemoService demoService;\n" +
            "\n" +
            "    /**\n" +
            "     * 新增数据\n" +
            "     *\n" +
            "     * @param t\n" +
            "     * @return\n" +
            "     */\n" +
            "    @PostMapping\n" +
            "    public ResponseEntity<T> save(@RequestBody @Validated Demo t) {\n" +
            "        boolean f;\n" +
            "        try {\n" +
            "            f = demoService.save(t);\n" +
            "        } catch (Exception e) {\n" +
            "            logger.warn(ExceptionUtil.stacktraceToString(e));\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));\n" +
            "        }\n" +
            "        if (f) {\n" +
            "            return ResponseVo.response(MessageCode.SUCCESS);\n" +
            "        } else {\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR);\n" +
            "        }\n" +
            "    }\n" +
            "\n" +
            "    @GetMapping(\"/{id}\")\n" +
            "    public ResponseEntity<T> getById(@PathVariable(\"id\") String id) {\n" +
            "        try {\n" +
            "            Demo t = demoService.getById(id);\n" +
            "            return ResponseVo.response(MessageCode.SUCCESS, t);\n" +
            "        } catch (Exception e) {\n" +
            "            logger.warn(ExceptionUtil.stacktraceToString(e));\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));\n" +
            "        }\n" +
            "    }\n" +
            "\n" +
            "\n" +
            "    @PutMapping(\"/{id}\")\n" +
            "    public ResponseEntity<T> updateById(@PathVariable(\"id\") String id, @RequestBody @Validated Demo t) {\n" +
            "        boolean f;\n" +
            "        try {\n" +
            "            f = demoService.updateWithNull(id, t);\n" +
            "        } catch (Exception e) {\n" +
            "            logger.warn(ExceptionUtil.stacktraceToString(e));\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));\n" +
            "        }\n" +
            "        if (f) {\n" +
            "            return ResponseVo.response(MessageCode.SUCCESS);\n" +
            "        } else {\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR);\n" +
            "        }\n" +
            "    }\n" +
            "\n" +
            "    @DeleteMapping(\"/{id}\")\n" +
            "    public ResponseEntity<T> deleteById(@PathVariable(\"id\") String id) {\n" +
            "        boolean f;\n" +
            "        try {\n" +
            "            f = demoService.deleteById(id);\n" +
            "        } catch (Exception e) {\n" +
            "            logger.warn(ExceptionUtil.stacktraceToString(e));\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));\n" +
            "        }\n" +
            "        if (f) {\n" +
            "            return ResponseVo.response(MessageCode.SUCCESS);\n" +
            "        } else {\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR);\n" +
            "        }\n" +
            "    }\n" +
            "\n" +
            "    @PostMapping(\"/page\")\n" +
            "    public ResponseEntity<T> page(@RequestBody(required = false) MyQuery query) {\n" +
            "        try {\n" +
            "            PageResult page = demoService.page(query);\n" +
            "            return ResponseVo.response(MessageCode.SUCCESS, page);\n" +
            "        } catch (Exception e) {\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR);\n" +
            "        }\n" +
            "    }\n" +
            "\n" +
            "\n" +
            "    @PostMapping(\"/_exists\")\n" +
            "    public ResponseEntity<T> exists(@RequestBody @Validated Demo t) {\n" +
            "        boolean exists;\n" +
            "        try {\n" +
            "            exists = demoService.exists(t);\n" +
            "        } catch (Exception e) {\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));\n" +
            "        }\n" +
            "        if (exists) {\n" +
            "            return ResponseVo.response(MessageCode.SUCCESS);\n" +
            "        } else {\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR);\n" +
            "        }\n" +
            "    }\n" +
            "\n" +
            "    @PostMapping(\"{id}/_exists\")\n" +
            "    public ResponseEntity<T> exists(@PathVariable String id, @RequestBody @Validated Demo t) {\n" +
            "        try {\n" +
            "            demoService.exists(id, t);\n" +
            "        } catch (Exception e) {\n" +
            "            logger.warn(ExceptionUtil.stacktraceToString(e));\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));\n" +
            "        }\n" +
            "            return ResponseVo.response(MessageCode.SUCCESS);\n" +
            "    }\n" +
            "\n" +
            "   /**\n" +
            "     * 更新部分字段信息\n" +
            "     *\n" +
            "     * @param t\n" +
            "     * @return \n" +
            "     */\n" +
            "    @PatchMapping(\"/{id}\")\n" +
            "    public ResponseEntity<T> patch(@PathVariable String id, @RequestBody @Validated Demo t) {\n" +
            "        boolean f;\n" +
            "        try {\n" +
            "            f = demoService.updateById(id, t);\n" +
            "        } catch (Exception e) {\n" +
            "            logger.warn(ExceptionUtil.stacktraceToString(e));\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));\n" +
            "        }\n" +
            "        if (f) {\n" +
            "            return ResponseVo.response(MessageCode.SUCCESS);\n" +
            "        } else {\n" +
            "            return ResponseVo.response(MessageCode.REQUEST_ERROR);\n" +
            "        }\n" +
            "    }\n" +
            "}\n";
}
