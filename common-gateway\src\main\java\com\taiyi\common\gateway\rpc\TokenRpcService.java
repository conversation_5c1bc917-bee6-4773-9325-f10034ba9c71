package com.taiyi.common.gateway.rpc;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(value = "common-auth")
public interface TokenRpcService {

    /**
     * 验证 oauth2的token
     * <p>
     * 返回 验证状态及 对应的jwt 令牌
     *
     * @param oauthToken
     * @return 
     */
    @GetMapping("/common/auth/rpc/token/verify")
    ResponseEntity<Boolean> verify(@RequestParam String oauthToken);
}
