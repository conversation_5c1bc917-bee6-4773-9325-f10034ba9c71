package com.taiyi.common.connector.rpc;

import com.taiyi.common.connector.service.DbQueryService;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping("/rpc/engine/db")
public class DbQueryRpcController {

    private static Logger logger = LoggerFactory.getLogger(DbQueryRpcController.class);

    @Resource
    private DbQueryService service;

    /**
     * 测试数据库连接
     *
     * @param dbDto 连接参数
     * @return bool
     */
    @PostMapping("/connect/test")
    public boolean testConnect(@RequestBody DbDto dbDto) {
        return service.testConnect(dbDto);
    }

    /**
     * 数据库连接
     *
     * @param dbDto 连接参数
     * @return bool
     */
    @PostMapping("/connect")
    public boolean connect(@RequestBody DbDto dbDto) {
        return service.connect(dbDto);
    }

    /**
     * 查询数据库表
     *
     * @param dbDto 数据库连接参数
     * @return list
     */
    @PostMapping("/getTables")
    public List<String> getTables(@RequestBody DbDto dbDto) {
        return service.getTables(dbDto);
    }

    /**
     * 查询数据库表字段
     *
     * @param dbDto 数据库连接参数&表名
     * @return list
     */
    @PostMapping("/getColumns")
    public List<String> getColumns(@RequestBody DbDto.DbQuery dbDto) {
        return service.getColumns(dbDto);
    }

    /**
     * 查询数据库表字段及字段类型
     *
     * @param dbDto 数据库连接参数&表名
     * @return list
     */
    @PostMapping("/getColumnsAndType")
    public List<Map<String, Object>> getColumnsAndType(@RequestBody DbDto.DbQuery dbDto) {
        return service.getColumnsAndType(dbDto);
    }

    /**
     * 数据查询
     *
     * @param dbDto 数据库连接参数&表名&条件参数
     * @return list
     */
    @PostMapping("/query")
    public List<Map<String, Object>> query(@RequestBody DbDto.DbQuery dbDto) {
        return service.query(dbDto);
    }

    /**
     * 数据查询
     *
     * @param dbDto 数据库连接参数&表名&条件参数
     * @return list
     */
    @PostMapping("/queryBySql")
    public List<Map<String, Object>> queryBySql(@RequestBody DbDto.DbQueryBySql dbDto) {
        return service.queryBySql(dbDto);
    }

    /**
     * 导出--生成唯一链接URL
     *
     * @param dbExport 导出参数
     * @return 导出URL
     */
    @PostMapping("/export")
    public String export(@RequestBody DbDto.DbExport dbExport) {
        return service.getExportUrl(dbExport);
    }

}

