package com.taiyi.common.gateway.rpc;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Set;

/**
 * <AUTHOR>
 */
@FeignClient(value = "common-auth")
public interface PathRpcService {

    /**
     * 获取配置允许的路径
     *
     * @return
     */
    @GetMapping("/common/auth/rpc/path/auth/allow")
    public ResponseEntity<Set<String>> authAllowPaths();
}
