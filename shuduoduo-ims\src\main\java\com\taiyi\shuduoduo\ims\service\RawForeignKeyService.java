package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ims.dao.RawForeignKeyDao;
import com.taiyi.shuduoduo.ims.entity.RawForeignKey;
import com.taiyi.shuduoduo.ims.vo.RawForeignKeyVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RawForeignKeyService extends CommonMysqlService<RawForeignKeyDao, RawForeignKey> {
    @Override
    public Class<RawForeignKey> getEntityClass() {
        return RawForeignKey.class;
    }

    @Autowired
    private RawTableService tableService;

    public List<RawForeignKey> listByRawId(String rawId) {
        QueryWrapper<RawForeignKey> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(rawId), "raw_id", rawId).eq("if_deleted", false).orderByAsc("order_by");
        return super.list(wrapper);
    }

    public boolean logicDeleteByRawId(String rawId) {
        List<RawForeignKey> tables = listByRawId(rawId);
        if (tables.isEmpty()) {
            return true;
        }
        UpdateWrapper<RawForeignKey> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", 1)
                .eq("raw_id", rawId);
        return this.update(wrapper);
    }

    /**
     * 根据台账ID 批量保存外键关联信息
     *
     * @param rawId       台账ID
     * @param foreignKeys 外键关联列表
     * @return bool
     */
    public boolean saveBatchByRaw(String companyId, String rawId, List<RawForeignKeyVo.InsertParam> foreignKeys) {
        List<RawForeignKey> list = new ArrayList<>();
        for (RawForeignKeyVo.InsertParam param : foreignKeys) {
            RawForeignKey foreignKey = BeanUtil.copy(param, RawForeignKey.class);
            foreignKey.setCompanyId(companyId);
            foreignKey.setRawId(rawId);
            foreignKey.setRawTableId(tableService.getByRawIdAndDimId(rawId, param.getDimId()).getId());
            foreignKey.setUnionRawTableId(tableService.getByRawIdAndDimId(rawId, param.getUnionDimId()).getId());
            list.add(foreignKey);
        }
        return super.saveBatch(list);
    }

    /**
     * 根据台账ID 查询外键列表
     *
     * @param rawId 台账ID
     * @return 外键列表
     */
    public List<RawForeignKeyVo.InsertParam> detailListByRawId(String rawId) {
        return BeanUtil.copyList(listByRawId(rawId), RawForeignKeyVo.InsertParam.class);
    }

    /**
     * 根据台账ID 和台账表ID集合查询台账表关联关系
     *
     * @param rawId       台账ID
     * @param rawTableIds 台账表ID集合
     * @return 台账表关联关系
     */
    public List<RawForeignKey> listByRawIdAndTableAttrIds(String rawId, List<String> rawTableIds) {
        QueryWrapper<RawForeignKey> wrapper = new QueryWrapper<>();
        wrapper.eq("raw_id", rawId)
                .eq("if_deleted", false)
                .in("raw_table_id", rawTableIds);
        return super.list(wrapper);
    }
}