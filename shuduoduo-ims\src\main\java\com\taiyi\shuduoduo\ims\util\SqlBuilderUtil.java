package com.taiyi.shuduoduo.ims.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.db.sql.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.util.DateFormatUtil;
import com.taiyi.shuduoduo.ims.vo.SqlBuilderVo;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL构建工具类
 *
 * <AUTHOR>
 */
public class SqlBuilderUtil {

    public static final char DOLLAR = '$';
    public static final String SPLIT_CHAR = "·";
    public static final String OPERATOR_LIKE = "LIKE";
    public static final String OPERATOR_NOT_LIKE = "NOT LIKE";
    public static final String OPERATOR_IN = "IN";
    public static final String OPERATOR_EQ = "=";
    public static final String OPERATOR_NOT_EQ = "!=";
    public static final String NUM_OPERATOR_EQ = "==";
    public static final String NUM_OPERATOR_NOT_EQ = "!==";
    public static final String OPERATOR_GQ = ">";
    public static final String OPERATOR_GQ_EQ = ">=";
    public static final String OPERATOR_LQ = "<";
    public static final String OPERATOR_LQ_EQ = "<=";
    public static final String OPERATOR_IS = "IS";
    public static final String OPERATOR_IS_NOT = "IS NOT";
    public static final String OPERATOR_BETWEEN = "BETWEEN";
    public static final String VALUE_NULL = "NULL";

    /**
     * 组装查询SQL
     *
     * @param isDistinct   是否去重
     * @param queryColumns 查询列
     * @param queryTables  查询表
     * @param joinKeys     关联关系
     * @param authFilter   权限过滤
     * @param filters      条件过滤
     * @param orders       排序条件
     * @param lastSql      追加SQL
     * @return sql
     */
    public static String buildSql(boolean isDistinct, List<String> queryColumns, List<String> queryTables, List<Map<String, Object>> joinKeys, List<SqlBuilderVo.Filter> filters, List<Map<String, Object>> authFilter, List<SqlBuilderVo.Order> orders, String lastSql) {
        SqlBuilder sql = SqlBuilder.create();
        if (isDistinct) {
            sql.select(true, queryColumns);
        } else {
            sql.select(queryColumns);
        }
        if (null == joinKeys || joinKeys.isEmpty()) {
            sql.from(queryTables.get(0));
        } else {
            for (int i = 0; i < joinKeys.size(); i++) {
                String mainTable = extractTableName(joinKeys.get(i).get("main").toString());
                String joinTable = extractTableName(joinKeys.get(i).get("join").toString());

                if (i == 0) {
                    sql.from(mainTable)
                            .join(joinTable, SqlBuilder.Join.LEFT);
                } else if (i > 0) {
                    sql.join(joinTable, SqlBuilder.Join.LEFT);
                }

                // 构建 ON 条件
                String onCondition = joinKeys.get(i).get("main").toString() + " = " + joinKeys.get(i).get("join").toString();
                if (joinKeys.get(i).containsKey("and")) {
                    onCondition += " AND " + joinKeys.get(i).get("and").toString();
                }
                sql.on(onCondition);
            }
        }
        if (null != filters && !filters.isEmpty()) {
            sql = buildFilterSql(sql, filters);
        }
        if (null != authFilter && !authFilter.isEmpty()) {
            if (null == filters || filters.isEmpty()) {
                sql.append(StrUtil.SPACE).append("WHERE ");
                sql = buildAuthFilterSql(sql, authFilter);
            } else {
                sql.append(StrUtil.SPACE).append(LogicalOperator.AND).append(StrUtil.SPACE).append("(");
                sql = buildAuthFilterSql(sql, authFilter);
                sql.append(StrUtil.SPACE).append(")");
            }
        }
        if (null != orders && !orders.isEmpty()) {
            sql = buildOrderSql(sql, orders);
        }
        if (StrUtil.isNotBlank(lastSql)) {
            sql.append(StrUtil.SPACE);
            if (sql.toString().contains("WHERE")) {
                lastSql = lastSql.replace("WHERE", "AND");
            }
            sql.append(lastSql);
        }
        return sql.toString();
    }

    /**
     * 组装查询总条数SQL
     *
     * @param sql 查询sql
     * @return sql
     */
    public static String buildCountSql(String sql) {
        int fromIndex = sql.toUpperCase().indexOf("FROM");
        int orderIndex = sql.toUpperCase().indexOf("ORDER BY");

        if (fromIndex == -1) {
            throw new IllegalArgumentException("SQL 中缺少 FROM 关键字");
        }
        String fromSql;
        if (orderIndex != -1) {
            fromSql = sql.substring(fromIndex + 4, orderIndex);
        } else {
            fromSql = sql.substring(fromIndex + 4);
        }
        SqlBuilder countSql = SqlBuilder.create();
        countSql.select("COUNT(*) AS total");
        countSql.from(fromSql);
        return countSql.toString();
    }

    /**
     * 组装分页SQL
     *
     * @param page 分页参数
     * @param sql  查询sql
     * @return sql
     */
    public static String buildPageSql(String sql, Page page) {
        SqlBuilder pageSql = SqlBuilder.create();
        pageSql.append(sql)
                .append(StrUtil.SPACE).append("LIMIT").append(StrUtil.SPACE).append(page.getSize())
                .append(StrUtil.SPACE).append("OFFSET").append(StrUtil.SPACE).append((page.getCurrent() - 1) * page.getSize());
        return pageSql.toString();
    }

    /**
     * 组装排序SQL
     *
     * @param sql 查询sql
     * @return sql
     */
    public static SqlBuilder buildOrderSql(SqlBuilder sql, List<SqlBuilderVo.Order> orders) {
        List<Order> list = new ArrayList<>();
        for (SqlBuilderVo.Order order : orders) {
            list.add(new Order(order.getCode(), order.getSortType()));
        }
        sql.append(StrUtil.SPACE).orderBy(list.toArray(new Order[orders.size()]));
        return sql;
    }

    /**
     * 组装筛选SQL
     *
     * @param sql 查询sql
     * @return sql
     */
    public static SqlBuilder buildFilterSql(SqlBuilder sql, List<SqlBuilderVo.Filter> filters) {
        List<Condition> list = new ArrayList<>();
        for (SqlBuilderVo.Filter filter : filters) {
            Condition condition = new Condition(false);
            condition.setField(filter.getField());
            if (filter.getOperator().equals(NUM_OPERATOR_EQ)) {
                condition.setOperator(OPERATOR_EQ);
                condition.setValue(filter.getValue());
            } else if (filter.getOperator().equals(NUM_OPERATOR_NOT_EQ)) {
                condition.setOperator(OPERATOR_NOT_EQ);
                condition.setValue(filter.getValue());
            } else if (filter.getOperator().equals(OPERATOR_EQ)) {
                condition.setOperator(OPERATOR_EQ);
                condition.setValue("'" + filter.getValue().toString() + "'");
            } else if (filter.getOperator().equals(OPERATOR_NOT_EQ)) {
                condition.setOperator(OPERATOR_NOT_EQ);
                condition.setValue("'" + filter.getValue().toString() + "'");
            } else if (filter.getOperator().equals(OPERATOR_GQ)) {
                condition.setOperator(OPERATOR_GQ);
                if (DateFormatUtil.isValidDate(filter.getValue().toString())) {
                    condition.setValue("'" + filter.getValue().toString() + "'");
                } else {
                    condition.setValue(filter.getValue());
                }
            } else if (filter.getOperator().equals(OPERATOR_GQ_EQ)) {
                condition.setOperator(OPERATOR_GQ_EQ);
                if (DateFormatUtil.isValidDate(filter.getValue().toString())) {
                    condition.setValue("'" + filter.getValue().toString() + "'");
                } else {
                    condition.setValue(filter.getValue());
                }
            } else if (filter.getOperator().equals(OPERATOR_LQ)) {
                condition.setOperator(OPERATOR_LQ);
                if (DateFormatUtil.isValidDate(filter.getValue().toString())) {
                    condition.setValue("'" + filter.getValue().toString() + "'");
                } else {
                    condition.setValue(filter.getValue());
                }
            } else if (filter.getOperator().equals(OPERATOR_LQ_EQ)) {
                condition.setOperator(OPERATOR_LQ_EQ);
                if (DateFormatUtil.isValidDate(filter.getValue().toString())) {
                    condition.setValue("'" + filter.getValue().toString() + "'");
                } else {
                    condition.setValue(filter.getValue());
                }
            } else if (filter.getOperator().equals(OPERATOR_LIKE)) {
                condition.setOperator(OPERATOR_LIKE);
                condition.setValue("'" + SqlUtil.buildLikeValue(filter.getValue().toString(), Condition.LikeType.Contains, false) + "'");
            } else if (filter.getOperator().equals(OPERATOR_NOT_LIKE)) {
                condition.setOperator(OPERATOR_NOT_LIKE);
                condition.setValue("'" + SqlUtil.buildLikeValue(filter.getValue().toString(), Condition.LikeType.Contains, false) + "'");
            } else if (filter.getOperator().equals(OPERATOR_IN)) {
                condition.setOperator(OPERATOR_IN);
                List<String> objs = Arrays.asList(filter.getValue().toString().split(StrUtil.COMMA));
                List<String> res = new ArrayList<>();
                // 对数组和集合值按照 IN 处理
                if (!objs.isEmpty()) {
                    objs.forEach(s -> res.add("'" + s + "'"));
                    condition.setValue(res, true);
                } else {
                    continue;
                }
            } else if (filter.getOperator().equals(OPERATOR_IS)) {
                condition.setOperator(OPERATOR_IS);
                condition.setValue(VALUE_NULL);
            } else if (filter.getOperator().equals(OPERATOR_IS_NOT)) {
                condition.setOperator(OPERATOR_IS_NOT);
                condition.setValue(VALUE_NULL);
            } else if (filter.getOperator().equals(OPERATOR_BETWEEN)) {
                condition.setOperator(OPERATOR_BETWEEN);
                condition.setValue("'" + filter.getValue() + "'");
                condition.setSecondValue("'" + filter.getSecondValue() + "'");
            }
            list.add(condition);
        }
        sql.append(StrUtil.SPACE).where(list.toArray(new Condition[0]));
        return sql;
    }

    public static List<Condition> convertToCondition(List<SqlBuilderVo.Filter> filters) {
        List<Condition> list = new ArrayList<>();
        for (SqlBuilderVo.Filter filter : filters) {
            Condition condition = new Condition(false);
            condition.setField(filter.getField());
            if (filter.getOperator().equals(NUM_OPERATOR_EQ)) {
                condition.setOperator(OPERATOR_EQ);
                condition.setValue(filter.getValue());
            } else if (filter.getOperator().equals(NUM_OPERATOR_NOT_EQ)) {
                condition.setOperator(OPERATOR_NOT_EQ);
                condition.setValue(filter.getValue());
            } else if (filter.getOperator().equals(OPERATOR_EQ)) {
                condition.setOperator(OPERATOR_EQ);
                condition.setValue("'" + filter.getValue().toString() + "'");
            } else if (filter.getOperator().equals(OPERATOR_NOT_EQ)) {
                condition.setOperator(OPERATOR_NOT_EQ);
                condition.setValue("'" + filter.getValue().toString() + "'");
            } else if (filter.getOperator().equals(OPERATOR_GQ)) {
                condition.setOperator(OPERATOR_GQ);
                if (DateFormatUtil.isValidDate(filter.getValue().toString())) {
                    condition.setValue("'" + filter.getValue().toString() + "'");
                } else {
                    condition.setValue(filter.getValue());
                }
            } else if (filter.getOperator().equals(OPERATOR_GQ_EQ)) {
                condition.setOperator(OPERATOR_GQ_EQ);
                if (DateFormatUtil.isValidDate(filter.getValue().toString())) {
                    condition.setValue("'" + filter.getValue().toString() + "'");
                } else {
                    condition.setValue(filter.getValue());
                }
            } else if (filter.getOperator().equals(OPERATOR_LQ)) {
                condition.setOperator(OPERATOR_LQ);
                if (DateFormatUtil.isValidDate(filter.getValue().toString())) {
                    condition.setValue("'" + filter.getValue().toString() + "'");
                } else {
                    condition.setValue(filter.getValue());
                }
            } else if (filter.getOperator().equals(OPERATOR_LQ_EQ)) {
                condition.setOperator(OPERATOR_LQ_EQ);
                if (DateFormatUtil.isValidDate(filter.getValue().toString())) {
                    condition.setValue("'" + filter.getValue().toString() + "'");
                } else {
                    condition.setValue(filter.getValue());
                }
            } else if (filter.getOperator().equals(OPERATOR_LIKE)) {
                condition.setOperator(OPERATOR_LIKE);
                condition.setValue("'" + SqlUtil.buildLikeValue(filter.getValue().toString(), Condition.LikeType.Contains, false) + "'");
            } else if (filter.getOperator().equals(OPERATOR_NOT_LIKE)) {
                condition.setOperator(OPERATOR_NOT_LIKE);
                condition.setValue("'" + SqlUtil.buildLikeValue(filter.getValue().toString(), Condition.LikeType.Contains, false) + "'");
            } else if (filter.getOperator().equals(OPERATOR_IN)) {
                condition.setOperator(OPERATOR_IN);
                List<String> objs = Arrays.asList(filter.getValue().toString().split(StrUtil.COMMA));
                List<String> res = new ArrayList<>();
                // 对数组和集合值按照 IN 处理
                if (!objs.isEmpty()) {
                    objs.forEach(s -> res.add("'" + s + "'"));
                    condition.setValue(res, true);
                } else {
                    continue;
                }
            } else if (filter.getOperator().equals(OPERATOR_IS)) {
                condition.setOperator(OPERATOR_IS);
                condition.setValue(VALUE_NULL);
            } else if (filter.getOperator().equals(OPERATOR_IS_NOT)) {
                condition.setOperator(OPERATOR_IS_NOT);
                condition.setValue(VALUE_NULL);
            }
            list.add(condition);
        }
        return list;
    }

    /**
     * 组装权限SQL
     *
     * @param sql     查询sql
     * @param filters 权限条件
     * @return sql
     */
    public static SqlBuilder buildAuthFilterSql(SqlBuilder sql, List<Map<String, Object>> filters) {
        for (Map list : filters) {
            if (filters.size() > 1) {
                sql.append(StrUtil.SPACE).append("(");
            }
            List<SqlBuilderVo.Filter> auth = (List<SqlBuilderVo.Filter>) list.get("auth");
            for (SqlBuilderVo.Filter filter : auth) {
                Condition condition = new Condition(false);
                condition.setField(filter.getField());
                condition.setOperator(OPERATOR_IN);
                List<String> objs = Arrays.asList(filter.getValue().toString().split(StrUtil.COMMA));
                List<String> res = new ArrayList<>();
                // 对数组和集合值按照 IN 处理
                if (!objs.isEmpty()) {
                    objs.forEach(s -> res.add("'" + s + "'"));
                    condition.setValue(res, true);
                } else {
                    continue;
                }
                sql.append(condition.toString((Arrays.asList(filter.getValue().toString().split(StrUtil.COMMA)))));
                if (list.size() > 1) {
                    sql.append(StrUtil.SPACE).append(list.get("op").toString()).append(StrUtil.SPACE);
                }
            }
            if (sql.toString().endsWith(list.get("op").toString() + StrUtil.SPACE)) {
                sql = new SqlBuilder().append(sql.toString().substring(0, sql.toString().lastIndexOf(list.get("op").toString() + StrUtil.SPACE)));
            }
            if (filters.size() > 1) {
                sql.append(")");
                sql.append(StrUtil.SPACE).append(LogicalOperator.OR);
            }
        }
        if (sql.toString().endsWith(String.valueOf(LogicalOperator.OR))) {
            sql = new SqlBuilder().append(sql.toString().substring(0, sql.toString().lastIndexOf(String.valueOf(LogicalOperator.OR))));
        }
        return sql;
    }

    /**
     * 获取完整表名
     *
     * @param tableName 表名
     * @param tables    完整表名集合
     * @return 完整表名
     */
    public static String getTableName(String tableName, List<String> tables) {
        for (String table : tables) {
            if (table.contains(tableName)) {
                return table;
            }
        }
        return "";
    }

    /**
     * 参数校验 - 不得含有SQL关键字
     *
     * @param str 参数字符串
     * @description 匹配效验
     */
    public static boolean sqlValidate(String str) {
        // 统一转为小写
        String s = str.toLowerCase();
        // 过滤掉的sql关键字，特殊字符前面需要加\\进行转义
        String badStr =
                "select|update|and|or|delete|insert|truncate|char|into|substr|ascii|declare|exec|count|master|into|drop|execute|table|" +
                        "char|declare|sitename|xp_cmdshell|like|from|grant|use|group_concat|column_name|" +
                        "information_schema.columns|table_schema|union|where|order|by|" +
                        "'\\*|\\;|\\--|\\+|\\,|\\//|\\/|\\%|\\#";
        //使用正则表达式进行匹配
        boolean matches = s.matches(badStr);
        return matches;
    }

    /**
     * 是否含有sql注入，返回true表示含有
     *
     * @param sql sql 语句
     * @return boolean
     */
    public static boolean containsSqlInjection(String sql) {
        Pattern pattern = Pattern.compile(
                "\\b(exec|insert|drop|grant|alter|delete|update|chr|mid|master|truncate|char|declare)\\b|(\\*|;|\\+|%)");
        Matcher matcher = pattern.matcher(sql.toLowerCase());
        return matcher.find();
    }

    /**
     * 组装计算SQL
     *
     * @param calcMethod  是否去重
     * @param queryColumn 查询列
     * @param queryTables 查询表
     * @param joinKeys    关联关系
     * @param authFilter  权限过滤
     * @param filters     条件过滤
     * @param orders      排序条件
     * @param lastSql     追加SQL
     * @return sql
     */
    public static String buildCalcSql(String calcMethod, String queryColumn, List<String> queryTables, List<Map<String, Object>> joinKeys, List<SqlBuilderVo.Filter> filters, List<Map<String, Object>> authFilter, List<SqlBuilderVo.Order> orders, String lastSql) {
        SqlBuilder sql = SqlBuilder.create();
        sql.append("SELECT").append(StrUtil.SPACE).append(calcMethod).append("(").append(queryColumn).append(")").append(StrUtil.SPACE).append("AS total");
        if (null == joinKeys || joinKeys.isEmpty()) {
            sql.from(queryTables.get(0));
        } else {
            for (int j = 0; j < joinKeys.size(); j++) {
                if (j == 0) {
                    sql.from(getTableName(joinKeys.get(j).get("main").toString().substring(0, joinKeys.get(j).get("main").toString().indexOf(StrUtil.DOT)), queryTables))
                            .join(getTableName(joinKeys.get(j).get("join").toString().substring(0, joinKeys.get(j).get("join").toString().indexOf(StrUtil.DOT)), queryTables), SqlBuilder.Join.LEFT);
                    sql.on(joinKeys.get(j).get("main").toString() + StrUtil.SPACE + "=" + StrUtil.SPACE + joinKeys.get(j).get("join").toString());
                } else {
                    // 查询在已拼接的sql中最近的表名
                    if (joinKeys.get(j).get("main").toString().substring(0, joinKeys.get(j).get("main").toString().indexOf(StrUtil.DOT))
                            .equals(joinKeys.get(j - 1).get("main").toString().substring(0, joinKeys.get(j - 1).get("main").toString().indexOf(StrUtil.DOT))) &&
                            joinKeys.get(j).get("join").toString().substring(0, joinKeys.get(j).get("join").toString().indexOf(StrUtil.DOT))
                                    .equals(joinKeys.get(j - 1).get("join").toString().substring(0, joinKeys.get(j - 1).get("join").toString().indexOf(StrUtil.DOT)))) {

                        sql.append(StrUtil.SPACE + LogicalOperator.AND + StrUtil.SPACE);
                        sql.append(joinKeys.get(j).get("main").toString() + StrUtil.SPACE + "=" + StrUtil.SPACE + joinKeys.get(j).get("join").toString());
                    } else {
                        sql.join(getTableName(joinKeys.get(j).get("join").toString().substring(0, joinKeys.get(j).get("join").toString().indexOf(StrUtil.DOT)), queryTables), SqlBuilder.Join.LEFT);
                        sql.on(joinKeys.get(j).get("main").toString() + StrUtil.SPACE + "=" + StrUtil.SPACE + joinKeys.get(j).get("join").toString());
                    }
                }
            }
        }
        if (null != filters && !filters.isEmpty()) {
            sql = buildFilterSql(sql, filters);
        }
        if (null != authFilter && !authFilter.isEmpty()) {
            if (null == filters || filters.isEmpty()) {
                sql.append(StrUtil.SPACE).append("WHERE ");
                sql = buildAuthFilterSql(sql, authFilter);
            } else {
                sql.append(StrUtil.SPACE).append(LogicalOperator.AND).append(StrUtil.SPACE).append("(");
                sql = buildAuthFilterSql(sql, authFilter);
                sql.append(StrUtil.SPACE).append(")");
            }
        }
        if (null != orders && !orders.isEmpty()) {
            sql = buildOrderSql(sql, orders);
        }
        return StrUtil.isNotBlank(lastSql) ? sql.append(StrUtil.SPACE).append(lastSql).toString() : sql.toString();
    }


    /**
     * 获取表名
     *
     * @param field 字段
     * @return 表名
     */
    private static String extractTableName(String field) {
        return field.substring(0, field.lastIndexOf('.'));
    }
}
