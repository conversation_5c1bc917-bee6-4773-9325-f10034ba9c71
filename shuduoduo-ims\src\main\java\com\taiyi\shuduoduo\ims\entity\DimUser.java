package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_dim_user")
public class DimUser extends CommonMySqlEntity {
    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 主题ID
     */
    private String topicId;

    /**
     * 维度表信息
     */
    private String dimInfo;

    /**
     * 删除标记
     */
    private Boolean ifDeleted;

}