package com.taiyi.shuduoduo.sms.controller;

import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.sms.service.DbTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 数据源类型控制类
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dbType")
public class DbTypeController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DbTypeService dbTypeService;

    /**
     * 获取数据源类型列表
     *
     * @return ResponseEntity
     */
    @GetMapping("/typeList")
    public ResponseEntity<ResponseVo.ResponseBean> dataSourceTypeList() {
        return ResponseVo.response(MessageCode.SUCCESS, dbTypeService.list());
    }
}
