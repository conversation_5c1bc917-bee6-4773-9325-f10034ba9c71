package com.taiyi.common.data.mysql.entity;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.util.ClassUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@Data
public class BaseList<T> {
    private final String IS_DELETED = "ifDeleted";
    private final String IS_DELETED_DB = "if_deleted";
    /**
     * 当前页
     */
    protected Integer pageNo = 0;
    /**
     * 每一页条数
     */
    protected Integer pageSize = 100;

    protected String column;
    protected String sortType;

    protected String keyWord;

    /**
     * 带模糊搜索
     *
     * @param keyWordColumn 模糊搜索所使用的字段名称
     * @return MyQuery
     */
    public MyQuery getPage(String keyWordColumn, T entity) {
        Page<T> page = new Page<>();
        page.setSize(pageSize);
        page.setCurrent(pageNo);

        QueryWrapper<T> wrapper = new QueryWrapper<>();
        wrapper.setEntity(entity);

        if (entity != null) {
            wrapper.eq(ClassUtil.containsField(entity.getClass(), IS_DELETED), IS_DELETED_DB, 0);
        }

        //添加排序
        if (StringUtils.isNotEmpty(column)) {
            column = StrUtil.toSymbolCase(column, '_');
            if ("asc".equalsIgnoreCase(sortType)) {
                wrapper.orderByAsc(column);
            } else {
                wrapper.orderByDesc(column);
            }
        } else {
            //默认按照更新时间来排序
            wrapper.orderByDesc("create_time");
        }

        if (StringUtils.isNotEmpty(keyWord)) {
            //设置模糊搜索
            wrapper.like(StrUtil.toSymbolCase(keyWordColumn, '_'), keyWord);
        }

        MyQuery myQuery = new MyQuery();
        myQuery.setPage(page);
        myQuery.setWrapper(wrapper);

        return myQuery;
    }

    /**
     * 带模糊搜索
     *
     * @param keyWordColumn 模糊搜索所使用的字段名称
     * @return MyQuery
     */
    public MyQuery getPage(String keyWordColumn, T entity,QueryWrapper<T> wrapper) {
        Page<T> page = new Page<>();
        page.setSize(pageSize);
        page.setCurrent(pageNo);

        wrapper.setEntity(entity);

        if (entity != null) {
            wrapper.eq(ClassUtil.containsField(entity.getClass(), IS_DELETED), IS_DELETED_DB, 0);
        }

        //添加排序
        if (StringUtils.isNotEmpty(column)) {
            column = StrUtil.toSymbolCase(column, '_');
            if ("asc".equalsIgnoreCase(sortType)) {
                wrapper.orderByAsc(column);
            } else {
                wrapper.orderByDesc(column);
            }
        } else {
            //默认按照更新时间来排序
            wrapper.orderByDesc("create_time");
        }

        if (StringUtils.isNotEmpty(keyWord)) {
            //设置模糊搜索
            wrapper.like(StrUtil.toSymbolCase(keyWordColumn, '_'), keyWord);
        }

        MyQuery myQuery = new MyQuery();
        myQuery.setPage(page);
        myQuery.setWrapper(wrapper);

        return myQuery;
    }
}
