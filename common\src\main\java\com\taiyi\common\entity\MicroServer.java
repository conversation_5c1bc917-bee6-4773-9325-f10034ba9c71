package com.taiyi.common.entity;

/**
 * <AUTHOR>
 */

public class MicroServer {

    public static final String BASE_PACKAGES = "com.taiyi";

    public static class CommonAuth {
        public static final String SERVER_NAME = "common-auth";
        public static final String SERVER_PREFIX = "/common/auth";
    }

    public static class CommonConnector {
        public static final String SERVER_NAME = "common-connector";
        public static final String SERVER_PREFIX = "/common/connector";
    }

    public static class ShuduoduoSms {
        public static final String SERVER_NAME = "shuduoduo-sms";
        public static final String SERVER_PREFIX = "/shuduoduo/sms";
    }

    public static class ShuduoduoUms {
        public static final String SERVER_NAME = "shuduoduo-ums";
        public static final String SERVER_PREFIX = "/shuduoduo/ums";
    }

    public static class ShuduoduoIms {
        public static final String SERVER_NAME = "shuduoduo-ims";
        public static final String SERVER_PREFIX = "/shuduoduo/ims";
    }

    public static class ShuduoduoNocodb {
        public static final String SERVER_NAME = "shuduoduo-nocodb";
        public static final String SERVER_PREFIX = "/shuduoduo/nocodb";
    }


}


