spring:
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
        group: DEFAULT_GROUP
        file-extension: yaml
        refresh-enabled: true
        extension-configs:
          - data-id: ${spring.application.name}-${spring.profiles.active}.yaml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        server-addr: 127.0.0.1:8848
      username: nacos
      password: E<PERSON>(b765fzbfiKxnELMx3HsbaQ==)

  redis:
    host: 127.0.0.1
    port: 6379
    password: root
    timeout: 3000ms
    jedis:
      pool:
        max-idle: 500
        min-idle: 50
        max-active: 2000
        max-wait: 1000ms

netty:
  websocket:
    port: 49988
    ip: 0.0.0.0
    path: /socket/{}
    max-frame-size: 10240

# 服务端 ip , port
web:
  server:
    host: 127.0.0.1
    port: