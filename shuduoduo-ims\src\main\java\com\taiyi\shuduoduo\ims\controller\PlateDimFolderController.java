package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.entity.PlateDimFolder;
import com.taiyi.shuduoduo.ims.service.PlateDimFolderService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/plate/dim/folder")
@Validated
public class PlateDimFolderController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private PlateDimFolderService plateDimFolderService;

    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            PlateDimFolder t = plateDimFolderService.getById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 获取板块下维度文件夹列表
     *
     * @param plateId 板块ID
     * @return T
     */
    @GetMapping("/plate/{plateId}")
    public ResponseEntity<ResponseVo.ResponseBean> getListByPlateId(@PathVariable("plateId") String plateId) {
        try {
            List<PlateDimFolder> t = plateDimFolderService.listByPlateId(plateId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

}
