package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.PlateDao;
import com.taiyi.shuduoduo.ims.dremio.service.DremioApiService;
import com.taiyi.shuduoduo.ims.entity.Plate;
import com.taiyi.shuduoduo.ims.entity.PlateLayer;
import com.taiyi.shuduoduo.ims.exceptions.DremioException;
import com.taiyi.shuduoduo.ims.nocodb.service.NocoDbApiService;
import com.taiyi.shuduoduo.ims.vo.PlateVo;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyDremioRpcService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 板块服务
 *
 * <AUTHOR>
 */
@Service
public class PlateService extends CommonMysqlService<PlateDao, Plate> {

    protected static final Logger logger = LoggerFactory.getLogger(PlateService.class);

    @Override
    public Class<Plate> getEntityClass() {
        return Plate.class;
    }

    private static final ExecutorService executor = new ThreadPoolExecutor(10, 20,
            30, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(50),
            (r, exec) -> {
                // 记录日志、告警、落盘等操作
                logger.warn("线程池任务被拒绝，请检查系统负载！");
                if (logger.isDebugEnabled()) {
                    logger.debug("被拒绝任务: {}", r.getClass().getName());
                }
            });

    @Autowired
    public PlateDao plateDao;

    @Autowired
    private PlateLayerService plateLayerService;

    @Autowired
    private DataFieldService dataFieldService;

    @Autowired
    private BusinessProcessService businessProcessService;

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private DimAttributeService dimAttributeService;

    @Autowired
    private DremioApiService dremioApiService;


    @Autowired
    private CompanyDremioRpcService companyDremioRpcService;

    @Autowired
    private PlateDimFolderService plateDimFolderService;

    @Autowired
    private DwsService dwsService;

    /**
     * 新增板块
     *
     * @param addPlate vo
     * @return 布尔值
     */
    @Transactional(rollbackFor = Exception.class)
    public String addPlate(PlateVo.AddPlate addPlate) {
        Plate plate = BeanUtil.copy(addPlate, Plate.class);
        //从token获取公司id
        String companyId = CurrentUserUtil.get().getCompanyId();
        plate.setCompanyId(companyId);
        // 设置类型为新增
        plate.setType(1);
        boolean f = super.save(plate);
        if (f) {
            //创建维度文件夹
            f = plateDimFolderService.saveByPlateId(plate.getId(), companyId);
        }
        if (f && ObjectUtil.isNotEmpty(addPlate.getPlateLayerList())) {
            for (PlateLayer plateLayer : addPlate.getPlateLayerList()) {
                plateLayer.setPlateId(plate.getId());
                plateLayer.setCompanyId(CurrentUserUtil.get().getCompanyId());
                plateLayer.setOrderBy(plateLayerService.getMaxOrder(plate.getId()));
            }
            f = plateLayerService.saveBatch(addPlate.getPlateLayerList());
        }
        return f ? plate.getId() : null;
    }


    /**
     * 查询所有板块
     *
     * @return 板块
     */
    public List<Map<String, String>> queryPlate(String keyWord, String companyId) {
        List<Map<String, String>> resList = new ArrayList<>();
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0);
        wrapper.eq("company_id", companyId);
        wrapper.like(StringUtils.isNotBlank(keyWord), "name", keyWord);
        List<Plate> list = this.list(wrapper);
        for (Plate plate : list) {
            Map<String, String> map = new HashMap<>();
            map.put("id", plate.getId());
            map.put("name", plate.getName());
            map.put("code", plate.getPlateCode());
            resList.add(map);
        }
        return resList;
    }

    public List<Map<String, Object>> queryPlateByDws(String keyWord) {
        List<Map<String, Object>> resList = new ArrayList<>();
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0);
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId());
        wrapper.like(StringUtils.isNotBlank(keyWord), "name", keyWord);
        List<Plate> list = this.list(wrapper);
        for (Plate plate : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", plate.getId());
            map.put("name", plate.getName());
            map.put("total", dwsService.getCountByRequiredId(plate.getId(), 1));
            resList.add(map);
        }
        return resList;
    }

    /**
     * 分页获取业务板块
     *
     * @param platePageVo 参数对象
     * @return PageResult
     */
    public PageResult<Plate> queryPage(PlateVo.PlatePageVo platePageVo) {
        Page<Plate> page = new Page<>(platePageVo.getPageNo(), platePageVo.getPageSize());
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false);
        if (StringUtils.isNotBlank(platePageVo.getKeyWord())) {
            wrapper.like("name", platePageVo.getKeyWord()).or().like("plate_code", platePageVo.getKeyWord());
        }
        if (StringUtils.isNotBlank(platePageVo.getColumn()) && StringUtils.isNotBlank(platePageVo.getSortType())) {
            wrapper.last("order by " + StrUtil.toSymbolCase(platePageVo.getColumn(), '_') + " " + platePageVo.getSortType());
        } else {
            wrapper.orderByDesc("create_time");
        }
        IPage<Plate> iPage = super.page(page, wrapper);
        return getPageResult(page, iPage);
    }

    /**
     * 根据公司ID 查询板块列表
     *
     * @param companyId 公司ID
     * @return 板块列表
     */
    public List<Plate> getListByCompanyId(String companyId) {
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("if_deleted", false);
        //selectList方法并不会返回null,无结果会返回空列表
        return plateDao.selectList(wrapper);
    }

    /**
     * 校验编码是否存在
     *
     * @param plate 板块
     * @return bool
     */
    public boolean isCodeExists(Plate plate) {
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId());
        wrapper.eq("plate_code", plate.getPlateCode());
        wrapper.ne(StringUtils.isNotBlank(plate.getId()), "id", plate.getId()).last("LIMIT 1");
        return this.getOne(wrapper) != null;
    }

    /**
     * 校验编码是否存在
     *
     * @param plate 板块
     * @return bool
     */
    public boolean isNameExists(Plate plate) {
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId());
        wrapper.eq("name", plate.getName());
        wrapper.ne(StringUtils.isNotBlank(plate.getId()), "id", plate.getId()).last("LIMIT 1");
        return this.getOne(wrapper) != null;
    }

    /**
     * 通过公司id，板块名称和板块编码判断板块存不存在
     *
     * @param plate 公司id，板块名称和板块编码
     * @return 布尔
     */
    public boolean isPlateExist(Plate plate) {
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", StringUtils.isEmpty(plate.getCompanyId()) ? CurrentUserUtil.get().getCompanyId() : plate.getCompanyId());
        wrapper.eq("plate_code", plate.getPlateCode());
        wrapper.eq("name", plate.getName());
        wrapper.last("LIMIT 1");
        return this.getOne(wrapper) != null;
    }

    /**
     * 根据条件查询单个板块
     *
     * @param plate 板块
     * @return 板块
     */
    public Plate getOnePlate(Plate plate) {
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.setEntity(plate)
                .last("LIMIT 1");
        return this.getOne(wrapper);
    }

    /**
     * 详情接口
     *
     * @param id 板块ID
     * @return 详情
     */
    public PlateVo.DetailResponse getDetail(String id) {
        Plate plate = super.getById(id);
        PlateVo.DetailResponse res = BeanUtil.copy(plate, PlateVo.DetailResponse.class);
        List<PlateLayer> list = plateLayerService.listByPlateId(id);
        res.setPlateLayerList(list);
        return res;
    }

    /**
     * 删除接口
     *
     * @param id 板块id
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean logicDeleteById(String id) {
        // 删除板块下数据分层信息
        boolean f = plateLayerService.logicDeleteByPlateId(id) && plateDimFolderService.logicDeleteByPlateId(id);
        return f && super.logicDeleteById(id);
    }

    /**
     * 修改
     *
     * @param id        板块ID
     * @param editPlate 修改信息
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlateById(String id, PlateVo.EditPlate editPlate) {
        // 删除数据分层数据
        boolean f = plateLayerService.logicDeleteByIds(editPlate.getDelPlateLayers());
        if (f) {
            // 设置为未同步状态
            editPlate.setStatus(1);
            // 修改板块信息
            f = super.updateById(id, BeanUtil.copy(editPlate, Plate.class));
            if (f) {
                // 重新绑定数据分层数据
                for (PlateLayer plateLayer : editPlate.getPlateLayerList()) {
                    if (StringUtils.isNotBlank(plateLayer.getId())) {
                        // 更新操作
                        plateLayerService.updateById(plateLayer.getId(), plateLayer);
                    } else {
                        plateLayer.setId(null);
                        plateLayer.setPlateId(id);
                        plateLayer.setCompanyId(CurrentUserUtil.get().getCompanyId());
                        plateLayer.setOrderBy(plateLayerService.getMaxOrder(id));
                        plateLayerService.save(plateLayer);
                    }
                }
            }
        }
        return f;
    }


    /**
     * 板块同步
     *
     * @param plateId 板块ID
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean syncPlate(String plateId) {
        boolean f = false;
        Plate plate = super.getById(plateId);
        if (null != plate) {
            CompanyDremioDTO dremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId() == null ? "7172070a1c264c2bb6bb201e17bf8ee7" : CurrentUserUtil.get().getCompanyId());
            if (null != dremioDTO) {
                String space = dremioApiService.createSpace(dremioDTO.getDremioUsername(), dremioDTO.getDremioPasswd(), dremioDTO.getDremioApiUri(), "space",
                        plate.getPlateCode());
                if (StrUtil.isNotBlank(space)) {
                    // 修改板块状态
                    plate.setStatus(2);
                    plate.setSpaceId(space);
                    f = super.updateById(plateId, plate);
                    // 同步数仓分层
                    if (f) {
                        syncPlateLayer(dremioDTO, plateId, plate.getPlateCode());
                    }
                }
            }
        }
        return f;
    }

    /**
     * 同步数据分层
     *
     * @param dremioDTO dremio连接信息
     * @param plateId   板块ID
     * @param plateName 板块名称
     */
    public void syncPlateLayer(CompanyDremioDTO dremioDTO, String plateId, String plateName) {
        List<PlateLayer> layerList = plateLayerService.listByPlateId(plateId);
        try {
            for (PlateLayer plateLayer : layerList) {
                String folder = dremioApiService.createFolder(dremioDTO.getDremioUsername(), dremioDTO.getDremioPasswd(), dremioDTO.getDremioApiUri(),
                        "folder", plateLayer.getCode(), plateName, Arrays.asList(plateName, plateLayer.getCode()));
                if (StrUtil.isNotBlank(folder)) {
                    plateLayer.setFolderId(folder);
                    plateLayerService.updateById(plateLayer.getId(), plateLayer);
                }
            }
        } catch (Exception e) {
            throw new DremioException(e.getMessage());
        }
    }

    /**
     * 查询数据总条数
     *
     * @return 总条数
     */
    public int selectTotal() {
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId()).eq("if_deleted", false);
        return super.count(wrapper);
    }

    /**
     * 数据统计
     *
     * @return list
     */
    public List<PlateVo.TotalResponse> getTotal() {
        List<PlateVo.TotalResponse> res = new ArrayList<>();

        PlateVo.TotalResponse plate = new PlateVo.TotalResponse();
        plate.setName("业务板块");
        plate.setTotal(this.selectTotal());
        res.add(plate);

        PlateVo.TotalResponse dataField = new PlateVo.TotalResponse();
        dataField.setName("数据域");
        dataField.setTotal(dataFieldService.selectTotal());
        res.add(dataField);

        PlateVo.TotalResponse busProcess = new PlateVo.TotalResponse();
        busProcess.setName("业务过程");
        busProcess.setTotal(businessProcessService.selectTotal());
        res.add(busProcess);

        PlateVo.TotalResponse dimension = new PlateVo.TotalResponse();
        dimension.setName("维度表");
        dimension.setTotal(dimensionService.selectTotal(1));
        res.add(dimension);

        PlateVo.TotalResponse fact = new PlateVo.TotalResponse();
        fact.setName("事实表");
        fact.setTotal(dimensionService.selectTotal(2));
        res.add(fact);

        return res;
    }

    /**
     * 根据公司ID 查询板块ID列表
     *
     * @return 板块ID列表
     */
    public List<String> getIdsByCompanyId() {
        List<String> res = new ArrayList<>();
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId()).eq("if_deleted", false);
        List<Plate> plateList = super.list();
        plateList.forEach(plate -> res.add(plate.getId()));
        return res;
    }

    public Plate getByNameAndCompanyId(String plateName, String companyId) {
        QueryWrapper<Plate> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId).eq("if_deleted", false).eq("name", plateName).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 判断板块下是否有数据湖空间
     *
     * @param plate 板块
     * @return true:有 false:无
     */
    public boolean hasDremioSpace(Plate plate) {
        CompanyDremioDTO dremioDTO = companyDremioRpcService.getCompanyById(plate.getCompanyId());
        String spaceId = dremioApiService.getSpace(dremioDTO.getDremioUsername(), dremioDTO.getDremioPasswd(), dremioDTO.getDremioApiUri(), plate.getSpaceId());
        return null != spaceId;
    }
}