package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

import com.alibaba.fastjson.JSON;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_dim_user_filter")
public class DimUserFilter extends CommonMySqlEntity {
    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 主题ID
     */
    private String topicId;

    /**
     * 维度ID
     */
    private String dimId;

    /**
     * 筛选条件
     */
    private String filters;

    private Boolean ifDeleted;

}