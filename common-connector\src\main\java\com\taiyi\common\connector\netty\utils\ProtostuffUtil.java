package com.taiyi.common.connector.netty.utils;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import io.protostuff.LinkedBuffer;
import io.protostuff.ProtobufIOUtil;
import io.protostuff.Schema;
import io.protostuff.runtime.RuntimeSchema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 编码解码类
 *
 * <AUTHOR>
 */
public class ProtostuffUtil {

    protected static Logger logger = LoggerFactory.getLogger(ProtostuffUtil.class);

    private static ConcurrentMap<Class<?>, Schema<?>> cachedSchema = new ConcurrentHashMap<>();

    /**
     * 使用LinkedBuffer分配一块默认大小的buffer空间
     */
    //private static ThreadLocal<LinkedBuffer> bufferThreadLocal = ThreadLocal.withInitial(() -> LinkedBuffer.allocate(65530));


    /**
     * 序列化
     *
     * @param obj
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> byte[] serialize(T obj) {
        // 获得对象的类
        Class<T> cls = (Class<T>) obj.getClass();
        // 通过对象的类构建对应的schema
        Schema<T> schema = getSchema(cls);
        LinkedBuffer buffer = LinkedBuffer.allocate(LinkedBuffer.DEFAULT_BUFFER_SIZE);
        byte[] data;
        try {
            // 使用给定的schema将对象序列化为一个byte数组，并返回。
            data = ProtobufIOUtil.toByteArray(obj, schema, buffer);
        } finally {
            buffer.clear();
        }
        return data;
    }

    /**
     * 反序列化
     *
     * @param data
     * @param cls
     * @param <T>
     * @return
     */
    public static <T> T deserialize(byte[] data, Class<T> cls) {
        Schema<T> schema = getSchema(cls);
        T obj = schema.newMessage();
        try {
            ProtobufIOUtil.mergeFrom(data, obj, schema);
        } catch (Exception e) {
            logger.debug("反序列化数据异常{}", ExceptionUtil.stacktraceToString(e));
        }
        return obj;
    }

    @SuppressWarnings("unchecked")
    private static <T> Schema<T> getSchema(Class<T> cls) {
        Schema<T> schema = (Schema<T>) cachedSchema.get(cls);
        if (schema == null) {
            // 把可序列化的字段封装到Schema
            schema = RuntimeSchema.createFrom(cls);
            if (ObjectUtil.isNotNull(schema)) {
                cachedSchema.putIfAbsent(cls, schema);
            }
        }
        return schema;
    }
}
