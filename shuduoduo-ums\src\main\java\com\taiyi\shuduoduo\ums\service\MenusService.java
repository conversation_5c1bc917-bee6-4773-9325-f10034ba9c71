package com.taiyi.shuduoduo.ums.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ums.dao.MenusDao;
import com.taiyi.shuduoduo.ums.entity.Menus;
import com.taiyi.shuduoduo.ums.entity.Permissions;
import com.taiyi.shuduoduo.ums.vo.MenuVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class MenusService extends CommonMysqlService<MenusDao, Menus> {
    @Override
    public Class<Menus> getEntityClass() {
        return Menus.class;
    }

    @Autowired
    PermissionsService permissionsService;

    /**
     * 获取权限列表
     *
     * @param param 参数
     * @return List
     */
    public List<MenuVo.TreeList> treeList(MenuVo.SearchParam param) {
        QueryWrapper<Menus> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false)
                .isNull("pid").orderByAsc("order_by");
        // 父级菜单
        List<Menus> list = super.list(wrapper);
        List<MenuVo.TreeList> res = BeanUtil.copyList(list, MenuVo.TreeList.class);
        for (MenuVo.TreeList treeList : res) {
            // 子级菜单
            List<Menus> childMenus = this.getMenuList(treeList.getId());
            List<MenuVo.TreeList> childRes = BeanUtil.copyList(childMenus, MenuVo.TreeList.class);
            for (MenuVo.TreeList childList : childRes) {
                List<Menus> menuList = this.getMenuList(childList.getId());
                childList.setChild(BeanUtil.copyList(menuList, MenuVo.TreeList.class));
            }
            treeList.setChild(childRes);
        }
        return res;
    }

    public List<Menus> getMenuList(String pid) {
        QueryWrapper<Menus> childWrapper = new QueryWrapper<>();
        childWrapper.eq("if_deleted", false)
                .eq("pid", pid).orderByAsc("order_by");
        ;
        return super.list(childWrapper);
    }

    /**
     * 根据父级节点下推子节点
     *
     * @param menus 菜单列表
     * @param pid   父级节点ID
     * @return 菜单列表
     */
    public List<Menus> echoDownMenuList(List<Menus> menus, String pid) {
        List<Menus> result = new ArrayList<>();
        for (Menus menu : menus) {
            if (Objects.equals(menu.getPid(), pid)) {
                result.add(menu);
                result.addAll(echoDownMenuList(menus, menu.getId()));
            }
        }
        return result;
    }

    /**
     * 根据子节点上推父级节点
     *
     * @param menus   菜单列表
     * @param childId 子节点ID
     * @return 菜单列表
     */
    public List<Menus> echoUpMenuList(List<Menus> menus, String childId) {
        List<Menus> result = new ArrayList<>();
        for (Menus menu : menus) {
            if (menu.getPid() != null) {
                result.add(menu);
                result.add(super.getById(menu.getPid()));
                result.addAll(echoUpMenuList(menus, menu.getId()));
            }
        }
        return result;
    }

    public List<String> getDefaultMenus() {
        List<String> menus = new ArrayList<>();
        QueryWrapper<Menus> wrapper = new QueryWrapper<>();
        wrapper.in("id", "1", "10", "11");
        List<Menus> menusList = super.list(wrapper);
        menusList.forEach(menus1 -> menus.add(menus1.getWebId()));
        return menus;

    }
}