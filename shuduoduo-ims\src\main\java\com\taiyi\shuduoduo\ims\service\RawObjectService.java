package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ims.dao.RawObjectDao;
import com.taiyi.shuduoduo.ims.entity.RawObject;
import com.taiyi.shuduoduo.ims.vo.RawObjectVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RawObjectService extends CommonMysqlService<RawObjectDao, RawObject> {
    @Override
    public Class<RawObject> getEntityClass() {
        return RawObject.class;
    }

    public List<RawObject> listByRawId(String rawId) {
        //通过维度id查询维度属性
        QueryWrapper<RawObject> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(rawId), "raw_id", rawId).eq("if_deleted", false).orderByAsc("order_by");
        return super.list(wrapper);
    }

    public boolean logicDeleteByRawId(String rawId) {
        List<RawObject> tables = listByRawId(rawId);
        if (tables.isEmpty()) {
            return true;
        }
        UpdateWrapper<RawObject> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", true)
                .eq("raw_id", rawId);
        return this.update(wrapper);
    }

    /**
     * 根据台账ID 查询对象列表
     *
     * @param rawId 台账ID
     * @return list
     */
    public List<RawObjectVo.InsertParam> detailListByRawId(String rawId) {
        return BeanUtil.copyList(listByRawId(rawId), RawObjectVo.InsertParam.class);
    }

    /**
     * 根据台账ID 批量保存台账对象
     *
     * @param companyId 企业ID
     * @param rawId     台账ID
     * @param objects   台账对象列表
     * @return bool
     */
    public boolean saveBatchByRaw(String companyId, String rawId, List<RawObjectVo.InsertParam> objects) {
        List<RawObject> list = new ArrayList<>();
        for (RawObjectVo.InsertParam param : objects) {
            RawObject object = BeanUtil.copy(param, RawObject.class);
            object.setCompanyId(companyId);
            object.setRawId(rawId);
            list.add(object);
        }
        return super.saveBatch(list);
    }

    public List<RawObjectVo.DetailResponse> browserDetailByRawId(String rawId) {
        return BeanUtil.copyList(listByRawId(rawId), RawObjectVo.DetailResponse.class);
    }
}