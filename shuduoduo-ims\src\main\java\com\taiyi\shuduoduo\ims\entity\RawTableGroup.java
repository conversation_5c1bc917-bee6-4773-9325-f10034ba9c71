package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw_table_group")
public class RawTableGroup extends CommonMySqlEntity {
    private String companyId;

    /**
     * 台账数据表ID
     */
    private String rawTableId;

    /**
     * 台账字段分组名称
     */
    private String name;

    private Long orderBy;

    private Boolean ifDeleted;

}