<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.taiyi.shuduoduo.ims.dao.DimensionDao">


    <select id="selectSearchDimensionList" resultType="map">
        SELECT *
        FROM (
            SELECT *, ROW_NUMBER() OVER (PARTITION BY id ORDER BY id) AS rn
            FROM (
                SELECT id, name, code, data_type ,NULL AS attrName, NULL AS attrCode
                FROM ims_dimension
                WHERE company_id = #{companyId}
                AND if_deleted = false
                <if test="dataType != null">
                    AND data_type = #{dataType}
                </if>
                AND (name LIKE CONCAT('%', #{keyword}, '%') OR code LIKE CONCAT('%', #{keyword}, '%'))

                UNION ALL

                SELECT r.id, r.name, r.code,r.data_type, a.name AS attrName, a.code AS attrCode
                FROM ims_dimension_attr a
                LEFT JOIN ims_dimension r ON a.dim_id = r.id
                WHERE a.if_deleted = false
                AND a.company_id = #{companyId}
                <if test="dataType != null">
                    AND r.data_type = #{dataType}
                </if>
                AND (
                a.name LIKE CONCAT('%', #{keyword}, '%')
                OR r.name LIKE CONCAT('%', #{keyword}, '%')
                OR a.code LIKE CONCAT('%', #{keyword}, '%')
                OR r.code LIKE CONCAT('%', #{keyword}, '%')
                )
                AND r.if_deleted = false
                AND r.company_id = #{companyId}
            ) t1
        ) t2
        WHERE rn = 1
        ORDER BY attrName, id
    </select>

</mapper>
