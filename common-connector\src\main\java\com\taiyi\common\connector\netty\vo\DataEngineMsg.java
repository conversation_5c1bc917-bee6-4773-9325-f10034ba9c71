package com.taiyi.common.connector.netty.vo;

import com.taiyi.common.connector.entity.DbVo;
import lombok.Data;

import java.io.Serializable;

/**
 * 消息
 *
 * <AUTHOR>
 */
@Data
public class DataEngineMsg implements Serializable {

    public DataEngineMsg() {
    }

    public DataEngineMsg(String id, MsgType type, DbVo request, String method, Object v) {
        this.id = id;
        this.type = type;
        this.request = request;
        this.method = method;
        this.response = v;
    }

    private String id;

    private MsgType type;

    private DbVo request;

    private String method;

    private Object response;
}
