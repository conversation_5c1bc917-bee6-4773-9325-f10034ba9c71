package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.service.DwsDataAuthService;
import com.taiyi.shuduoduo.ims.service.DwsService;
import com.taiyi.shuduoduo.ims.vo.DwsAuthVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 指标数据认证控制器
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/dws/data/auth")
@Validated
public class DwsDataAuthController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DwsDataAuthService dwsDataAuthService;

    @Autowired
    private DwsService dwsService;

    /**
     * 新增数据
     *
     * @param t
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated DwsAuthVo.AddRequestVo t) {
        boolean f;
        try {
            f = dwsDataAuthService.saveDwsAuth(t);
        } catch (Exception e) {
            logger.error("保存指标数据认证失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


    /**
     * 根据指标查询认证列表
     *
     * @param id 指标id
     * @return T
     */
    @GetMapping("/list/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getDwsAuthListByDwsId(@PathVariable("id") String id) {
        if (null == dwsService.getById(id)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dwsDataAuthService.getDwsAuthListByDwsId(id));
        } catch (Exception e) {
            logger.error("查询指标数据认证列表失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
