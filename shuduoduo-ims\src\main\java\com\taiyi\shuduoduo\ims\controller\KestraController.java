package com.taiyi.shuduoduo.ims.controller;

import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.kestra.KestraResponse;
import com.taiyi.shuduoduo.ims.kestra.service.KestraApiService;
import com.taiyi.shuduoduo.ims.kestra.vo.KestraVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 流程引擎
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/kestra")
@Validated
public class KestraController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private KestraApiService kestraApiService;

    /**
     * 创建流程
     *
     * @param flow yaml文件
     * @return
     */
    @PostMapping(value = "/flow", consumes = "application/x-yaml")
    public ResponseEntity<ResponseVo.ResponseBean> saveFlow(@RequestBody String flow) {
        if (!flow.isEmpty()) {
            // 处理YAML文件的逻辑
            KestraResponse response = kestraApiService.createFlow(flow);
            return ResponseVo.response(MessageCode.SUCCESS, response);
        } else {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "上传的文件为空");
        }
    }

    /**
     * 编辑流程
     *
     * @param flow yaml文件
     * @return
     */
    @PutMapping(value = "/flow/{namespace}/{flowId}", consumes = "application/x-yaml")
    public ResponseEntity<ResponseVo.ResponseBean> updateFlow(@PathVariable("namespace") String namespace, @PathVariable("flowId") String flowId, @RequestBody String flow) {
        if (!flow.isEmpty()) {
            // 处理YAML文件的逻辑
            KestraResponse response = kestraApiService.updateFlow(namespace, flowId, flow);
            return ResponseVo.response(MessageCode.SUCCESS, response);
        } else {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "上传的文件为空");
        }
    }

    /**
     * 获取文件内容
     *
     * @param namespace 目录
     * @param flowId    流程ID
     * @return T
     */
    @GetMapping("/flow/search")
    public ResponseEntity<ResponseVo.ResponseBean> searchFlow(@RequestParam("namespace") String namespace, @RequestParam(value = "flowId") String flowId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, kestraApiService.searchFlow(namespace, flowId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据namespace获取flow列表
     *
     * @param namespace 目录
     * @return T
     */
    @GetMapping("/flow/list")
    public ResponseEntity<ResponseVo.ResponseBean> searchFlow(@RequestParam("namespace") String namespace) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, kestraApiService.flowList(namespace));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 校验文件
     *
     * @param flow yaml文件
     * @return
     */
    @PostMapping(value = "/flow/valid", consumes = "application/x-yaml")
    public ResponseEntity<ResponseVo.ResponseBean> validFlow(@RequestBody String flow) {
        if (!flow.isEmpty()) {
            KestraResponse response = kestraApiService.validFlow(flow);
            return ResponseVo.response(MessageCode.SUCCESS, response);
        } else {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "上传的文件为空");
        }
    }

    /**
     * 执行
     *
     * @param vo vo
     * @return T
     */
    @PostMapping("/execute")
    public ResponseEntity<ResponseVo.ResponseBean> execute(@RequestBody KestraVo vo) {
        try {
            KestraResponse response = kestraApiService.executeFlowByApi(vo);
            return ResponseVo.response(MessageCode.SUCCESS, response);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 组件列表
     *
     * @return T
     */
    @GetMapping("/plugins")
    public ResponseEntity<ResponseVo.ResponseBean> getList() {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, kestraApiService.getPluginList());
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取执行状态
     *
     * @param executeId 执行ID
     * @return T
     */
    @GetMapping("/state/{executeId}")
    public ResponseEntity<ResponseVo.ResponseBean> getState(@PathVariable("executeId") String executeId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, kestraApiService.getExecutionStatus(executeId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取执行日志
     *
     * @param executeId 执行ID
     * @return T
     */
    @GetMapping("/log/{executeId}")
    public ResponseEntity<ResponseVo.ResponseBean> getLog(@PathVariable("executeId") String executeId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, kestraApiService.getExecuteLog(executeId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取执行详情
     *
     * @param executeId 执行ID
     * @return T
     */
    @GetMapping("/executions/{executeId}")
    public ResponseEntity<ResponseVo.ResponseBean> getExecutionInfo(@PathVariable("executeId") String executeId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, kestraApiService.getExecuteInfo(executeId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取文件内容
     *
     * @param executeId 执行ID
     * @param path      文件路径
     * @return T
     */
    @GetMapping("/executions/output/{executeId}")
    public ResponseEntity<ResponseVo.ResponseBean> getOutputData(@PathVariable("executeId") String executeId, @RequestParam(value = "path") String path) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, kestraApiService.getOutputData(executeId, path));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

}
