package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.AuthTableDao;
import com.taiyi.shuduoduo.ims.entity.AuthTable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class AuthTableService extends CommonMysqlService<AuthTableDao, AuthTable> {
    @Override
    public Class<AuthTable> getEntityClass() {
        return AuthTable.class;
    }

    /**
     * 根据表名查询权限表是否存在
     *
     * @param tableName 表名
     * @return bool
     */
    public AuthTable getByName(String tableName, String companyId) {
        QueryWrapper<AuthTable> wrapper = new QueryWrapper<>();
        wrapper.eq("auth_table", tableName).eq("company_id", companyId).eq("if_deleted", false).last("LIMIT 1");
        return super.getOne(wrapper);
    }
}