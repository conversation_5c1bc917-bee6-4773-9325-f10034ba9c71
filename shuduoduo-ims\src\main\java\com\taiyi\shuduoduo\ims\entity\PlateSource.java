package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_plate_source")
public class PlateSource extends CommonMySqlEntity {
    private String companyId;

    private String plateId;

    private String sourceName;

    private Boolean ifDeleted;

    /**
     * 排序字段
     */
    private Long orderBy;

}