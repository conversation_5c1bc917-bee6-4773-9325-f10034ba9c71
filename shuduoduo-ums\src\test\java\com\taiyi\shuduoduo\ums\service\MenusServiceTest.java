package com.taiyi.shuduoduo.ums.service;

import com.taiyi.shuduoduo.ums.entity.Menus;
import com.taiyi.shuduoduo.ums.vo.MenuVo;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class MenusServiceTest {

    @Autowired
    private MenusService menusService;

    @Test
    void treeList() {
        menusService.treeList(new MenuVo.SearchParam()).forEach(System.out::println);
    }
}