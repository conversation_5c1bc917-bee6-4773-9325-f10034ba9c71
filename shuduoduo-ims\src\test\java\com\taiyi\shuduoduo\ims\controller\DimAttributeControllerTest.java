package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.taiyi.shuduoduo.ims.entity.DimAttribute;
import com.taiyi.shuduoduo.ims.vo.DimAttributeVo;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DimAttributeControllerTest {
    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;
    private static final String json_utf8 = "application/json;charset=UTF-8";

    @BeforeEach
    public void env() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    private static final String uuid = IdUtil.simpleUUID();
    private static final String companyId = "";
    private static final String pathName = "/dim-attribute/";
    private static final String pathNamePage = pathName + "page";
    private DimAttributeVo.AddDimAttribute addDimAttribute = new DimAttributeVo.AddDimAttribute();
    private DimAttribute dimAttribute = new DimAttribute();
    private final String dimID = "3838b640ef834f0cb70fdefd62b29c9d";

    public DimAttributeControllerTest() {
        dimAttribute.setId(uuid);
        dimAttribute.setCode("1111");
        dimAttribute.setName("ssss");

        addDimAttribute.setDimId(dimID);
    }

    @Test
    @Order(10)
    void save() {
        try {
            String r = mockMvc.perform(MockMvcRequestBuilders.post(pathName)
                    .content(JSON.toJSONString(addDimAttribute))
                    .characterEncoding("utf8")
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .accept(json_utf8))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andDo(MockMvcResultHandlers.print())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    @Order(20)
    void updateById() {
        dimAttribute.setCode("222");

        try {
            String r = mockMvc.perform(MockMvcRequestBuilders.put(pathName + uuid)
                    .content(JSON.toJSONString(dimAttribute))
                    .characterEncoding("utf8")
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .accept(json_utf8))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andDo(MockMvcResultHandlers.print())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    @Order(60)
    void deleteById() {
        try {
            String r = mockMvc.perform(MockMvcRequestBuilders.delete(pathName + uuid)
                    .characterEncoding("utf8")
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .accept(json_utf8))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andDo(MockMvcResultHandlers.print())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    @Order(40)
    void page() {
        DimAttributeVo.DimAttributeList dimAttributeList = new DimAttributeVo.DimAttributeList();
        dimAttributeList.setPageNo(0);
        dimAttributeList.setPageSize(100);

        DimAttribute dimAttribute1 = new DimAttribute();
        dimAttribute1.setName("xiangmu_bianma");
        dimAttributeList.setDimAttribute(dimAttribute1);
        try {
            String r = mockMvc.perform(MockMvcRequestBuilders.post(pathNamePage)
                    .content(JSON.toJSONString(dimAttributeList))
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .accept(json_utf8))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andDo(MockMvcResultHandlers.print())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    @Order(50)
    void listByCompanyId() {
        try {
            String r = mockMvc.perform(MockMvcRequestBuilders.post(pathName+"list/" + companyId)
                    .characterEncoding("utf8")
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .accept(json_utf8))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andDo(MockMvcResultHandlers.print())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}