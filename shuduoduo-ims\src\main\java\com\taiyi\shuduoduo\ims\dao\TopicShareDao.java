package com.taiyi.shuduoduo.ims.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.TopicShare;
import com.taiyi.shuduoduo.ims.vo.TopicShareVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface TopicShareDao extends CommonMysqlMapper<TopicShare> {

    /**
     * 分页查询
     *
     * @param topicPage    分页
     * @param queryWrapper 条件
     * @return T
     */
    @Select("select a.id,a.topic_id,a.last_time,a.if_read,a.to_user,a.from_user,a.create_time,b.subject,b.raw_id from ims_topic_share as a left join " +
            " ims_query_topic as b on a.topic_id = b.id ${ew.customSqlSegment}")
    IPage<TopicShareVo.PageResultVo> page(Page<TopicShareVo.PageResultVo> topicPage, @Param("ew") QueryWrapper<TopicShareVo.PageResultVo> queryWrapper);
}