package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.LabelUnionDao;
import com.taiyi.shuduoduo.ims.entity.LabelUnion;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class LabelUnionService extends CommonMysqlService<LabelUnionDao, LabelUnion> {
    @Override
    public Class<LabelUnion> getEntityClass() {
        return LabelUnion.class;
    }


    /**
     * 根据关联ID删除关联列表
     *
     * @param unionId 关联ID
     * @param type    关联类型 1.标签 2.指标
     * @return list
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByUnionId(String unionId, Integer type) {
        List<LabelUnion> labelUnionList = getListByUnionId(unionId, type);
        if (labelUnionList.isEmpty()) {
            return true;
        }
        return super.removeByIds(labelUnionList.stream().map(LabelUnion::getId).collect(Collectors.toList()));
    }

    /**
     * 根据关联ID查询列表
     *
     * @param unionId 关联ID
     * @param type    关联类型 1.标签 2.指标
     * @return list
     */
    public List<LabelUnion> getListByUnionId(String unionId, Integer type) {
        QueryWrapper<LabelUnion> wrapper = new QueryWrapper<>();
        if (type == 1) {
            wrapper.eq("label_id", unionId);
        }
        if (type == 2) {
            wrapper.eq("dws_id", unionId);
        }
        wrapper.orderByAsc("create_time")
                .orderByAsc("order_by");
        return super.list(wrapper);
    }

}