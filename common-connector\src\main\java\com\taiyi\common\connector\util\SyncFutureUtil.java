package com.taiyi.common.connector.util;


import javax.annotation.Nonnull;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class SyncFutureUtil<DataEngineMsg> implements Future<DataEngineMsg> {

    /**
     * 请求和响应是一一对应的，因此初始化CountDownLatch值为1。
     */
    private CountDownLatch latch = new CountDownLatch(1);

    /**
     * 需要响应线程设置的响应结果
     */
    private DataEngineMsg response;

    /**
     * Future的请求时间，用于计算Future是否超时
     */
    private long beginTime = System.currentTimeMillis();

    public SyncFutureUtil() {
    }

    @Override
    public boolean cancel(boolean mayInterruptIfRunning) {
        return false;
    }

    @Override
    public boolean isCancelled() {
        return false;
    }

    @Override
    public boolean isDone() {
        return response != null;
    }

    /**
     * 获取响应结果，直到有结果才返回。
     *
     * @return DataEngineMsg
     * @throws InterruptedException e
     */
    @Override
    public DataEngineMsg get() throws InterruptedException {
        latch.await();
        return this.response;
    }

    /**
     * 获取响应结果，直到有结果或者超过指定时间就返回。
     *
     * @param timeout 超时时间
     * @param unit    单位
     * @return DataEngineMsg
     * @throws InterruptedException e
     */
    @Override
    public DataEngineMsg get(long timeout, @Nonnull TimeUnit unit) throws InterruptedException {
        if (latch.await(timeout, unit)) {
            return this.response;
        }
        return null;
    }

    /**
     * 用于设置响应结果，并且做countDown操作，通知请求线程
     *
     * @param response 响应结果
     */
    public void setResponse(DataEngineMsg response) {
        this.response = response;
        latch.countDown();
    }

    public long getBeginTime() {
        return beginTime;
    }
}