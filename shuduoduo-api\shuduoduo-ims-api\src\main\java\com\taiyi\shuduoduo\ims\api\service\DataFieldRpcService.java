package com.taiyi.shuduoduo.ims.api.service;

import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.ims.api.dto.DataFieldDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 数据域RPC
 *
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoIms.SERVER_NAME)
public interface DataFieldRpcService {

    /**
     * 根据板块ID获取数据域列表
     *
     * @return 数据域列表
     */
    @GetMapping(value = MicroServer.ShuduoduoIms.SERVER_PREFIX + "/rpc/dataField/getDataFieldListByPlateId")
    List<DataFieldDTO> getDataFieldListByPlateId(@RequestParam("plateId") String plateId);

}
