package com.taiyi.common.util;

import cn.hutool.core.util.ReflectUtil;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 树状结构工具类
 * <p>
 * 必须具备 id  pid  children 字段
 *
 * <AUTHOR>
 */
public class TreeUtil {

    /**
     * 构建树状结构
     * <p>
     * 必须具备 id  pid  children 字段
     * <p>
     * 其中 id pid 必须为 基础数据类型
     * <p>
     * children 必须为集合数据类型
     * 其他非children 属性 也必须为基础数据类型，非基础数据类型 属性值 在构造树形结构时候会丢失值
     *
     * @param list
     * @param clazz
     * @param pidValue
     * @return
     */
    public static List build(List list, Class clazz, Object pidValue) {
        if (list == null || clazz == null) {
            return null;
        }
        if (!judgeLegal(clazz)) {
            return null;
        }
        Field fieldPid = ReflectUtil.getField(clazz, "pid");
        if (pidValue != null) {
            if (!pidValue.getClass().equals(fieldPid.getType())) {
                return null;
            }
        }
        return getChildren(list, clazz, pidValue);
    }


    private static List<?> getChildren(List<?> list, Class clazz, Object pidValue) {

        Field fieldId = ReflectUtil.getField(clazz, "id");
        Field fieldPid = ReflectUtil.getField(clazz, "pid");
        Field fieldChildren = ReflectUtil.getField(clazz, "children");

        List result = new ArrayList();
        try {
            for (Object val : list) {
                if (!val.getClass().equals(clazz)) {
                    return null;
                }
                Object id = fieldId.get(val);
                Object pid = fieldPid.get(val);

                if (pid.equals(pidValue)) {
                    Object node = BeanUtil.copy(val, clazz);
                    fieldChildren.set(node, getChildren(list, clazz, id));
                    result.add(node);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        if (result.isEmpty()) {
            return null;
        }
        return result;

    }

    /**
     * 判断数据类型是否满足 树型结构
     * <p>
     * id  pid  children
     *
     * @param clazz
     * @return
     */
    public static boolean judgeLegal(Class clazz) {

        if (clazz == null) {
            return false;
        }

        String[] fields = new String[]{"id", "pid", "children"};
        for (String field : fields) {
            if (!ReflectUtil.hasField(clazz, field)) {
                return false;
            }
        }
        if (!ReflectUtil.getField(clazz, "id").getType()
                .equals(ReflectUtil.getField(clazz, "pid").getType())) {
            return false;
        }
        Field field = ReflectUtil.getField(clazz, "children");
        if (!Collection.class.isAssignableFrom(field.getType())) {
            return false;
        }

        try {
            Object data = clazz.newInstance();

            List children = new ArrayList();
            Object child = clazz.newInstance();
            children.add(child);

            field.setAccessible(true);
            field.set(data, children);

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        return true;
    }

}
