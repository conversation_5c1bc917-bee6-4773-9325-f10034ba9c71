package com.taiyi.shuduoduo.ims.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 台账表字段
 *
 * <AUTHOR>
 */
@Data
public class RawTableAttrVo {

    @ApiModel("台账表字段参数")
    @Data
    public static class InsertParam {

        private String id;

        /**
         * 台账表ID
         */
        @ApiModelProperty("台账表ID")
        private String rawTableId;

        /**
         * 指标ID
         */
        private String dwsId;

        /**
         * 维度/事实表字段ID
         */
        @ApiModelProperty("维度/事实表字段ID")
        private String dimAttrId;

        /**
         * 是否台账显示
         */
        @ApiModelProperty("是否台账显示")
        private Boolean ifShow;

        /**
         * 字段中文名
         */
        @ApiModelProperty("字段中文名")
        private String name;

        /**
         * 字段别名
         */
        @ApiModelProperty("字段别名")
        private String aliasName;

        /**
         * 字段英文名
         */
        @ApiModelProperty("字段英文名")
        private String code;

        /**
         * 显示格式
         */
        @ApiModelProperty("显示格式")
        private String dataFormat;

        /**
         * 是否默认查询列
         */
        @ApiModelProperty("是否默认查询列")
        private Boolean ifDefault;

        /**
         * 分组名称
         */
        @ApiModelProperty("分组名称")
        private String groupName;

        private Long orderBy;
    }

    @Data
    public static class DetailResponse {

        private String id;

        /**
         * 台账表ID
         */
        private String rawTableId;

        /**
         * 维度/事实表字段ID
         */
        private String dimAttrId;

        /**
         * 表名
         */
        private String tableName;

        /**
         * 分组ID，没有分组则为表ID
         */
        private String groupId;

        /**
         * 字段中文名
         */
        private String name;

        private String aliasName;

        private String dwsId;

        /**
         * 字段英文名
         */
        private String code;

        /**
         * 是否默认查询列
         */
        private Boolean ifDefault;

        /**
         * 显示格式
         */
        private String dataFormat;

        private Long orderBy;
    }
}
