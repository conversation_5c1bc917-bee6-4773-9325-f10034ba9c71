package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.NocodbTableDao;
import com.taiyi.shuduoduo.ims.entity.NocodbTable;
import com.taiyi.shuduoduo.ims.nocodb.service.NocoDbApiService;
import com.taiyi.shuduoduo.ims.vo.NocodbTableVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class NocodbTableService extends CommonMysqlService<NocodbTableDao, NocodbTable> {
    @Override
    public Class<NocodbTable> getEntityClass() {
        return NocodbTable.class;
    }

    @Autowired
    private NocoDbApiService apiService;

    @Autowired
    private NocodbTableDao dao;

    /**
     * 编辑
     *
     * @param t t
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveData(NocodbTableVo.EditRequest t) {
        Map<String, Object> body = t.getBody() == null ? new HashMap<>() : t.getBody();
        body.put("table_name", t.getName());
        body.put("project_id", t.getProjectId());
        body.put("title", StringUtils.isNotBlank(t.getDesc()) ? t.getDesc() : t.getName());

        if (StringUtils.isNotBlank(t.getId())) {
            NocodbTable nocodbTable = super.getById(t.getId());
            nocodbTable.setTableName(t.getName());
            nocodbTable.setTableDesc(t.getDesc());
            boolean f = apiService.updateTable(nocodbTable.getNocodbTableId(), body);
            if (f) {
                return super.updateById(t.getId(), nocodbTable);
            } else {
                return false;
            }
        } else {
            String default_column = "[{\"column_name\":\"id\",\"title\":\"Id\",\"dt\":\"integer\",\"dtx\":\"integer\",\"ct\":\"int(11)\",\"nrqd\":false,\"rqd\":true,\"ck\":false,\"pk\":true,\"un\":false,\"ai\":true,\"cdf\":null,\"clen\":null,\"np\":null,\"ns\":0,\"dtxp\":\"\",\"dtxs\":\"\",\"altered\":1,\"uidt\":\"ID\",\"uip\":\"\",\"uicn\":\"\"}]";
            body.put("columns", JSON.parseArray(default_column));

            //调用nocodb API
            String tableId = apiService.createTable(t.getProjectId(), t.getBaseId(), body);
            if (null == tableId) {
                return false;
            }
            NocodbTable table = new NocodbTable();
            table.setProjectId(t.getProjectId());
            table.setCreateBy(CurrentUserUtil.get().getRealName());
            table.setNocodbTableId(tableId);
            table.setTableName(t.getName());
            table.setTableDesc(t.getDesc());
            table.setCompanyId(CurrentUserUtil.get().getCompanyId());
            return super.save(table);
        }
    }

    /**
     * 删除
     *
     * @param id 表ID
     * @return bool
     */
    public boolean deleteData(String id) {
        NocodbTable nocodbTable = super.getById(id);
        if (null == nocodbTable) {
            return false;
        }
        boolean f = apiService.deleteTable(nocodbTable.getNocodbTableId());
        if (f) {
            return super.logicDeleteById(id);
        }
        return false;
    }

    /**
     * 获取列表
     *
     * @param pageVo 分页参数
     * @return 列表
     */
    public PageResult<NocodbTable> getList(NocodbTableVo.NocodbTablePageVo pageVo) {
        Page<NocodbTable> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        QueryWrapper<NocodbTable> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false)
                .eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("project_id", pageVo.getProjectId())
                .eq("create_by", CurrentUserUtil.get().getRealName());
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.like("table_name", pageVo.getKeyWord());
        }
        wrapper.orderByDesc("create_time");
        Page<NocodbTable> tablePage = super.page(page, wrapper);
        return getPageResult(page, tablePage);
    }

    @Override
    public boolean exists(NocodbTable t) {
        QueryWrapper<NocodbTable> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(t.getId())) {
            wrapper.ne("id", t.getId());
        }
        wrapper.eq("if_deleted", false)
                .eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("project_id", t.getProjectId())
                .eq("table_name", t.getTableName()).last("LIMIT 1");
        NocodbTable one = super.getOne(wrapper);
        return one != null;
    }

    public boolean existsByNocodbTableId(String projectId, String tableId) {
        QueryWrapper<NocodbTable> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false)
                .eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("project_id", projectId)
                .eq("nocodb_table_id", tableId).last("LIMIT 1");
        NocodbTable one = super.getOne(wrapper);
        return one != null;
    }

    public void refresh(String projectId) {
        // 获取项目下所有表
        List<Map> mapList = apiService.getProjectTableList(projectId);
        for (Map map : mapList) {
            if (!existsByNocodbTableId(projectId, map.get("id").toString())) {
                NocodbTable table = new NocodbTable();
                table.setId(IdUtil.simpleUUID());
                table.setCompanyId(CurrentUserUtil.get().getCompanyId());
                table.setProjectId(projectId);
                table.setNocodbTableId(map.get("id").toString());
                table.setTableName(map.get("title").toString());
                table.setCreateBy(CurrentUserUtil.get().getRealName());
                table.setVersion(1L);
                table.setCreateTime(DateUtil.parse(map.get("created_at").toString()));
                table.setUpdateTime(DateUtil.parse(map.get("updated_at").toString()));
                dao.insert(table);
            }
        }
    }
}