spring:
  cloud:
    nacos:
      config:
        server-addr: 127.0.0.1:8848
        group: DEFAULT_GROUP
        file-extension: yaml
        refresh-enabled: true
        extension-configs:
          - data-id: ${spring.application.name}-${spring.profiles.active}.yaml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        server-addr: 127.0.0.1:8848
      username: nacos
      password: E<PERSON>(b765fzbfiKxnELMx3HsbaQ==)

  datasource:
    dynamic:
      primary: mysql
      strict: false
      datasource:
        mysql:
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: shuduoduo
          password: <PERSON><PERSON>(xAyypihAUwpGj2q8juM2w2IcpZebw6SF)
          url: ************************************************************************************************************************************************************************
          hikari:
            connection-test-query: SELECT 1
            max-lifetime: 60000
            idle-timeout: 60000
        sqlite:
          driver-class-name: org.sqlite.JDBC
          url: jdbc:sqlite:C:\Users\<USER>\IdeaProjects\shuduoduo-v2\.doc\docker\nocodb\noco.db?date_string_format=yyyy-MM-dd HH:mm:ss
          username:
          password:

  redis:
    host: 127.0.0.1
    port: 6379
    password: ENC(M9VBghQwkfqpvQTWoXJY7g==)
    timeout: 3000ms
    jedis:
      pool:
        max-idle: 500
        min-idle: 50
        max-active: 2000
        max-wait: 1000ms

task:
  running: true