package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ims.dao.RawTableDao;
import com.taiyi.shuduoduo.ims.entity.RawTable;
import com.taiyi.shuduoduo.ims.entity.RawTableGroup;
import com.taiyi.shuduoduo.ims.vo.RawTableAttrVo;
import com.taiyi.shuduoduo.ims.vo.RawTableVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RawTableService extends CommonMysqlService<RawTableDao, RawTable> {
    @Override
    public Class<RawTable> getEntityClass() {
        return RawTable.class;
    }

    @Autowired
    private RawTableAttrService rawTableAttrService;

    @Autowired
    private RawTableGroupService tableGroupService;

    @Autowired
    private RawTableGroupAttrService tableGroupAttrService;

    @Autowired
    private DimensionService dimensionService;

    /**
     * 通过台账id查询表列表
     *
     * @param rawId 台账id
     * @return 表列表
     */
    public List<RawTable> listByRawId(String rawId) {
        QueryWrapper<RawTable> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(rawId), "raw_id", rawId).eq("if_deleted", false).orderByAsc("order_by");
        return super.list(wrapper);
    }

    /**
     * 根据台账ID获取台账表及表字段列表
     *
     * @param rawId 台账ID
     * @return list
     */
    public List<RawTableVo.InsertParam> detailListByRawId(String rawId) {
        QueryWrapper<RawTable> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(rawId), "raw_id", rawId).eq("if_deleted", false).orderByAsc("order_by");
        List<RawTableVo.InsertParam> res = BeanUtil.copyList(super.list(wrapper), RawTableVo.InsertParam.class);
        for (RawTableVo.InsertParam param : res) {
            param.setDimType(dimensionService.getTypeByDimId(param.getDimId()));
            // 获取表字段
            param.setRowTableAttrList(rawTableAttrService.detailListByRawTableId(param.getId()));
        }
        return res;
    }

    /**
     * 根据台账ID删除台账表
     *
     * @param rawId 台账ID
     * @return bool
     */
    public boolean logicDeleteByRawId(String rawId) {
        List<RawTable> tables = listByRawId(rawId);
        if (tables.isEmpty()) {
            return true;
        }
        for (RawTable table : tables) {
            // 删除表字段
            rawTableAttrService.logicDeleteByRawTableId(table.getId());
            // 删除表分组及字段分组
            tableGroupService.logicDeleteByRawTableId(table.getId());
        }
        UpdateWrapper<RawTable> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", 1)
                .eq("raw_id", rawId);
        return this.update(wrapper);
    }

    /**
     * 保存台账表及表字段信息
     *
     * @param rawId     台账ID
     * @param rowTables 表及表字段信息
     * @return boolean
     */
    public boolean saveBatchByRaw(String companyId, String rawId, List<RawTableVo.InsertParam> rowTables) {
        boolean f = false;
        for (RawTableVo.InsertParam param : rowTables) {
            RawTable table = BeanUtil.copy(param, RawTable.class);
            table.setCompanyId(companyId);
            table.setRawId(rawId);
            f = super.save(table);
            if (f) {
                // 保存表字段
                f = param.getRowTableAttrList().isEmpty() || rawTableAttrService.saveBatchByRawTableId(companyId, table.getId(), param.getRowTableAttrList());
            }
        }
        return f;
    }

    /**
     * 根据台账ID和维度/事实表ID查询台账表
     *
     * @param rawId 台账ID
     * @param dimId 维度/事实表ID
     * @return 台账表
     */
    public RawTable getByRawIdAndDimId(String rawId, String dimId) {
        QueryWrapper<RawTable> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(rawId), "raw_id", rawId)
                .eq("dim_id", dimId)
                .eq("if_deleted", false)
                .orderByAsc("order_by")
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 查询台账详情下展示字段列表
     *
     * @param rawId 台账ID
     * @return list
     */
    public List<RawTableAttrVo.DetailResponse> browserDetailByRawId(String rawId) {
        List<RawTable> tables = this.listByRawId(rawId);
        List<RawTableAttrVo.DetailResponse> res = new ArrayList<>();
        for (RawTable table : tables) {
            // 查询表字段
            List<RawTableAttrVo.DetailResponse> responses = rawTableAttrService.browserDetailByRawTableId(table.getId());
            for (RawTableAttrVo.DetailResponse response : responses) {
                // 设置表名称
                response.setTableName(table.getName());
                response.setCode(table.getCode() + StrUtil.DOT + response.getCode());
                // 设置分组ID
                String groupId = tableGroupAttrService.getGroupIdByRawTableAttrId(response.getId());
                response.setGroupId(StringUtils.isNotBlank(groupId) ? groupId : table.getId());
                res.add(response);
            }
        }
        return res;
    }

    /**
     * 查询台账详情下展示字段列表
     *
     * @param rawId 台账ID
     * @return list
     */
    public List<String> showAttrByRawId(String rawId) {
        List<RawTable> tables = this.listByRawId(rawId);
        List<String> res = new ArrayList<>();
        for (RawTable table : tables) {
            // 查询表字段
            List<RawTableAttrVo.DetailResponse> responses = rawTableAttrService.browserDetailByRawTableId(table.getId());
            for (RawTableAttrVo.DetailResponse response : responses) {
                res.add(response.getId());
            }
        }
        return res;
    }

    /**
     * 查询可选字段列表 (附带分组和统计)
     *
     * @param rawId 台账ID
     * @return list
     */
    public List<RawTableVo.OptionParam> optionListByRawId(String rawId) {
        List<RawTable> tables = this.listByRawId(rawId);
        List<RawTableVo.OptionParam> res = new ArrayList<>();
        for (RawTable table : tables) {
            RawTableVo.OptionParam param = BeanUtil.copy(table, RawTableVo.OptionParam.class);
            // 查询没有分组的表字段列表
            List<RawTableAttrVo.DetailResponse> responses = rawTableAttrService.optionListByRawTableId(table.getId());
            List<RawTableAttrVo.DetailResponse> attrRes = getAttrList(table, responses);
            if (!attrRes.isEmpty()) {
                param.setName(table.getName());
                attrRes.sort(Comparator.comparingLong(RawTableAttrVo.DetailResponse::getOrderBy));
                param.setTableAttrs(attrRes);
                param.setTotal(attrRes.size());
                res.add(param);
            }
            // 查询表分组
            List<RawTableGroup> groupList = tableGroupService.listByRawTableId(table.getId());
            for (RawTableGroup group : groupList) {
                // 查询分组表字段列表
                List<RawTableAttrVo.DetailResponse> tableAttrs = BeanUtil.copyList(rawTableAttrService.listByRawTableIdAndGroupId(table.getId(), group.getId()), RawTableAttrVo.DetailResponse.class);
                List<RawTableAttrVo.DetailResponse> groupAttrRes = getAttrList(table, tableAttrs);
                if (!groupAttrRes.isEmpty()) {
                    RawTableVo.OptionParam params = BeanUtil.copy(table, RawTableVo.OptionParam.class);
                    params.setName(table.getName() + StrUtil.COLON + group.getName());
                    groupAttrRes.sort(Comparator.comparingLong(RawTableAttrVo.DetailResponse::getOrderBy));
                    params.setTableAttrs(groupAttrRes);
                    params.setTotal(groupAttrRes.size());
                    res.add(params);
                }
            }
        }
//        res.sort(Comparator.comparing(RawTableVo.OptionParam::getName));
        return res;
    }

    public List<RawTableAttrVo.DetailResponse> getAttrList(RawTable table, List<RawTableAttrVo.DetailResponse> responses) {
        List<RawTableAttrVo.DetailResponse> attrRes = new ArrayList<>();
        for (RawTableAttrVo.DetailResponse response : responses) {
            // 设置表名称
            response.setTableName(table.getName());
            response.setCode(table.getCode() + StrUtil.DOT + response.getCode());
            // 设置分组ID
            String groupId = tableGroupAttrService.getGroupIdByRawTableAttrId(response.getId());
            response.setGroupId(StringUtils.isNotBlank(groupId) ? groupId : table.getId());
            attrRes.add(response);
        }
        return attrRes;
    }

    /**
     * 查询可选字段列表 (结构为：表-字段)
     *
     * @param rawId 台账ID
     * @return list
     */
    public List<RawTableVo.OptionParam> getTableWithAttrTree(String rawId) {
        List<RawTable> tables = this.listByRawId(rawId);
        List<RawTableVo.OptionParam> res = new ArrayList<>();
        for (RawTable table : tables) {
            RawTableVo.OptionParam param = BeanUtil.copy(table, RawTableVo.OptionParam.class);
            param.setId(table.getId());
            param.setName(table.getName());
            // 查询表字段列表
            List<RawTableAttrVo.DetailResponse> tableAttrs = BeanUtil.copyList(rawTableAttrService.listByRawTableId(table.getId()), RawTableAttrVo.DetailResponse.class);
            param.setTableAttrs(tableAttrs);
            res.add(param);
        }
        return res;
    }

    /**
     * 校验字段是否存在
     *
     * @param rawId 台账ID
     * @param field 字段
     * @return bool
     */
    public boolean checkAttrExist(String rawId, String field) {
        boolean f;
        List<RawTable> tables = listByRawId(rawId);
        if (tables.isEmpty()) {
            return false;
        }
        for (RawTable table : tables) {
            f = rawTableAttrService.checkAttrExist(table.getId(), field);
            if (!f) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取默认显示的字段ID
     *
     * @param rawId 台账ID
     * @return 字段ID
     */
    public List<String> defaultShowAttrListByRawId(String rawId) {
        List<String> res = new ArrayList<>();
        List<RawTable> tables = listByRawId(rawId);
        for (RawTable table : tables) {
            List<String> ids = rawTableAttrService.defaultShowAttrListByRawTableId(table.getId());
            res.addAll(ids);
        }
        return res;
    }

    /**
     * 根据维度表ID查询关联的台账表列表
     *
     * @param companyId 公司ID
     * @param dimId     维度表ID
     * @return 台账表列表
     */
    public List<RawTable> getListByDimId(String companyId, String dimId) {
        QueryWrapper<RawTable> wrapper = new QueryWrapper<>();
        wrapper.select("id", "raw_id").eq("company_id", companyId)
                .eq("if_deleted", false)
                .eq("dim_id", dimId);
        return super.list(wrapper);
    }

    public RawTable getHasDwsByRawId(String companyId, String rawId) {
        QueryWrapper<RawTable> wrapper = new QueryWrapper<>();
        wrapper.select("id", "raw_id", "dws_id")
                .eq("company_id", companyId)
                .eq("raw_id", rawId)
                .eq("if_deleted", false)
                .isNotNull("dws_id").last("LIMIT 1");
        return super.getOne(wrapper);
    }


    /**
     * 指标删除--修改台账信息
     *
     * @param companyId 公司ID
     * @param dimId     维度表ID
     * @param dwsId     指标ID
     * @return bool
     */
    public boolean updateDwsId(String companyId, String dimId, String dwsId) {
        QueryWrapper<RawTable> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("dim_id", dimId)
                .eq("if_deleted", false);
        List<RawTable> tableList = super.list(wrapper);
        List<String> tableIds = tableList.stream().collect(ArrayList::new, (list, rawTable) -> list.add(rawTable.getId()), ArrayList::addAll);
        return rawTableAttrService.updateDwsInfo(tableIds, dwsId);
    }

    public RawTable getOneByRawIdAndName(@NotNull String companyId, @NotNull String rawId, @NotNull String name) {
        QueryWrapper<RawTable> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("raw_id", rawId)
                .eq("if_deleted", false)
                .eq("name", name)
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public List<RawTable> getListByRawIdAndCompanyId(@NotNull String companyId, @NotNull String rawId) {
        QueryWrapper<RawTable> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("raw_id", rawId)
                .eq("if_deleted", false);
        return super.list(wrapper);
    }
}