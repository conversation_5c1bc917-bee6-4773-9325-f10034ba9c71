package com.taiyi.common.auth.rpc;

import com.taiyi.common.config.PathConfig;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rpc/path")
public class PathRpcController {

    @GetMapping("/auth/allow")
    public Set<String> authAllowPaths() {
        return new HashSet<>(PathConfig.authAllowPathList());
    }

}
