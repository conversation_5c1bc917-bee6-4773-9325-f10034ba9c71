package com.taiyi.shuduoduo.ims.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.RawTableGroup;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface RawTableGroupDao extends CommonMysqlMapper<RawTableGroup> {

    @Select("SELECT a.* FROM \n" +
            "\t`ims_raw_table_group` AS  a\n" +
            "\tLEFT JOIN `ims_raw_table_group_attr` AS b ON a.id = b.raw_table_group_id ${ew.customSqlSegment}")
    RawTableGroup getNameByRawTableIdAndTableAttrId(@Param("ew") Wrapper wrapper);
}