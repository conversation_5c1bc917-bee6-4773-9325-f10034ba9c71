package com.taiyi.shuduoduo.ims.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DimDwsVo {

    /**
     * 关联维度/事实表字段ID
     */
    private String dimAttrId;

    /**
     * 关联维度/事实表ID
     */
    private String dimId;

    /**
     * 关联的维度信息
     */
    private String dimension;

    /**
     * 关联指标ID
     */
    private String dwsId;

    /**
     * 排序
     */
    private Long orderBy;

    /**
     * 统计周期
     */
    private String periodValue;

    /**
     * 挂接类型
     */
    private String unionType;

    /**
     * 可用分析
     */
    private String analysis;

    @Data
    public static class InsertParam{

        private String dwsId;

        private List<DwsVo.DimensionUnion> unionList;
    }

    @Data
    public static class DetailVo {
        /**
         * 挂接类型
         */
        private String unionType;

        /**
         * 关联维度/事实表板块
         */
        private String plateId;

        /**
         * 关联维度/事实表数据域
         */
        private String dataFieldId;

        /**
         * 关联维度/事实表业务过程
         */
        private String businessProcessId;

        /**
         * 关联维度/事实表类型
         */
        private Integer dimType;

        /**
         * 关联维度/事实表ID
         */
        private String dimId;

        /**
         * 关联维度/事实表
         */
        private String dimName;

        /**
         * 关联维度/事实表code
         */
        private String dimCode;

        /**
         * 关联维度/事实表字段
         */
        private String dimAttrName;

        /**
         * 关联维度/事实表字段code
         */
        private String dimAttrCode;
    }
}
