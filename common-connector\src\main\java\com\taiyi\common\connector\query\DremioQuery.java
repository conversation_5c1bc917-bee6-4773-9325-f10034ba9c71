package com.taiyi.common.connector.query;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.taiyi.common.connector.db.ConnectionPool;
import com.taiyi.common.connector.db.DbConnection;
import com.taiyi.common.connector.entity.DbEntity;
import com.taiyi.common.connector.entity.DremioEntity;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;

/**
 * Dremio 数据湖
 *
 * <AUTHOR>
 */
public class DremioQuery implements DbQuery {

    private static Logger logger = LoggerFactory.getLogger(DremioQuery.class);

    private DremioEntity entity;

    private ResultSet resultSet;

    public DremioQuery(DbDto dbDto) {
        entity = BeanUtil.copy(dbDto, DremioEntity.class);
    }

    DbConnection<DbEntity> dbConnection = new ConnectionPool<>();

    public DremioQuery() {

    }

    /**
     * 关闭结果集
     *
     * @throws SQLException e
     */
    @Override
    public void close() throws SQLException {
        if (ObjectUtil.isNotEmpty(resultSet)) {
            logger.info("关闭resultSet{}", resultSet);
            resultSet.close();
            logger.info("resultSet closed...");
        }
    }

    /**
     * 获取数仓连接
     *
     * @return boolean
     */
    @Override
    public boolean getConnection() {
        return ObjectUtil.isNotEmpty(dbConnection.getConnection(entity));
    }

    /**
     * 获取表
     *
     * @return list
     */
    @Override
    public List<String> getTables() throws SQLException {
        logger.info("获取数据库表,链接地址为{},数据库为{},schema为{},数据库类型为{}", entity.getHost(), entity.getDatabase(), entity.getSchema(), entity.getType());
        List<String> list = new ArrayList<>();
        resultSet = dbConnection.getConnection(entity).createStatement().executeQuery("SHOW TABLES IN " + entity.getDatabase());
        while (resultSet.next()) {
            list.add(resultSet.getString(1));
        }
        close();
        return list;
    }

    /**
     * 获取数据库表字段
     *
     * @param tableName 表
     * @return List
     * @throws SQLException e
     */
    @Override
    public List<String> getColumns(String tableName) throws SQLException {
        logger.info("获取数据库表字段,链接地址为{},数据库为{},表名为{}", entity.getHost(), entity.getDatabase(), tableName);
        List<String> list = new ArrayList<>();
        resultSet = dbConnection.getConnection(entity).createStatement().executeQuery("SHOW COLUMNS FROM " + tableName);
        while (resultSet.next()) {
            list.add(resultSet.getString(1));
        }
        close();
        return list;
    }

    /**
     * 获取数据库表字段及字段类型
     *
     * @param tableName 表名
     * @return List
     * @throws SQLException e
     */
    @Override
    public List<Map<String, Object>> getColumnsAndType(String tableName) throws SQLException {
        logger.info("getColumnsAndType获取数据库表字段及字段类型,链接地址为{},数据库为{},表名为{}", entity.getHost(), entity.getDatabase(), tableName);
        List<Map<String, Object>> list = new ArrayList<>();
        resultSet = dbConnection.getConnection(entity).createStatement().executeQuery("SHOW COLUMNS FROM " + entity.getDatabase() + "." + tableName);
        ResultSetMetaData metaData = resultSet.getMetaData();
        while (resultSet.next()) {
            Map<String, Object> map = new HashMap<>();
            map.put("columnName", resultSet.getString(1));
            for (int i = 0; i < metaData.getColumnCount(); i++) {
                map.put("dataType", metaData.getColumnTypeName(i + 1));
                break;
            }
            list.add(map);
        }
        close();
        return list;
    }

    /**
     * 条件查询
     *
     * @param tableName   表名
     * @param queryColumn 查询列
     * @param queryParam  查询条件
     * @param sortParam   排序条件
     * @return list
     * @throws SQLException sqlError
     */
    @Override
    public List<Map<String, Object>> query(String tableName, List<String> queryColumn, List<Map<String, Object>> queryParam, List<Map<String, Object>> sortParam) throws SQLException {
        logger.info("数据库表查询,数据库地址为{},数据库为{},查询表为{}", entity.getHost(), entity.getDatabase(), tableName);
        StringBuilder builder = new StringBuilder();
        builder.append("select ");
        if (ObjectUtil.isEmpty(queryColumn)) {
            builder.append("* ");
        } else {
            for (String s : queryColumn) {
                builder.append(s).append(",");
            }
            builder.deleteCharAt(builder.length() - 1);
        }
        builder.append(" from ").append(tableName);
        if (ObjectUtil.isNotEmpty(queryParam)) {
            for (Map<String, Object> map : queryParam) {
                for (String key : map.keySet()) {
                    builder.append(" where ");
                    if (key.contains("like") || key.contains("LIKE")) {
                        builder.append(key).append(" '%").append(map.get(key)).append("%' ");
                    } else {
                        builder.append(key).append("'").append(map.get(key)).append("' ");
                    }
                }
            }
        }
        if (ObjectUtil.isNotEmpty(sortParam)) {
            for (Map<String, Object> map : sortParam) {
                builder.append("order by ");
                for (String key : map.keySet()) {
                    builder.append(key).append(" ").append(map.get(key));
                }
            }
        }
        logger.info("sql:{}", builder.toString());
        resultSet = dbConnection.getConnection(entity).createStatement().executeQuery(builder.toString());
        return convertList(resultSet);
    }

    /**
     * 数据查询
     *
     * @param sql sql
     * @return List
     */
    @Override
    public List<Map<String, Object>> querySql(String sql) throws SQLException {
        logger.info("数据库表查询,数据库地址为{},数据库为{},查询SQL为{}", entity.getHost(), entity.getDatabase(), sql);
        try {
            resultSet = dbConnection.getConnection(entity).createStatement().executeQuery(sql);
        } catch (Exception e) {
            String stacktrace = ExceptionUtil.stacktraceToString(e);
            List<Map<String, Object>> result = new ArrayList<>();
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("error", stacktrace.length() > 200 ? stacktrace.substring(0, 200) : stacktrace);
            result.add(map);
            return result;
        }
        return convertList(resultSet);
    }

    /**
     * 数据集处理
     *
     * @param resultSet 结果集
     * @return List
     * @throws SQLException e
     */
    public List<Map<String, Object>> convertList(ResultSet resultSet) throws SQLException {
        List<Map<String, Object>> result = new ArrayList<>();
        ResultSetMetaData metaData = resultSet.getMetaData();
        int count = metaData.getColumnCount();
        while (resultSet.next()) {
            Map<String, Object> map = new LinkedHashMap<>();
            for (int i = 1; i <= count; i++) {
                map.put(metaData.getColumnName(i), resultSet.getObject(i));
            }
            result.add(map);
        }
        close();
        return result;
    }
}
