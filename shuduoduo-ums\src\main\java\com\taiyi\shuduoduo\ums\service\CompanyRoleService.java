package com.taiyi.shuduoduo.ums.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ums.entity.CompanyRole;
import com.taiyi.shuduoduo.ums.dao.CompanyRoleDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公司角色关联业务层
 *
 * <AUTHOR>
 */
@Service
public class CompanyRoleService extends CommonMysqlService<CompanyRoleDao, CompanyRole> {

    @Autowired
    private CompanyRoleDao companyRoleDao;

    @Override
    public Class<CompanyRole> getEntityClass() {
        return CompanyRole.class;
    }

    /**
     * 根据公司id，查找角色列表
     *
     * @param companyId 公司id
     * @return 角色id列表
     */
    public List<String> getCompanyRoleList(String companyId) {
        QueryWrapper<CompanyRole> wrapper = new QueryWrapper<>();
        wrapper.eq("com_id", companyId);
        List<CompanyRole> userCompanies = companyRoleDao.selectList(wrapper);
        if (userCompanies != null) {
            return userCompanies.stream().map(CompanyRole::getRoleId).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 根据公司ID和角色ID查询关联信息
     *
     * @param companyId 公司ID
     * @param roleId    角色ID
     * @return T
     */
    public CompanyRole getByComIdAndRoleId(String companyId, String roleId) {
        QueryWrapper<CompanyRole> wrapper = new QueryWrapper<>();
        wrapper.eq("com_id", companyId).eq("role_id", roleId).last("limit 1");
        return getOne(wrapper);
    }
}