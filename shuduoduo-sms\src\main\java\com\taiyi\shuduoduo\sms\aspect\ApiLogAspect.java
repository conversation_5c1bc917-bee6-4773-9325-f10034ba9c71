package com.taiyi.shuduoduo.sms.aspect;

import com.alibaba.fastjson.JSON;
import com.taiyi.common.aspect.ApiLogPointCut;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.IpUtil;
import com.taiyi.shuduoduo.sms.entity.Log;
import com.taiyi.shuduoduo.sms.service.LogService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 日志切面处理
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class ApiLogAspect {

    private Logger logger = LoggerFactory.getLogger(ApiLogAspect.class);

    @Autowired
    LogService logService;


    @Pointcut("execution(public * com.taiyi.shuduoduo.sms.controller.*.*(..))")
    public void apiLogPointCutSms() {
    }


    @Before("apiLogPointCutSms()")
    public void before(JoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取目标类上的目标注解
        Method method = signature.getMethod();
        ApiLogPointCut annotation = method.getAnnotation(ApiLogPointCut.class);
        if (annotation != null) {
            // 记录操作日志
            Log logDTO = new Log();
            logDTO.setCompanyId(CurrentUserUtil.get().getCompanyId());
            logDTO.setUsername(CurrentUserUtil.get().getRealName());
            logDTO.setPath(request.getContextPath() + request.getServletPath());
            logDTO.setIp(IpUtil.getRealIP(request));
            logDTO.setDoAction(annotation.name());
            logDTO.setActionDescription(annotation.description());
            logDTO.setParameters(JSON.toJSONString(joinPoint.getArgs()));
            logService.save(logDTO);
        }
    }

}
