package com.taiyi.shuduoduo.ums.api.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UserDTO {

    /**
     * 主键id
     */
    private String id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 真实名称
     */
    private String realName;

    /**
     * 三方ID
     */
    private String thirdUserId;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 后台管理端角色名称
     */
    private String roleName;

    /**
     * 用户头像
     */
    private String avatarUri;

    /**
     * 数据湖密码摘要
     */
    private String dremioPwdDigest;

    @Data
    public static class DremioDTO {
        private String id;
        /**
         * 数据湖密码摘要
         */
        private String dremioPwdDigest;
    }

}
