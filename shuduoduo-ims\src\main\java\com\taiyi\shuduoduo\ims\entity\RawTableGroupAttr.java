package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw_table_group_attr")
public class RawTableGroupAttr extends CommonMySqlEntity {
    private String companyId;

    /**
     * 台账表分组ID
     */
    private String rawTableGroupId;

    /**
     * 台账表字段ID
     */
    private String rawTableAttrId;

    private Long orderBy;

    private Boolean ifDeleted;

}