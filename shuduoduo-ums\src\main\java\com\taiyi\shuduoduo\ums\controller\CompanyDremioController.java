package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ums.entity.CompanyDremio;
import com.taiyi.shuduoduo.ums.service.CompanyDremioService;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.entity.MyQuery;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ums.vo.CompanyDremioVo;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 企业数据湖信息控制器
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/dremio")
@Validated
public class CompanyDremioController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private CompanyDremioService companyDremioService;

    /**
     * 新增数据
     *
     * @param t
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated CompanyDremio t) {
        boolean f;
        try {
            f = companyDremioService.save(t);
        } catch (Exception e) {
            logger.error(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            CompanyDremio t = companyDremioService.getById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 删除数据湖信息
     *
     * @param id 数据湖信息ID
     * @return bool
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = companyDremioService.deleteById(id);
        } catch (Exception e) {
            logger.error("删除数据湖信息失败:{}",ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取数据湖信息
     *
     * @return 数据湖信息
     */
    @GetMapping("/getDremioInfo")
    public ResponseEntity<ResponseVo.ResponseBean> getDremioInfoByComId() {
        try {
            CompanyDremio t = companyDremioService.getDremioInfoByComId(CurrentUserUtil.get().getCompanyId());
            return ResponseVo.response(MessageCode.SUCCESS, BeanUtil.copy(t, CompanyDremioVo.class));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }
}
