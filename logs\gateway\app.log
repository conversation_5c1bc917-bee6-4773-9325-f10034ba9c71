2025-07-19 17:14:00.113 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 17:14:00.116 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:14:00.118 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource gateway-properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:00.119 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:00.119 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:00.120 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:14:00.120 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:14:00.120 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:01.578 [main] INFO  c.t.c.gateway.GatewayApplication - The following profiles are active: local
2025-07-19 17:14:02.138 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 17:14:02.176 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=cb8ba6c0-bf1c-3c34-8d5b-bb710e269414
2025-07-19 17:14:02.204 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 17:14:02.204 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:14:02.204 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource gateway-properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:02.205 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:02.205 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:14:02.205 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:14:02.205 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:02.206 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:02.206 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:02.206 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:02.206 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:14:02.206 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:14:02.283 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:14:02.286 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:14:02.287 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:14:02.640 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 17:14:02.648 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 17:14:02.650 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 17:14:05.003 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-19 17:14:05.003 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-19 17:14:05.003 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-19 17:14:05.003 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-19 17:14:05.004 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-19 17:14:05.004 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-19 17:14:05.004 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-19 17:14:05.004 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-19 17:14:05.004 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-19 17:14:05.004 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBodyPredicateFactory]
2025-07-19 17:14:05.004 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-19 17:14:05.004 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-19 17:14:05.005 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-19 17:14:05.210 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 17:14:05.210 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 17:14:05.217 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 17:14:05.217 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 17:14:06.764 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 17:14:06.796 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 17:14:06.896 [main] INFO  o.s.b.a.s.r.ReactiveUserDetailsServiceAutoConfiguration - 

Using generated security password: 5f62453d-0d7a-4c97-b77e-1e9cdc90107d

2025-07-19 17:14:08.163 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port(s): 9999
2025-07-19 17:14:09.470 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP common-gateway *************:9999 register finished
2025-07-19 17:14:09.486 [main] INFO  c.t.c.gateway.GatewayApplication - Started GatewayApplication in 11.263 seconds (JVM running for 11.814)
2025-07-19 17:46:54.080 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 17:46:54.082 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 17:46:54.084 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-19 18:32:39.792 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 18:32:39.797 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 18:32:39.799 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource gateway-properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:39.800 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:39.800 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:39.800 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 18:32:39.800 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 18:32:39.801 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:41.317 [main] INFO  c.t.c.gateway.GatewayApplication - The following profiles are active: local
2025-07-19 18:32:41.884 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 18:32:41.924 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=cb8ba6c0-bf1c-3c34-8d5b-bb710e269414
2025-07-19 18:32:41.948 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource gateway-properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:32:41.949 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 18:32:42.044 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 18:32:42.047 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 18:32:42.049 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 18:32:42.401 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 18:32:42.406 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 18:32:42.409 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 18:32:44.733 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-19 18:32:44.733 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-19 18:32:44.733 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-19 18:32:44.733 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-19 18:32:44.733 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-19 18:32:44.733 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-19 18:32:44.734 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-19 18:32:44.734 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-19 18:32:44.734 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-19 18:32:44.734 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBodyPredicateFactory]
2025-07-19 18:32:44.734 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-19 18:32:44.734 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-19 18:32:44.734 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-19 18:32:44.947 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 18:32:44.947 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 18:32:44.955 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 18:32:44.955 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 18:32:46.463 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 18:32:46.504 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 18:32:46.622 [main] INFO  o.s.b.a.s.r.ReactiveUserDetailsServiceAutoConfiguration - 

Using generated security password: 05220693-0eb6-4160-8684-596056641ad6

2025-07-19 18:32:47.889 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port(s): 9999
2025-07-19 18:32:49.173 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP common-gateway *************:9999 register finished
2025-07-19 18:32:49.188 [main] INFO  c.t.c.gateway.GatewayApplication - Started GatewayApplication in 11.323 seconds (JVM running for 11.816)
2025-07-19 18:33:15.634 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 18:33:15.635 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 18:33:15.639 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-19 18:33:25.841 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 18:33:25.845 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 18:33:25.847 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource gateway-properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:25.847 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:25.847 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:25.848 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 18:33:25.848 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 18:33:25.848 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:27.436 [main] INFO  c.t.c.gateway.GatewayApplication - The following profiles are active: local
2025-07-19 18:33:28.295 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 18:33:28.346 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=cb8ba6c0-bf1c-3c34-8d5b-bb710e269414
2025-07-19 18:33:28.378 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 18:33:28.379 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 18:33:28.380 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource gateway-properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:28.381 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:28.381 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 18:33:28.381 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 18:33:28.381 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:28.382 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:28.382 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:28.382 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:28.382 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 18:33:28.382 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 18:33:28.473 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 18:33:28.476 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 18:33:28.478 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 18:33:29.000 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 18:33:29.008 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 18:33:29.013 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 18:33:31.689 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-19 18:33:31.689 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-19 18:33:31.689 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-19 18:33:31.689 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-19 18:33:31.689 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-19 18:33:31.689 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-19 18:33:31.689 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-19 18:33:31.689 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-19 18:33:31.689 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-19 18:33:31.689 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBodyPredicateFactory]
2025-07-19 18:33:31.690 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-19 18:33:31.690 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-19 18:33:31.690 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-19 18:33:31.981 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 18:33:31.982 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 18:33:31.997 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 18:33:31.998 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 18:33:34.173 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 18:33:34.208 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 18:33:34.321 [main] INFO  o.s.b.a.s.r.ReactiveUserDetailsServiceAutoConfiguration - 

Using generated security password: 9d4cd203-a2a9-4def-9a5b-211571917c48

2025-07-19 18:33:35.664 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port(s): 9999
2025-07-19 18:33:36.931 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP common-gateway *************:9999 register finished
2025-07-19 18:33:36.948 [main] INFO  c.t.c.gateway.GatewayApplication - Started GatewayApplication in 13.083 seconds (JVM running for 13.649)
2025-07-19 18:33:58.964 [reactor-http-nio-4] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - http request: a4dd1361-fa9b-4ff2-8b1d-9f03de57d937,  method: GET , path : /shuduoduo/ims/query/topic/total, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6IuaWueS_iiIsImFjY2Vzc190b2tlbiI6IiIsImlmX2JhY2tlbmQiOmZhbHNlLCJjb21wYW55X2lkIjoiODY2OTIyYWNhYzViNDFmNjhiZTM4NTRlZTBhN2VhOTgiLCJvcGVuSWQiOiJjNGIxNmM2NjNjMzE0NGY3OWMzOTEwZGJlOGQ1OTE2MyIsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoi5pa55L-KIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLmlrnkv4oiLCJJZCI6ImM0YjE2YzY2M2MzMTQ0Zjc5YzM5MTBkYmU4ZDU5MTYzIiwiZXhwIjoxNzU0NzkyMzA4LCJleHBpcmVzX2luIjo0MzIwMH0.PvspThRq23wyHgfNzRYZoBKmFEV_owos5AXY8YYFqSk", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 18:33:58.964 [reactor-http-nio-3] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - http request: 79423577-9ca8-4770-9035-1386427335bb,  method: GET , path : /shuduoduo/ims/plate/home/<USER>"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6IuaWueS_iiIsImFjY2Vzc190b2tlbiI6IiIsImlmX2JhY2tlbmQiOmZhbHNlLCJjb21wYW55X2lkIjoiODY2OTIyYWNhYzViNDFmNjhiZTM4NTRlZTBhN2VhOTgiLCJvcGVuSWQiOiJjNGIxNmM2NjNjMzE0NGY3OWMzOTEwZGJlOGQ1OTE2MyIsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoi5pa55L-KIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLmlrnkv4oiLCJJZCI6ImM0YjE2YzY2M2MzMTQ0Zjc5YzM5MTBkYmU4ZDU5MTYzIiwiZXhwIjoxNzU0NzkyMzA4LCJleHBpcmVzX2luIjo0MzIwMH0.PvspThRq23wyHgfNzRYZoBKmFEV_owos5AXY8YYFqSk", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 18:33:58.964 [reactor-http-nio-5] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - http request: 02e89496-2634-450e-a7c9-7226e660a521,  method: POST , path : /shuduoduo/ims/query/topic/page, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"52", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6IuaWueS_iiIsImFjY2Vzc190b2tlbiI6IiIsImlmX2JhY2tlbmQiOmZhbHNlLCJjb21wYW55X2lkIjoiODY2OTIyYWNhYzViNDFmNjhiZTM4NTRlZTBhN2VhOTgiLCJvcGVuSWQiOiJjNGIxNmM2NjNjMzE0NGY3OWMzOTEwZGJlOGQ1OTE2MyIsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoi5pa55L-KIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLmlrnkv4oiLCJJZCI6ImM0YjE2YzY2M2MzMTQ0Zjc5YzM5MTBkYmU4ZDU5MTYzIiwiZXhwIjoxNzU0NzkyMzA4LCJleHBpcmVzX2luIjo0MzIwMH0.PvspThRq23wyHgfNzRYZoBKmFEV_owos5AXY8YYFqSk", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 18:33:58.964 [reactor-http-nio-2] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - http request: c95391f6-fd26-4113-a951-7818e2ab8000,  method: POST , path : /shuduoduo/sms/log, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"177", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6IuaWueS_iiIsImFjY2Vzc190b2tlbiI6IiIsImlmX2JhY2tlbmQiOmZhbHNlLCJjb21wYW55X2lkIjoiODY2OTIyYWNhYzViNDFmNjhiZTM4NTRlZTBhN2VhOTgiLCJvcGVuSWQiOiJjNGIxNmM2NjNjMzE0NGY3OWMzOTEwZGJlOGQ1OTE2MyIsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoi5pa55L-KIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLmlrnkv4oiLCJJZCI6ImM0YjE2YzY2M2MzMTQ0Zjc5YzM5MTBkYmU4ZDU5MTYzIiwiZXhwIjoxNzU0NzkyMzA4LCJleHBpcmVzX2luIjo0MzIwMH0.PvspThRq23wyHgfNzRYZoBKmFEV_owos5AXY8YYFqSk", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 18:34:03.081 [reactor-http-nio-4] INFO  c.n.config.ChainedDynamicProperty - Flipping property: common-auth.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 18:34:03.111 [reactor-http-nio-4] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: common-auth instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=common-auth,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-19 18:34:03.117 [reactor-http-nio-4] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-07-19 18:34:03.136 [reactor-http-nio-4] INFO  c.n.config.ChainedDynamicProperty - Flipping property: common-auth.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 18:34:03.138 [reactor-http-nio-4] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client common-auth initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=common-auth,current list of Servers=[*************:9977],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:*************:9977;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@2b7d76fa
2025-07-19 18:34:03.577 [reactor-http-nio-5] WARN  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - Gateway 获取允许访问的path配置发生异常，异常类型：feign.FeignException$Forbidden: [403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]，异常信息：[403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]
2025-07-19 18:34:03.577 [reactor-http-nio-4] WARN  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - Gateway 获取允许访问的path配置发生异常，异常类型：feign.FeignException$Forbidden: [403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]，异常信息：[403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]
2025-07-19 18:34:03.577 [reactor-http-nio-2] WARN  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - Gateway 获取允许访问的path配置发生异常，异常类型：feign.FeignException$Forbidden: [403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]，异常信息：[403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]
2025-07-19 18:34:11.946 [reactor-http-nio-2] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 18:34:11.946 [reactor-http-nio-3] WARN  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - Gateway 获取允许访问的path配置发生异常，异常类型：feign.FeignException$Forbidden: [403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]，异常信息：[403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]
2025-07-19 18:34:11.946 [reactor-http-nio-4] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 18:34:11.947 [reactor-http-nio-3] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 18:34:11.947 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: common-auth.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 18:34:11.946 [reactor-http-nio-5] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 18:34:42.174 [reactor-http-nio-2] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - http request: c69ed106-3e53-4712-9017-7fa7a7b8ca74,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[演示]}
2025-07-19 18:34:50.347 [reactor-http-nio-2] WARN  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - Gateway 获取允许访问的path配置发生异常，异常类型：feign.FeignException$Forbidden: [403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]，异常信息：[403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]
2025-07-19 18:35:21.098 [reactor-http-nio-2] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 18:36:27.409 [reactor-http-nio-2] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - http request: c7909e7b-a767-49e7-9db4-1882d7b5d2cb,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[演示]}
2025-07-19 18:37:01.084 [reactor-http-nio-2] WARN  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - Gateway 获取允许访问的path配置发生异常，异常类型：feign.FeignException$Forbidden: [403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]，异常信息：[403] during [GET] to [http://common-auth/common/auth/rpc/path/auth/allow] [PathRpcService#authAllowPaths()]: [{"code":"B200","data":{},"message":"用户访问权限异常","tips":"用户访问权限异常"}]
2025-07-19 18:37:01.085 [reactor-http-nio-2] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 18:40:39.111 [reactor-http-nio-6] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - http request: 5627385a-c914-4c59-b72d-8441c9976738,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[y]}
2025-07-19 18:40:39.115 [reactor-http-nio-6] WARN  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - Gateway 获取允许访问的path配置发生异常，异常类型：java.lang.RuntimeException: com.netflix.client.ClientException: Load balancer does not have available server for client: common-auth，异常信息：com.netflix.client.ClientException: Load balancer does not have available server for client: common-auth
2025-07-19 18:40:39.115 [reactor-http-nio-6] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 18:40:39.867 [reactor-http-nio-7] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - http request: 0d7f4230-d93b-41ae-a360-408bca6806b8,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[y'n]}
2025-07-19 18:40:39.867 [reactor-http-nio-7] WARN  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - Gateway 获取允许访问的path配置发生异常，异常类型：java.lang.RuntimeException: com.netflix.client.ClientException: Load balancer does not have available server for client: common-auth，异常信息：com.netflix.client.ClientException: Load balancer does not have available server for client: common-auth
2025-07-19 18:40:39.867 [reactor-http-nio-7] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 18:41:26.719 [reactor-http-nio-7] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - http request: 19f4c544-1974-4cf1-aa08-036fc8cf771d,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[y]}
2025-07-19 18:41:29.343 [reactor-http-nio-6] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - http request: 9655e2f4-ba5b-4f65-b180-f57fc5659efa,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[演示]}
2025-07-19 18:41:29.561 [reactor-http-nio-6] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - Gateway 获取允许访问的path配置成功，获取结果：[/shuduoduo/*/task/**, /shuduoduo/nocodb/openapi/**, /shuduoduo/ums/swagger-ui/**, /shuduoduo/ims/auth/nocodb/info/**, /shuduoduo/ims/test/**, /shuduoduo/ums/openapi/**, /shuduoduo/ums/api/tledu/**, /shuduoduo/sms/openapi/**, /shuduoduo/sms/test/**, /shuduoduo/ums/api/zj/**, /shuduoduo/ums/login/**, /shuduoduo/ims/openapi/**, /shuduoduo/ims/query/data/export/**, /shuduoduo/ims/swagger-ui/**, /shuduoduo/sms/swagger-ui/**, /shuduoduo/ums/company/**, /shuduoduo/ums/api/**, /shuduoduo/ums/api/wx/**, /shuduoduo/ums/test/**, /shuduoduo/ims/nocodb/table/**]
2025-07-19 18:41:29.561 [reactor-http-nio-6] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - 白名单免认证接口
2025-07-19 18:41:30.237 [reactor-http-nio-7] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - Gateway 获取允许访问的path配置成功，获取结果：[/shuduoduo/*/task/**, /shuduoduo/nocodb/openapi/**, /shuduoduo/ums/swagger-ui/**, /shuduoduo/ims/auth/nocodb/info/**, /shuduoduo/ims/test/**, /shuduoduo/ums/openapi/**, /shuduoduo/ums/api/tledu/**, /shuduoduo/sms/openapi/**, /shuduoduo/sms/test/**, /shuduoduo/ums/api/zj/**, /shuduoduo/ums/login/**, /shuduoduo/ims/openapi/**, /shuduoduo/ims/query/data/export/**, /shuduoduo/ims/swagger-ui/**, /shuduoduo/sms/swagger-ui/**, /shuduoduo/ums/company/**, /shuduoduo/ums/api/**, /shuduoduo/ums/api/wx/**, /shuduoduo/ums/test/**, /shuduoduo/ims/nocodb/table/**]
2025-07-19 18:41:32.454 [reactor-http-nio-7] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$a76dba7d - 白名单免认证接口
2025-07-19 18:41:32.485 [reactor-http-nio-7] INFO  c.n.config.ChainedDynamicProperty - Flipping property: shuduoduo-ums.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 18:41:32.487 [reactor-http-nio-7] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: shuduoduo-ums instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=shuduoduo-ums,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-19 18:41:32.488 [reactor-http-nio-7] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-07-19 18:41:32.491 [reactor-http-nio-7] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client shuduoduo-ums initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=shuduoduo-ums,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@5ffe419e
2025-07-19 18:41:32.518 [reactor-http-nio-7] INFO  c.t.c.g.h.ErrorExceptionHandler - request :19f4c544-1974-4cf1-aa08-036fc8cf771d, 请求处理出错，错误信息为：503 SERVICE_UNAVAILABLE "Unable to find instance for shuduoduo-ums"
2025-07-19 18:41:32.518 [reactor-http-nio-6] INFO  c.t.c.g.h.ErrorExceptionHandler - request :9655e2f4-ba5b-4f65-b180-f57fc5659efa, 请求处理出错，错误信息为：503 SERVICE_UNAVAILABLE "Unable to find instance for shuduoduo-ums"
2025-07-19 18:41:32.529 [reactor-http-nio-6] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [8c5c224e-20]  500 Server Error for HTTP POST "/shuduoduo/ums/company/list?keyWord=%E6%BC%94%E7%A4%BA"
org.springframework.cloud.gateway.support.NotFoundException: 503 SERVICE_UNAVAILABLE "Unable to find instance for shuduoduo-ums"
	at org.springframework.cloud.gateway.support.NotFoundException.create(NotFoundException.java:46)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	|_ checkpoint ⇢ org.springframework.web.cors.reactive.CorsWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ org.springframework.cloud.gateway.filter.WeightCalculatorWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ org.springframework.boot.actuate.metrics.web.reactive.server.MetricsWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ HTTP POST "/shuduoduo/ums/company/list?keyWord=%E6%BC%94%E7%A4%BA" [ExceptionHandlingWebHandler]
Stack trace:
		at org.springframework.cloud.gateway.support.NotFoundException.create(NotFoundException.java:46)
		at org.springframework.cloud.gateway.filter.LoadBalancerClientFilter.filter(LoadBalancerClientFilter.java:86)
		at org.springframework.cloud.gateway.handler.FilteringWebHandler$GatewayFilterAdapter.filter(FilteringWebHandler.java:138)
		at org.springframework.cloud.gateway.filter.OrderedGatewayFilter.filter(OrderedGatewayFilter.java:44)
		at org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.lambda$filter$0(FilteringWebHandler.java:118)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:44)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:150)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:67)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:76)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:274)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:851)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:114)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:67)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1782)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:144)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:114)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:76)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:274)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:851)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:73)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:173)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1782)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:140)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2344)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:103)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:441)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onNext(FluxConcatMap.java:243)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:91)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:38)
		at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:267)
		at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:225)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:120)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:228)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onSubscribe(FluxDematerialize.java:70)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:161)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:441)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:211)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:161)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:65)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:514)
		at reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:267)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:462)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:170)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:296)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:750)
2025-07-19 18:41:32.529 [reactor-http-nio-7] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [2797bac2-18]  500 Server Error for HTTP POST "/shuduoduo/ums/company/list?keyWord=y"
org.springframework.cloud.gateway.support.NotFoundException: 503 SERVICE_UNAVAILABLE "Unable to find instance for shuduoduo-ums"
	at org.springframework.cloud.gateway.support.NotFoundException.create(NotFoundException.java:46)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	|_ checkpoint ⇢ org.springframework.web.cors.reactive.CorsWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ org.springframework.cloud.gateway.filter.WeightCalculatorWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ org.springframework.boot.actuate.metrics.web.reactive.server.MetricsWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ HTTP POST "/shuduoduo/ums/company/list?keyWord=y" [ExceptionHandlingWebHandler]
Stack trace:
		at org.springframework.cloud.gateway.support.NotFoundException.create(NotFoundException.java:46)
		at org.springframework.cloud.gateway.filter.LoadBalancerClientFilter.filter(LoadBalancerClientFilter.java:86)
		at org.springframework.cloud.gateway.handler.FilteringWebHandler$GatewayFilterAdapter.filter(FilteringWebHandler.java:138)
		at org.springframework.cloud.gateway.filter.OrderedGatewayFilter.filter(OrderedGatewayFilter.java:44)
		at org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.lambda$filter$0(FilteringWebHandler.java:118)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:44)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:150)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:67)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:76)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:274)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:851)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:114)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:67)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1782)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:144)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:114)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:76)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:274)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:851)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:73)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:173)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1782)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:140)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2344)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:103)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:441)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onNext(FluxConcatMap.java:243)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:91)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:38)
		at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:267)
		at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:225)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:120)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:228)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onSubscribe(FluxDematerialize.java:70)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:161)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:441)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:211)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:161)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:65)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:514)
		at reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:267)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:462)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:170)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:296)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:750)
2025-07-19 18:41:45.764 [Thread-30] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-07-19 18:41:45.767 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 18:41:45.769 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 18:41:45.772 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-19 21:30:31.332 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:30:31.336 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:30:31.338 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource gateway-properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:31.338 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:31.338 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:31.338 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:30:31.338 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:30:31.338 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:33.035 [main] INFO  c.t.c.gateway.GatewayApplication - The following profiles are active: local
2025-07-19 21:30:33.720 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 21:30:33.763 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=cb8ba6c0-bf1c-3c34-8d5b-bb710e269414
2025-07-19 21:30:33.793 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:30:33.793 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:30:33.793 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource gateway-properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:33.793 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:33.793 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:30:33.794 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:30:33.794 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:33.794 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:33.794 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:33.794 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:33.794 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:30:33.794 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:30:33.878 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:30:33.880 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:30:33.882 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'deferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:30:34.382 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 21:30:34.394 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 21:30:34.397 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 21:30:36.759 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [After]
2025-07-19 21:30:36.760 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Before]
2025-07-19 21:30:36.760 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Between]
2025-07-19 21:30:36.760 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Cookie]
2025-07-19 21:30:36.761 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Header]
2025-07-19 21:30:36.762 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Host]
2025-07-19 21:30:36.762 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Method]
2025-07-19 21:30:36.762 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Path]
2025-07-19 21:30:36.763 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Query]
2025-07-19 21:30:36.763 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [ReadBodyPredicateFactory]
2025-07-19 21:30:36.763 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [RemoteAddr]
2025-07-19 21:30:36.763 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [Weight]
2025-07-19 21:30:36.764 [main] INFO  o.s.c.g.r.RouteDefinitionRouteLocator - Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-07-19 21:30:36.993 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 21:30:36.993 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 21:30:37.003 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 21:30:37.003 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 21:30:38.825 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 21:30:38.857 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 21:30:38.978 [main] INFO  o.s.b.a.s.r.ReactiveUserDetailsServiceAutoConfiguration - 

Using generated security password: 405fdb75-939f-43ef-98f6-73808a2aa9da

2025-07-19 21:30:40.382 [main] INFO  o.s.b.w.e.netty.NettyWebServer - Netty started on port(s): 9999
2025-07-19 21:30:41.713 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP common-gateway *************:9999 register finished
2025-07-19 21:30:41.729 [main] INFO  c.t.c.gateway.GatewayApplication - Started GatewayApplication in 12.661 seconds (JVM running for 13.278)
2025-07-19 21:31:31.602 [reactor-http-nio-2] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 377104ce-7f10-40eb-a8de-529c8cf4d0ed,  method: GET , path : /shuduoduo/ims/plate/home/<USER>"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImxzZ2NhZG1pbiIsInRlbmFudF9rZXkiOiIiLCJjb21wYW55X2lkIjoiODY2OTIyYWNhYzViNDFmNjhiZTM4NTRlZTBhN2VhOTgiLCJvcGVuSWQiOiIxNmFlYzY2ZTJlZjU0NjM0OTUwZDI2YjUzMjExMzU3MSIsInJvbGVzIjpbXSwicmVhbF9uYW1lIjoi6LaF566hIiwidG9rZW5fdHlwZSI6IiIsImFjY2Vzc190b2tlbiI6IiIsInJlZnJlc2hfdG9rZW4iOiIiLCJpZl9iYWNrZW5kIjp0cnVlLCJyZWZyZXNoX2V4cGlyZXNfaW4iOjAsIm5pY2tfbmFtZSI6ImxzZ2NhZG1pbiIsIklkIjoiMTZhZWM2NmUyZWY1NDYzNDk1MGQyNmI1MzIxMTM1NzEiLCJleHAiOjE3NTU1MjM1NzIsImV4cGlyZXNfaW4iOjQzMjAwfQ.qOMTWTKb47kCfyRtqQWKyG7X22x5kBMt_XSHgLs6gw4", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:31:31.603 [reactor-http-nio-3] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 53c34d23-31e3-4eca-9642-ab80d571547d,  method: POST , path : /shuduoduo/sms/log, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"177", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImxzZ2NhZG1pbiIsInRlbmFudF9rZXkiOiIiLCJjb21wYW55X2lkIjoiODY2OTIyYWNhYzViNDFmNjhiZTM4NTRlZTBhN2VhOTgiLCJvcGVuSWQiOiIxNmFlYzY2ZTJlZjU0NjM0OTUwZDI2YjUzMjExMzU3MSIsInJvbGVzIjpbXSwicmVhbF9uYW1lIjoi6LaF566hIiwidG9rZW5fdHlwZSI6IiIsImFjY2Vzc190b2tlbiI6IiIsInJlZnJlc2hfdG9rZW4iOiIiLCJpZl9iYWNrZW5kIjp0cnVlLCJyZWZyZXNoX2V4cGlyZXNfaW4iOjAsIm5pY2tfbmFtZSI6ImxzZ2NhZG1pbiIsIklkIjoiMTZhZWM2NmUyZWY1NDYzNDk1MGQyNmI1MzIxMTM1NzEiLCJleHAiOjE3NTU1MjM1NzIsImV4cGlyZXNfaW4iOjQzMjAwfQ.qOMTWTKb47kCfyRtqQWKyG7X22x5kBMt_XSHgLs6gw4", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:31:31.603 [reactor-http-nio-4] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: c1e303ab-2c06-47ae-a074-e9121ae555e0,  method: POST , path : /shuduoduo/ims/query/topic/page, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"52", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImxzZ2NhZG1pbiIsInRlbmFudF9rZXkiOiIiLCJjb21wYW55X2lkIjoiODY2OTIyYWNhYzViNDFmNjhiZTM4NTRlZTBhN2VhOTgiLCJvcGVuSWQiOiIxNmFlYzY2ZTJlZjU0NjM0OTUwZDI2YjUzMjExMzU3MSIsInJvbGVzIjpbXSwicmVhbF9uYW1lIjoi6LaF566hIiwidG9rZW5fdHlwZSI6IiIsImFjY2Vzc190b2tlbiI6IiIsInJlZnJlc2hfdG9rZW4iOiIiLCJpZl9iYWNrZW5kIjp0cnVlLCJyZWZyZXNoX2V4cGlyZXNfaW4iOjAsIm5pY2tfbmFtZSI6ImxzZ2NhZG1pbiIsIklkIjoiMTZhZWM2NmUyZWY1NDYzNDk1MGQyNmI1MzIxMTM1NzEiLCJleHAiOjE3NTU1MjM1NzIsImV4cGlyZXNfaW4iOjQzMjAwfQ.qOMTWTKb47kCfyRtqQWKyG7X22x5kBMt_XSHgLs6gw4", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:31:31.602 [reactor-http-nio-5] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 04162c9c-0738-4fa2-8289-c81a02d8c4c4,  method: GET , path : /shuduoduo/ims/query/topic/total, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImxzZ2NhZG1pbiIsInRlbmFudF9rZXkiOiIiLCJjb21wYW55X2lkIjoiODY2OTIyYWNhYzViNDFmNjhiZTM4NTRlZTBhN2VhOTgiLCJvcGVuSWQiOiIxNmFlYzY2ZTJlZjU0NjM0OTUwZDI2YjUzMjExMzU3MSIsInJvbGVzIjpbXSwicmVhbF9uYW1lIjoi6LaF566hIiwidG9rZW5fdHlwZSI6IiIsImFjY2Vzc190b2tlbiI6IiIsInJlZnJlc2hfdG9rZW4iOiIiLCJpZl9iYWNrZW5kIjp0cnVlLCJyZWZyZXNoX2V4cGlyZXNfaW4iOjAsIm5pY2tfbmFtZSI6ImxzZ2NhZG1pbiIsIklkIjoiMTZhZWM2NmUyZWY1NDYzNDk1MGQyNmI1MzIxMTM1NzEiLCJleHAiOjE3NTU1MjM1NzIsImV4cGlyZXNfaW4iOjQzMjAwfQ.qOMTWTKb47kCfyRtqQWKyG7X22x5kBMt_XSHgLs6gw4", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:31:31.985 [reactor-http-nio-3] INFO  c.n.config.ChainedDynamicProperty - Flipping property: common-auth.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:31:32.027 [reactor-http-nio-3] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: common-auth instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=common-auth,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-19 21:31:32.035 [reactor-http-nio-3] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-07-19 21:31:32.212 [reactor-http-nio-3] INFO  c.n.config.ChainedDynamicProperty - Flipping property: common-auth.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:31:32.215 [reactor-http-nio-3] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client common-auth initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=common-auth,current list of Servers=[*************:9977],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:*************:9977;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@11daa4c5
2025-07-19 21:31:32.641 [reactor-http-nio-2] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - Gateway 获取允许访问的path配置成功，获取结果：[/shuduoduo/*/task/**, /shuduoduo/nocodb/openapi/**, /shuduoduo/ums/swagger-ui/**, /shuduoduo/ims/auth/nocodb/info/**, /shuduoduo/ims/test/**, /shuduoduo/ums/openapi/**, /shuduoduo/sms/openapi/**, /shuduoduo/sms/test/**, /shuduoduo/ums/login/**, /shuduoduo/ims/openapi/**, /shuduoduo/ims/query/data/export/**, /shuduoduo/ims/swagger-ui/**, /shuduoduo/sms/swagger-ui/**, /shuduoduo/ums/company/**, /shuduoduo/ums/api/**, /shuduoduo/ums/api/wx/**, /shuduoduo/ums/test/**, /shuduoduo/ims/nocodb/table/**]
2025-07-19 21:31:32.641 [reactor-http-nio-3] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - Gateway 获取允许访问的path配置成功，获取结果：[/shuduoduo/*/task/**, /shuduoduo/nocodb/openapi/**, /shuduoduo/ums/swagger-ui/**, /shuduoduo/ims/auth/nocodb/info/**, /shuduoduo/ims/test/**, /shuduoduo/ums/openapi/**, /shuduoduo/sms/openapi/**, /shuduoduo/sms/test/**, /shuduoduo/ums/login/**, /shuduoduo/ims/openapi/**, /shuduoduo/ims/query/data/export/**, /shuduoduo/ims/swagger-ui/**, /shuduoduo/sms/swagger-ui/**, /shuduoduo/ums/company/**, /shuduoduo/ums/api/**, /shuduoduo/ums/api/wx/**, /shuduoduo/ums/test/**, /shuduoduo/ims/nocodb/table/**]
2025-07-19 21:31:32.641 [reactor-http-nio-4] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - Gateway 获取允许访问的path配置成功，获取结果：[/shuduoduo/*/task/**, /shuduoduo/nocodb/openapi/**, /shuduoduo/ums/swagger-ui/**, /shuduoduo/ims/auth/nocodb/info/**, /shuduoduo/ims/test/**, /shuduoduo/ums/openapi/**, /shuduoduo/sms/openapi/**, /shuduoduo/sms/test/**, /shuduoduo/ums/login/**, /shuduoduo/ims/openapi/**, /shuduoduo/ims/query/data/export/**, /shuduoduo/ims/swagger-ui/**, /shuduoduo/sms/swagger-ui/**, /shuduoduo/ums/company/**, /shuduoduo/ums/api/**, /shuduoduo/ums/api/wx/**, /shuduoduo/ums/test/**, /shuduoduo/ims/nocodb/table/**]
2025-07-19 21:31:32.641 [reactor-http-nio-5] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - Gateway 获取允许访问的path配置成功，获取结果：[/shuduoduo/*/task/**, /shuduoduo/nocodb/openapi/**, /shuduoduo/ums/swagger-ui/**, /shuduoduo/ims/auth/nocodb/info/**, /shuduoduo/ims/test/**, /shuduoduo/ums/openapi/**, /shuduoduo/sms/openapi/**, /shuduoduo/sms/test/**, /shuduoduo/ums/login/**, /shuduoduo/ims/openapi/**, /shuduoduo/ims/query/data/export/**, /shuduoduo/ims/swagger-ui/**, /shuduoduo/sms/swagger-ui/**, /shuduoduo/ums/company/**, /shuduoduo/ums/api/**, /shuduoduo/ums/api/wx/**, /shuduoduo/ums/test/**, /shuduoduo/ims/nocodb/table/**]
2025-07-19 21:31:32.643 [reactor-http-nio-3] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:31:32.643 [reactor-http-nio-5] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:31:32.643 [reactor-http-nio-4] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:31:32.643 [reactor-http-nio-2] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:31:33.044 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: common-auth.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:32:13.894 [reactor-http-nio-4] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 4f263044-91aa-4f85-a264-9faec3ec83a7,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[演示]}
2025-07-19 21:32:13.894 [reactor-http-nio-4] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:32:13.936 [reactor-http-nio-4] INFO  c.n.config.ChainedDynamicProperty - Flipping property: shuduoduo-ums.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:32:13.939 [reactor-http-nio-4] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: shuduoduo-ums instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=shuduoduo-ums,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-19 21:32:13.943 [reactor-http-nio-4] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-07-19 21:32:13.945 [reactor-http-nio-4] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client shuduoduo-ums initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=shuduoduo-ums,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@f7bb616
2025-07-19 21:32:13.977 [reactor-http-nio-4] INFO  c.t.c.g.h.ErrorExceptionHandler - request :4f263044-91aa-4f85-a264-9faec3ec83a7, 请求处理出错，错误信息为：503 SERVICE_UNAVAILABLE "Unable to find instance for shuduoduo-ums"
2025-07-19 21:32:13.992 [reactor-http-nio-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [5869b265-10]  500 Server Error for HTTP POST "/shuduoduo/ums/company/list?keyWord=%E6%BC%94%E7%A4%BA"
org.springframework.cloud.gateway.support.NotFoundException: 503 SERVICE_UNAVAILABLE "Unable to find instance for shuduoduo-ums"
	at org.springframework.cloud.gateway.support.NotFoundException.create(NotFoundException.java:46)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	|_ checkpoint ⇢ org.springframework.web.cors.reactive.CorsWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ org.springframework.cloud.gateway.filter.WeightCalculatorWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ org.springframework.boot.actuate.metrics.web.reactive.server.MetricsWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ HTTP POST "/shuduoduo/ums/company/list?keyWord=%E6%BC%94%E7%A4%BA" [ExceptionHandlingWebHandler]
Stack trace:
		at org.springframework.cloud.gateway.support.NotFoundException.create(NotFoundException.java:46)
		at org.springframework.cloud.gateway.filter.LoadBalancerClientFilter.filter(LoadBalancerClientFilter.java:86)
		at org.springframework.cloud.gateway.handler.FilteringWebHandler$GatewayFilterAdapter.filter(FilteringWebHandler.java:138)
		at org.springframework.cloud.gateway.filter.OrderedGatewayFilter.filter(OrderedGatewayFilter.java:44)
		at org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.lambda$filter$0(FilteringWebHandler.java:118)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:44)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:150)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:67)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:76)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:274)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:851)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:114)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:67)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1782)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:144)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:114)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:76)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:274)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:851)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:73)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:173)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1782)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:140)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2344)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:103)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:441)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onNext(FluxConcatMap.java:243)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:91)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:38)
		at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:267)
		at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:225)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:120)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:228)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onSubscribe(FluxDematerialize.java:70)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:161)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:441)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:211)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:161)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:65)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:514)
		at reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:267)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:462)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:170)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:296)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:750)
2025-07-19 21:33:06.607 [reactor-http-nio-4] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 31a4c086-aa94-4139-9efc-d27d90d2b686,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[演示]}
2025-07-19 21:33:06.608 [reactor-http-nio-4] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:33:06.609 [reactor-http-nio-4] INFO  c.t.c.g.h.ErrorExceptionHandler - request :31a4c086-aa94-4139-9efc-d27d90d2b686, 请求处理出错，错误信息为：503 SERVICE_UNAVAILABLE "Unable to find instance for shuduoduo-ums"
2025-07-19 21:33:06.610 [reactor-http-nio-4] ERROR o.s.b.a.w.r.e.AbstractErrorWebExceptionHandler - [5869b265-12]  500 Server Error for HTTP POST "/shuduoduo/ums/company/list?keyWord=%E6%BC%94%E7%A4%BA"
org.springframework.cloud.gateway.support.NotFoundException: 503 SERVICE_UNAVAILABLE "Unable to find instance for shuduoduo-ums"
	at org.springframework.cloud.gateway.support.NotFoundException.create(NotFoundException.java:46)
	Suppressed: reactor.core.publisher.FluxOnAssembly$OnAssemblyException: 
Error has been observed at the following site(s):
	|_ checkpoint ⇢ org.springframework.web.cors.reactive.CorsWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ org.springframework.cloud.gateway.filter.WeightCalculatorWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ org.springframework.boot.actuate.metrics.web.reactive.server.MetricsWebFilter [DefaultWebFilterChain]
	|_ checkpoint ⇢ HTTP POST "/shuduoduo/ums/company/list?keyWord=%E6%BC%94%E7%A4%BA" [ExceptionHandlingWebHandler]
Stack trace:
		at org.springframework.cloud.gateway.support.NotFoundException.create(NotFoundException.java:46)
		at org.springframework.cloud.gateway.filter.LoadBalancerClientFilter.filter(LoadBalancerClientFilter.java:86)
		at org.springframework.cloud.gateway.handler.FilteringWebHandler$GatewayFilterAdapter.filter(FilteringWebHandler.java:138)
		at org.springframework.cloud.gateway.filter.OrderedGatewayFilter.filter(OrderedGatewayFilter.java:44)
		at org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.lambda$filter$0(FilteringWebHandler.java:118)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:44)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:150)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:67)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:76)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:274)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:851)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:114)
		at reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:67)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1782)
		at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:144)
		at reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:114)
		at reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:76)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.innerNext(FluxConcatMap.java:274)
		at reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:851)
		at reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:73)
		at reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:173)
		at reactor.core.publisher.Operators$MonoSubscriber.complete(Operators.java:1782)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:140)
		at reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2344)
		at reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:103)
		at reactor.core.publisher.MonoJust.subscribe(MonoJust.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:441)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onNext(FluxConcatMap.java:243)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:91)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:38)
		at reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:267)
		at reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:225)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:120)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:228)
		at reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onSubscribe(FluxDematerialize.java:70)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:161)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalFluxOperator.subscribe(InternalFluxOperator.java:62)
		at reactor.core.publisher.FluxDefer.subscribe(FluxDefer.java:54)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.drain(FluxConcatMap.java:441)
		at reactor.core.publisher.FluxConcatMap$ConcatMapImmediate.onSubscribe(FluxConcatMap.java:211)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:161)
		at reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:86)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:52)
		at reactor.core.publisher.Mono.subscribe(Mono.java:4213)
		at reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.drain(MonoIgnoreThen.java:172)
		at reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:56)
		at reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:64)
		at reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:65)
		at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:514)
		at reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:267)
		at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:462)
		at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:170)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)
		at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:324)
		at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:296)
		at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:357)
		at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
		at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:365)
		at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)
		at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:163)
		at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
		at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
		at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
		at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
		at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
		at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
		at java.lang.Thread.run(Thread.java:750)
2025-07-19 21:33:14.972 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: shuduoduo-ums.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:33:17.386 [reactor-http-nio-4] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: bbdfd369-717e-4f25-b231-cbb8a6a0e486,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[演示]}
2025-07-19 21:33:17.387 [reactor-http-nio-4] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:33:28.704 [reactor-http-nio-4] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 0c9e8870-0923-4111-b85e-8642350ca4eb,  method: POST , path : /shuduoduo/ums/login/adminlogin, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"104", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:33:28.704 [reactor-http-nio-4] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:35:02.862 [reactor-http-nio-4] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: de304582-6799-4851-ab89-a09ac720fc58,  method: POST , path : /shuduoduo/ums/login/adminlogin, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"104", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:35:02.863 [reactor-http-nio-4] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:35:04.926 [reactor-http-nio-4] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 5d054b38-4d97-463e-b892-9e80e7c144cb,  method: GET , path : /shuduoduo/ims/nocodb/table/info/7172070a1c264c2bb6bb201e17bf8ee7, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:35:04.926 [reactor-http-nio-4] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:35:04.946 [reactor-http-nio-4] INFO  c.n.config.ChainedDynamicProperty - Flipping property: shuduoduo-ims.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:35:04.947 [reactor-http-nio-4] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: shuduoduo-ims instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=shuduoduo-ims,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-19 21:35:04.948 [reactor-http-nio-4] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-07-19 21:35:04.948 [reactor-http-nio-4] INFO  c.n.config.ChainedDynamicProperty - Flipping property: shuduoduo-ims.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:35:04.950 [reactor-http-nio-4] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client shuduoduo-ims initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=shuduoduo-ims,current list of Servers=[*************:8880],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:*************:8880;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@357d38dc
2025-07-19 21:35:05.962 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: shuduoduo-ims.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:36:39.923 [reactor-http-nio-7] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 5256c082-b87e-4904-9a54-6d27b58b6ebe,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[演示]}
2025-07-19 21:36:39.924 [reactor-http-nio-7] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:36:53.491 [reactor-http-nio-7] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 2479689c-28b8-43ec-aa5e-ae5ae67d4422,  method: POST , path : /shuduoduo/ums/login/adminlogin, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"104", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:36:53.492 [reactor-http-nio-7] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:36:55.532 [reactor-http-nio-7] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: f844856c-9a8b-429d-89f8-00873b36b484,  method: GET , path : /shuduoduo/ims/nocodb/table/info/7172070a1c264c2bb6bb201e17bf8ee7, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:36:55.532 [reactor-http-nio-7] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:45:49.172 [reactor-http-nio-8] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: fd132cf0-eac2-4c68-9671-01ce0341a7bc,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[演示]}
2025-07-19 21:45:49.172 [reactor-http-nio-8] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:46:01.321 [reactor-http-nio-8] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 3986bc49-faa4-4e3b-b365-fc0757e8c52c,  method: POST , path : /shuduoduo/ums/login/adminlogin, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"104", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:01.321 [reactor-http-nio-8] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:46:03.393 [reactor-http-nio-8] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 66c3d064-cf7e-4619-9355-844ee398e3a6,  method: GET , path : /shuduoduo/ims/nocodb/table/info/7172070a1c264c2bb6bb201e17bf8ee7, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:03.393 [reactor-http-nio-8] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:46:05.428 [reactor-http-nio-8] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: a67033f1-9a40-467d-ac6f-b810c60c0d68,  method: GET , path : /shuduoduo/ims/auth/nocodb/info/7172070a1c264c2bb6bb201e17bf8ee7, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:05.428 [reactor-http-nio-8] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:46:29.334 [reactor-http-nio-12] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: c38db3d7-6d4f-40b0-b95a-ce20a8e19526,  method: GET , path : /shuduoduo/ims/plate/queryPlate, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImFkbWluIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLotoXnrqEiLCJ0b2tlbl90eXBlIjoiIiwiYWNjZXNzX3Rva2VuIjoiIiwicmVmcmVzaF90b2tlbiI6IiIsImlmX2JhY2tlbmQiOnRydWUsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoiYWRtaW4iLCJJZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwiZXhwIjoxNzU1NTI0MTAyLCJleHBpcmVzX2luIjo0MzIwMH0.kYQKrukiDGF3peJlrFmi4cqZjvpEnLGM8y3R_iW7yHI", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[]}
2025-07-19 21:46:29.334 [reactor-http-nio-12] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:46:29.341 [reactor-http-nio-9] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 3accc600-f99c-493b-8596-22dc413a3b92,  method: POST , path : /shuduoduo/ims/raw/page, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"119", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImFkbWluIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLotoXnrqEiLCJ0b2tlbl90eXBlIjoiIiwiYWNjZXNzX3Rva2VuIjoiIiwicmVmcmVzaF90b2tlbiI6IiIsImlmX2JhY2tlbmQiOnRydWUsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoiYWRtaW4iLCJJZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwiZXhwIjoxNzU1NTI0MTAyLCJleHBpcmVzX2luIjo0MzIwMH0.kYQKrukiDGF3peJlrFmi4cqZjvpEnLGM8y3R_iW7yHI", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:29.341 [reactor-http-nio-9] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:46:29.342 [reactor-http-nio-11] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 4743b198-0369-44ed-b127-0c49425cc512,  method: GET , path : /shuduoduo/ims/plate/source/list, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImFkbWluIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLotoXnrqEiLCJ0b2tlbl90eXBlIjoiIiwiYWNjZXNzX3Rva2VuIjoiIiwicmVmcmVzaF90b2tlbiI6IiIsImlmX2JhY2tlbmQiOnRydWUsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoiYWRtaW4iLCJJZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwiZXhwIjoxNzU1NTI0MTAyLCJleHBpcmVzX2luIjo0MzIwMH0.kYQKrukiDGF3peJlrFmi4cqZjvpEnLGM8y3R_iW7yHI", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {plateId=[]}
2025-07-19 21:46:29.342 [reactor-http-nio-13] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 3761181f-9d46-4b26-921d-85ffe4a81404,  method: POST , path : /shuduoduo/sms/log, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"263", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImFkbWluIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLotoXnrqEiLCJ0b2tlbl90eXBlIjoiIiwiYWNjZXNzX3Rva2VuIjoiIiwicmVmcmVzaF90b2tlbiI6IiIsImlmX2JhY2tlbmQiOnRydWUsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoiYWRtaW4iLCJJZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwiZXhwIjoxNzU1NTI0MTAyLCJleHBpcmVzX2luIjo0MzIwMH0.kYQKrukiDGF3peJlrFmi4cqZjvpEnLGM8y3R_iW7yHI", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:29.342 [reactor-http-nio-10] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 43d13544-34e3-41a1-b33d-827df1adcd59,  method: GET , path : /shuduoduo/ims/plate/control/dept/list, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImFkbWluIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLotoXnrqEiLCJ0b2tlbl90eXBlIjoiIiwiYWNjZXNzX3Rva2VuIjoiIiwicmVmcmVzaF90b2tlbiI6IiIsImlmX2JhY2tlbmQiOnRydWUsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoiYWRtaW4iLCJJZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwiZXhwIjoxNzU1NTI0MTAyLCJleHBpcmVzX2luIjo0MzIwMH0.kYQKrukiDGF3peJlrFmi4cqZjvpEnLGM8y3R_iW7yHI", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {plateId=[]}
2025-07-19 21:46:29.342 [reactor-http-nio-8] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 5cd8fac9-140d-4842-89dd-925c1a78a161,  method: GET , path : /shuduoduo/ims/data/field/queryDataField, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImFkbWluIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLotoXnrqEiLCJ0b2tlbl90eXBlIjoiIiwiYWNjZXNzX3Rva2VuIjoiIiwicmVmcmVzaF90b2tlbiI6IiIsImlmX2JhY2tlbmQiOnRydWUsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoiYWRtaW4iLCJJZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwiZXhwIjoxNzU1NTI0MTAyLCJleHBpcmVzX2luIjo0MzIwMH0.kYQKrukiDGF3peJlrFmi4cqZjvpEnLGM8y3R_iW7yHI", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {plateId=[]}
2025-07-19 21:46:29.342 [reactor-http-nio-13] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:46:29.342 [reactor-http-nio-10] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:46:29.342 [reactor-http-nio-8] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:46:29.342 [reactor-http-nio-11] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:46:39.380 [reactor-http-nio-10] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 1b561327-c958-4604-ab3c-13879f62040f,  method: POST , path : /shuduoduo/ums/company/list, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"0", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/x-www-form-urlencoded", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {keyWord=[演示]}
2025-07-19 21:46:39.381 [reactor-http-nio-10] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:46:51.781 [reactor-http-nio-10] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 02551f6a-12d6-43e9-8e21-eda358888e21,  method: POST , path : /shuduoduo/ums/login/adminlogin, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"104", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:51.781 [reactor-http-nio-10] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:46:51.954 [reactor-http-nio-10] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 6d2cbb9a-b68c-4fc5-9b89-a8e327534e46,  method: GET , path : /shuduoduo/ims/nocodb/table/info/7172070a1c264c2bb6bb201e17bf8ee7, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:51.954 [reactor-http-nio-10] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:46:52.011 [reactor-http-nio-10] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 3b0ca6ff-677e-428d-8933-0b2e834c2093,  method: GET , path : /shuduoduo/ims/auth/nocodb/info/7172070a1c264c2bb6bb201e17bf8ee7, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:52.011 [reactor-http-nio-10] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 白名单免认证接口
2025-07-19 21:46:54.676 [reactor-http-nio-11] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: ad3afc1f-a9d8-462e-b65e-1b9eb24e8bff,  method: POST , path : /shuduoduo/sms/log, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"177", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImFkbWluIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLotoXnrqEiLCJ0b2tlbl90eXBlIjoiIiwiYWNjZXNzX3Rva2VuIjoiIiwicmVmcmVzaF90b2tlbiI6IiIsImlmX2JhY2tlbmQiOnRydWUsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoiYWRtaW4iLCJJZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwiZXhwIjoxNzU1NTI0MTAyLCJleHBpcmVzX2luIjo0MzIwMH0.kYQKrukiDGF3peJlrFmi4cqZjvpEnLGM8y3R_iW7yHI", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:54.676 [reactor-http-nio-11] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:46:54.676 [reactor-http-nio-10] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 9f00d929-5ff4-4e62-aaa4-85a46a87a349,  method: POST , path : /shuduoduo/ims/query/topic/page, header: [Host:"*************:9999", Connection:"keep-alive", Content-Length:"52", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImFkbWluIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLotoXnrqEiLCJ0b2tlbl90eXBlIjoiIiwiYWNjZXNzX3Rva2VuIjoiIiwicmVmcmVzaF90b2tlbiI6IiIsImlmX2JhY2tlbmQiOnRydWUsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoiYWRtaW4iLCJJZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwiZXhwIjoxNzU1NTI0MTAyLCJleHBpcmVzX2luIjo0MzIwMH0.kYQKrukiDGF3peJlrFmi4cqZjvpEnLGM8y3R_iW7yHI", Content-Type:"application/json", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:54.676 [reactor-http-nio-10] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:46:54.678 [reactor-http-nio-13] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: a68da3f0-9d59-469e-97bc-89f39bb509cb,  method: GET , path : /shuduoduo/ims/query/topic/total, header: [Host:"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImFkbWluIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLotoXnrqEiLCJ0b2tlbl90eXBlIjoiIiwiYWNjZXNzX3Rva2VuIjoiIiwicmVmcmVzaF90b2tlbiI6IiIsImlmX2JhY2tlbmQiOnRydWUsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoiYWRtaW4iLCJJZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwiZXhwIjoxNzU1NTI0MTAyLCJleHBpcmVzX2luIjo0MzIwMH0.kYQKrukiDGF3peJlrFmi4cqZjvpEnLGM8y3R_iW7yHI", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:54.678 [reactor-http-nio-8] DEBUG c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - http request: 6c228443-d7bb-4601-9513-91e9c2067a68,  method: GET , path : /shuduoduo/ims/plate/home/<USER>"*************:9999", Connection:"keep-alive", User-Agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", Accept:"application/json, text/plain, */*", Zaojiankeji-Token:"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ0aGlyZFVzZXJJZCI6ImFkbWluIiwidGVuYW50X2tleSI6IiIsImNvbXBhbnlfaWQiOiI3MTcyMDcwYTFjMjY0YzJiYjZiYjIwMWUxN2JmOGVlNyIsIm9wZW5JZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwicm9sZXMiOltdLCJyZWFsX25hbWUiOiLotoXnrqEiLCJ0b2tlbl90eXBlIjoiIiwiYWNjZXNzX3Rva2VuIjoiIiwicmVmcmVzaF90b2tlbiI6IiIsImlmX2JhY2tlbmQiOnRydWUsInJlZnJlc2hfZXhwaXJlc19pbiI6MCwibmlja19uYW1lIjoiYWRtaW4iLCJJZCI6IjE2YWVjNjZlMmVmNTQ2MzQ5NTBkMjZiNTMyMTEzNTcxIiwiZXhwIjoxNzU1NTI0MTAyLCJleHBpcmVzX2luIjo0MzIwMH0.kYQKrukiDGF3peJlrFmi4cqZjvpEnLGM8y3R_iW7yHI", Origin:"http://localhost:3000", Referer:"http://localhost:3000/", Accept-Encoding:"gzip, deflate", Accept-Language:"zh-CN,zh;q=0.9"],query params: {}
2025-07-19 21:46:54.678 [reactor-http-nio-13] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:46:54.678 [reactor-http-nio-8] INFO  c.t.c.g.f.GatewayTokenFilterConfig$$EnhancerBySpringCGLIB$$c0201098 - 没有token - 返回403，前端将自行处理重定向到CAS
2025-07-19 21:50:13.610 [Thread-34] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-07-19 21:50:13.615 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'Nacso-Watch-Task-Scheduler'
2025-07-19 21:50:13.616 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 21:50:13.619 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
