package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 维度vo
 *
 * <AUTHOR>
 */
@Data
public class DimensionVo {

    /**
     * 新增/编辑参数
     */
    @Data
    public static class AddDimension {

        private String id;

        @NotBlank
        private String plateId;

        private String dataFieldId;

        private String busProcessId;

        @NotNull
        private Integer dataType;

        @NotBlank
        private String name;
        @NotBlank
        private String code;

        private String plateLayerId;

        private String description;

        /**
         * 表文档
         */
        private String doc;

        /**
         * 新增的表字段集合
         */
        private List<DimAttr> attrList;

        /**
         * 编辑的字段集合
         */
        private List<DimAttr> editAttrList;

        /**
         * 删除的字段ID集合
         */
        private List<String> delAttrList;


        /**
         * 排序字段
         */
        private Long orderBy;

        /**
         * 机构id
         */
        private String orgId;

        /**
         * 条线id
         */
        private String lineId;

        /**
         * 业务品质id
         */
        private String bvId;

        /**
         * 来源系统id
         */
        private String sourceId;

        @Data
        public static class DimAttr {

            private String id;

            private String dwsId;

            private String name;
            private String code;
            private String aliasName;

            /**
             * 类型 1、新建 2、开通
             */
            private Integer type;

            /**
             * 状态 1、未同步 2、已同步
             */
            private Integer status;

            /**
             * 字段类型 json
             */
            private String columnType;

            /**
             * 数据格式 json
             */
            private String dataFormat;

            /**
             * 字典值--周期值
             */
            private Integer dictValue;

            /**
             * 字典类型--周期
             */
            private String dictType;

            /**
             * 是否主键
             */
            private Boolean ifPrimaryKey;

            /**
             * 事实表字段选择的维度外键ID
             */
            private String unionDimId;

            /**
             * 事实表字段选择的维度字段ID
             */
            private String unionDimAttrId;

            /**
             * 关联维度表中文名
             */
            private String dimName;

            /**
             * 维度表英文名
             */
            private String dimEnName;

            /**
             * 维度表主键中文名
             */
            private String dimAttrName;

            /**
             * 维度表主键英文名
             */
            private String dimAttrEnName;

            /**
             * 排序字段
             */
            private Long orderBy;

            /**
             * 统计周期
             */
            private String periodValue;

            /**
             * 挂接类型
             */
            private String unionType;

            /**
             * 可用分析
             */
            private String analysis;

            /**
             * 关联的维度信息
             */
            private String dimension;

        }
    }

    /**
     * 分页请求参数
     */
    @Data
    public static class PageVo extends CommonMySqlPageVo {

        private Integer dataType;

        private String plateId;

        private String dataFieldId;

        private String busProcessId;

        private String id;

    }

    /**
     * 分页请求参数
     */
    @Data
    public static class DataModelPageVo extends CommonMySqlPageVo {

        private Integer dataType;

        /**
         * 机构id
         */
        private String orgId;

        /**
         * 条线id
         */
        private String lineId;

        /**
         * 业务品质id
         */
        private String bvId;

        /**
         * 来源系统id
         */
        private String sourceId;

        private String id;

    }

    /**
     * 分页返回参数
     */
    @Data
    public static class PageResponse {
        private String id;

        /**
         * 板块ID
         */
        private String plateId;

        /**
         * 数据域ID
         */
        private String dataFieldId;

        /**
         * 业务过程ID
         */
        private String busProcessId;

        /**
         * 类型：1、维度 2、事实表
         */
        private Integer dataType;

        /**
         * 名称
         */
        private String name;

        /**
         * 编码
         */
        private String code;

        /**
         * 描述
         */
        private String description;

        /**
         * 类型 1、新建 2、开通
         */
        private Integer type;

        /**
         * 状态 1、未同步 2、已同步
         */
        private Integer status;

        /**
         * 排序字段
         */
        private Long orderBy;

        /**
         * 目录
         */
        private String target;

        /**
         * 检查状态
         */
        private Integer checkStatus;

        /**
         * 检查描述
         */
        private String checkMessage;

        /**
         * 检查时间
         */
        private Date checkTime;
    }

    /**
     * 搜索参数
     */
    @Data
    public static class SearchParam {

        /**
         * 类型：1、维度 2、事实表
         */
        private Integer dataType;

        private String plateId;

        private String dataFieldId;

        private String busProcessId;

        /**
         * 关键字搜索
         */
        @NotBlank
        public String keyWord;
    }

    /**
     * 搜索返回参数
     */
    @Data
    public static class SearchResponse {
        private String id;

        private String name;

        private String code;

        private String attrName;

        private String attrCode;

    }

    @Data
    public static class DetailResponse {
        private String id;

        @NotBlank
        private String plateId;

        private String dataFieldId;

        private String busProcessId;

        @NotNull
        private Integer dataType;

        @NotBlank
        private String name;
        @NotBlank
        private String code;

        private String plateLayerId;

        private String description;

        /**
         * 表文档
         */
        private String doc;

        /**
         * 类型 1、新建 2、开通
         */
        private Integer type;

        /**
         * 状态 1、未同步 2、已同步
         */
        private Integer status;

        /**
         * 机构id
         */
        private String orgId;

        /**
         * 条线id
         */
        private String lineId;

        /**
         * 业务品质id
         */
        private String bvId;

        /**
         * 来源系统id
         */
        private String sourceId;

        /**
         * 表字段相关
         */
        private List<AddDimension.DimAttr> attrList;

    }

    @Data
    public static class CheckField {

        private String plateId;

        private String dataFieldId;

        private String busProcessId;

        @NotNull
        @Min(1)
        @Max(2)
        private Integer dataType;
    }

    @Data
    public static class DimensionPageResult<T> extends PageResult<T> {
        private Integer checkingTotal;
    }

    @Data
    public static class SqlParam {

        @NotBlank
        private String sql;
    }

    @Data
    public static class DocParam {

        @NotBlank
        private String doc;
    }
}
