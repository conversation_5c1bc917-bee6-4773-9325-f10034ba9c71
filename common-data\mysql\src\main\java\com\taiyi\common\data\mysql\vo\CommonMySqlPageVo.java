package com.taiyi.common.data.mysql.vo;

import java.io.Serializable;

/**
 * 分页/排序/查询
 *
 * <AUTHOR>
 */
public class CommonMySqlPageVo implements Serializable {

    public int pageNo = 1;

    public int pageSize = 10;

    public int getPageNo() {
        return pageNo;
    }

    public void setPageNo(int pageNo) {
        this.pageNo = pageNo;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getColumn() {
        return column;
    }

    public void setColumn(String column) {
        this.column = column;
    }

    public String getSortType() {
        return sortType;
    }

    public void setSortType(String sortType) {
        this.sortType = sortType;
    }

    /**
     * 关键字搜索
     */
    public String keyWord;

    /**
     * 排序字段
     */
    public String column;

    /**
     * 排序类型 asc/desc
     */
    public String sortType;


}
