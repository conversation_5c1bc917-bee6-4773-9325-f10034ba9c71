###########common dev config##########
# nacos config
spring.cloud.nacos.discovery.server-addr=127.0.0.1:44430
spring.cloud.nacos.config.server-addr=127.0.0.1:44430
spring.cloud.nacos.username=nacos
spring.cloud.nacos.password=nacos
#spring.cloud.sentinel.transport.dashboard=47.102.128.219:44408
#spring.cloud.sentinel.transport.port=44409
# datasource config
spring.datasource.dynamic.primary=mysql
spring.datasource.dynamic.strict=false
spring.datasource.dynamic.hikari.max-pool-size=5
spring.datasource.dynamic.hikari.min-idle=5
spring.datasource.dynamic.hikari.connection-test-query=SELECT 1
spring.datasource.dynamic.hikari.max-lifetime=60000
spring.datasource.dynamic.hikari.idle-timeout=60000
spring.datasource.dynamic.datasource.mysql.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.mysql.username=root
spring.datasource.dynamic.datasource.mysql.password=root
spring.datasource.dynamic.datasource.mysql.url=*********************************************************************************************************************************
spring.datasource.dynamic.datasource.sqlite.driver-class-name=org.sqlite.JDBC
spring.datasource.dynamic.datasource.sqlite.username=
spring.datasource.dynamic.datasource.sqlite.password=
spring.datasource.dynamic.datasource.sqlite.url=jdbc:sqlite:C:\\Users\\<USER>\\Documents\\Virtual Machines\\服务器\\public_dir\\nocodb\\noco.db?date_string_format=yyyy-MM-dd HH:mm:ss
# redis
spring.redis.host=127.0.0.1
spring.redis.jedis.pool.max-active=2000
spring.redis.jedis.pool.max-idle=500
spring.redis.jedis.pool.max-wait=1000ms
spring.redis.jedis.pool.min-idle=50
spring.redis.password=root
spring.redis.port=6379
spring.redis.timeout=3000ms
# feign config
feign.hystrix.enabled=false
# elk config
logging.config=classpath:logback-spring.xml
