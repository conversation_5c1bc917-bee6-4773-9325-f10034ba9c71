package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.InsightExecuteLogDao;
import com.taiyi.shuduoduo.ims.entity.InsightExecuteLog;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class InsightExecuteLogService extends CommonMysqlService<InsightExecuteLogDao, InsightExecuteLog> {
    @Override
    public Class<InsightExecuteLog> getEntityClass() {
        return InsightExecuteLog.class;
    }


    public InsightExecuteLog getByExecutionId(String executionId) {
        QueryWrapper<InsightExecuteLog> wrapper = new QueryWrapper<>();
        wrapper.eq("execution_id", executionId).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public List<InsightExecuteLog> getLogListByInsightId(String insightId) {
        QueryWrapper<InsightExecuteLog> wrapper = new QueryWrapper<>();
        wrapper.eq("insight_id", insightId).orderByDesc("create_time");
        return super.list(wrapper);
    }
}