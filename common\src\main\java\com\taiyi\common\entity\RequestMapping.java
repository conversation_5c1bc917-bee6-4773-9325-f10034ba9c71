package com.taiyi.common.entity;

import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Set;

/**
 * <AUTHOR>
 */
public class RequestMapping {

    private String className;

    private String methodName;

    private Set<RequestMethod> requestMethods;

    private Set<String> requestPatterns;

    public RequestMapping(String className, String methodName, Set<RequestMethod> requestMethods, Set<String> requestPatterns) {
        this.className = className;
        this.methodName = methodName;
        this.requestMethods = requestMethods;
        this.requestPatterns = requestPatterns;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public Set<RequestMethod> getRequestMethods() {
        return requestMethods;
    }

    public void setRequestMethods(Set<RequestMethod> requestMethods) {
        this.requestMethods = requestMethods;
    }

    public Set<String> getRequestPatterns() {
        return requestPatterns;
    }

    public void setRequestPatterns(Set<String> requestPatterns) {
        this.requestPatterns = requestPatterns;
    }

    @Override
    public String toString() {
        return "RequestMapping{" +
                "className='" + className + '\'' +
                ", methodName='" + methodName + '\'' +
                ", requestMethods=" + requestMethods +
                ", requestPatterns=" + requestPatterns +
                '}';
    }
}
