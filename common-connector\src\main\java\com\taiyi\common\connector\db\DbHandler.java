package com.taiyi.common.connector.db;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.connector.entity.DbEntity;
import com.taiyi.common.entity.DbType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
public class DbHandler<E extends DbEntity> {

    private final Logger logger = LoggerFactory.getLogger(DbHandler.class);

    private Connection conn = null;

    /**
     * 构建数据库连接
     *
     * @param entity entity
     * @return Connection
     */
    public Connection buildConnection(E entity) {
        String url;
        try {
            if (entity.getType().equalsIgnoreCase(DbType.MYSQL.toString())) {
                //加载MYSQL驱动
                Class.forName("com.mysql.cj.jdbc.Driver");
                url = "jdbc:mysql://" + entity.getHost() + ":" + entity.getPort() + "/" + entity.getDatabase() + "?serverTimezone=Asia/Shanghai";
                this.conn = DriverManager.getConnection(url, entity.getUsername(), entity.getPassword());
            } else if (entity.getType().equalsIgnoreCase(DbType.ORACLE.toString())) {
                //加载ORACLE驱动
                Class.forName("oracle.jdbc.driver.OracleDriver");
                url = "jdbc:oracle:thin:@//" + entity.getHost() + ":" + entity.getPort() + "/" + entity.getDatabase();
                this.conn = DriverManager.getConnection(url, entity.getUsername(), entity.getPassword());
            } else if (entity.getType().equalsIgnoreCase(DbType.POSTGRE.toString())) {
                //加载POSTGRE驱动
                Class.forName("org.postgresql.Driver");
                url = "jdbc:postgresql://" + entity.getHost() + ":" + entity.getPort() + "/" + entity.getDatabase();
                this.conn = DriverManager.getConnection(url, entity.getUsername(), entity.getPassword());
            } else if (entity.getType().equalsIgnoreCase(DbType.INCEPTOR.toString())) {
                Class.forName("org.apache.hive.jdbc.HiveDriver");
                url = "jdbc:hive2://" + entity.getHost() + ":" + entity.getPort() + "/" + entity.getDatabase();
                this.conn = DriverManager.getConnection(url, entity.getUsername(), entity.getPassword());
            } else if (entity.getType().equalsIgnoreCase(DbType.DREMIO.toString())) {
                Class.forName("com.dremio.jdbc.Driver");
                url = "jdbc:dremio:direct=" + entity.getHost() + ":" + entity.getPort() + ";schema=" + entity.getDatabase();
                this.conn = DriverManager.getConnection(url, entity.getUsername(), entity.getPassword());
            } else {
                this.conn = null;
            }
        } catch (ClassNotFoundException | SQLException e) {
            logger.info("获取数据库连接失败{},链接地址为{},数据库为{}", ExceptionUtil.stacktraceToString(e), entity.getHost(), entity.getDatabase());
        }
        logger.info("获取数据库连接,链接地址为{},数据库为{}", entity.getHost(), entity.getDatabase());
        return conn;
    }


    /**
     * 关闭数据库连接
     *
     * @param connection connection
     */
    public void closeConnection(Connection connection) {
        try {
            if (connection != null) {
                connection.close();
            }
        } catch (SQLException e) {
            logger.debug("数据库连接关闭失败,{}", ExceptionUtil.stacktraceToString(e));
        }
        logger.info("数据库连接已关闭");
    }

}
