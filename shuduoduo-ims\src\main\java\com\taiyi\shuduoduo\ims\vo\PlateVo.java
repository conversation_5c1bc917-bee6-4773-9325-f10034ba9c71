package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.entity.BaseList;
import com.taiyi.common.data.mysql.entity.MyQuery;
import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ims.entity.Plate;
import com.taiyi.shuduoduo.ims.entity.PlateLayer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 业务板块
 *
 * <AUTHOR>
 */
public class PlateVo {

    @Data
    public static class PlatePageVo extends CommonMySqlPageVo {

    }

    @Data
    public static class DetailResponse {

        private String id;

        private String description;

        /**
         * 板块编码
         */
        private String plateCode;

        private String name;

        private String companyId;

        /**
         * 类型 1、新建 2、开通
         */
        private Integer type;

        /**
         * 状态 1、未同步 2、已同步
         */
        private Integer status;

        private List<PlateLayer> plateLayerList;


    }

    @Data
    public static class EditPlate {
        private String description;

        /**
         * 板块编码
         */
        private String plateCode;

        private String name;

        private String companyId;

        /**
         * 类型 1、新建 2、开通
         */
        private Integer type;

        /**
         * 状态 1、未同步 2、已同步
         */
        private Integer status;

        /**
         * 板块分层
         */
        private List<PlateLayer> plateLayerList;

        /**
         * 删除的数仓分层ID集合
         */
        private List<String> delPlateLayers;
    }


    @Data
    public static class AddPlate {

        /**
         * 板块描述
         */
        private String description;

        /**
         * 板块编码
         */
        @NotBlank
        private String plateCode;

        @NotBlank
        private String name;

        /**
         * 板块分层
         */
        private List<PlateLayer> plateLayerList;
    }

    @Data
    public static class PlateList extends BaseList<Plate> {
        private AddPlate plate;

        public MyQuery getPage() {
            if (plate == null) {
                plate = new AddPlate();
            }
            return super.getPage("name", BeanUtil.copy(plate, Plate.class));
        }
    }

    @Data
    public static class QueryParam {

        private String keyWord;

        private String plateCode;

    }

    @Data
    public static class TotalResponse {
        private String name;

        private Integer total;
    }
}
