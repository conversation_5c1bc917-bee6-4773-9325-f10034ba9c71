package com.taiyi.shuduoduo.ims.util;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 校验图片是否可以打开
 *
 * <AUTHOR>
 */
public class CheckImageUrlUtil {

    /**
     * 判断网络图片是否存在
     * posturl 图片地址链接
     */
    public static boolean isImagesTrue(String postUrl) throws IOException {
        URL url = new URL(postUrl);
        HttpURLConnection urlcon = (HttpURLConnection) url.openConnection();
        urlcon.setRequestMethod("GET");
        urlcon.setRequestProperty("Content-type", "application/x-www-form-urlencoded");
        if (urlcon.getResponseCode() == HttpURLConnection.HTTP_OK) {
            System.out.println(HttpURLConnection.HTTP_OK + postUrl
                    + ":posted ok!");
            return true;
        } else {
            System.out.println(urlcon.getResponseCode() + postUrl
                    + ":Bad post...");
            return false;
        }
    }

    public static void main(String[] args) {
        String url = "https://csdnimg.cn/medal/<EMAIL>";
        try {
            System.out.println(isImagesTrue(url));
        } catch (Exception e) {
            System.out.println(false);
        }
    }
}
