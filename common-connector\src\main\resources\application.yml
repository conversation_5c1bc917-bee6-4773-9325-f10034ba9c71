server:
  port: 9988
  servlet:
    context-path: /common/connector
  compression:
    enabled: true
    mime-types: application/json,text/html,text/plain,application/javascript,text/css,text/javascript

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: common-connector
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  cloud:
    nacos:
      discovery:
        metadata:
          management:
            context-path: /common/connector/actuator

  profiles:
    active: local

feign:
  sentinel:
    enabled: true #打开sentinel对feign的支持

# 设置RPC的默认超时时间 30S
ribbon:
  ReadTimeout: 30000
  ConnectTimeout: 30000

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always

logging:
  config: classpath:logback-spring.xml