package com.taiyi.shuduoduo.ums.aspect;

import com.alibaba.fastjson.JSON;
import com.taiyi.common.aspect.ApiLogPointCut;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.IpUtil;
import com.taiyi.shuduoduo.sms.api.dto.LogDTO;
import com.taiyi.shuduoduo.sms.api.service.LogRpcService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 日志切面
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class ApiLogAspect {

    private Logger logger = LoggerFactory.getLogger(ApiLogAspect.class);

    @Autowired
    LogRpcService logRpcService;


    @Pointcut(
            "execution(public * com.taiyi.shuduoduo.ums.controller.*.*(..))"
                    + "&& !execution(public * com.taiyi.shuduoduo.ums.controller.LoginController.*(..))"
                    + "&& !execution(public * com.taiyi.shuduoduo.ums.controller.CompanyController.companyList(..))"
    )
    public void apiLogPointCutUms() {
    }

    @Pointcut(
            "execution(public * com.taiyi.shuduoduo.ums.controller.LoginController.*(..))" +
                    "execution(public * com.taiyi.shuduoduo.ums.controller.CompanyController.companyList(..))"
    )
    public void apiLogPointCutLogin() {
    }


    @Before("apiLogPointCutUms()")
    public void before(JoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取目标类上的目标注解
        Method method = signature.getMethod();
        ApiLogPointCut annotation = method.getAnnotation(ApiLogPointCut.class);
        if (annotation != null) {
            // 记录操作日志
            LogDTO logDTO = new LogDTO();
            logDTO.setCompanyId(CurrentUserUtil.get().getCompanyId());
            logDTO.setUsername(CurrentUserUtil.get().getRealName());
            logDTO.setPath(request.getContextPath() + request.getServletPath());
            logDTO.setIp(IpUtil.getRealIP(request));
            logDTO.setDoAction(annotation.name());
            logDTO.setActionDescription(annotation.description());
            logDTO.setParameters(JSON.toJSONString(joinPoint.getArgs()));
            logRpcService.save(logDTO);
        }
    }

    @After("apiLogPointCutLogin()")
    public void after(JoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取目标类上的目标注解
        Method method = signature.getMethod();
        ApiLogPointCut annotation = method.getAnnotation(ApiLogPointCut.class);
        if (annotation != null) {
            // 记录操作日志
            LogDTO logDTO = new LogDTO();
            logDTO.setCompanyId(CurrentUserUtil.get().getCompanyId());
            logDTO.setUsername(CurrentUserUtil.get().getRealName());
            logDTO.setPath(request.getContextPath() + request.getServletPath());
            logDTO.setIp(IpUtil.getRealIP(request));
            logDTO.setDoAction(annotation.name());
            logDTO.setActionDescription(annotation.description());
            logDTO.setParameters(JSON.toJSONString(joinPoint.getArgs()));
            logRpcService.save(logDTO);
        }
    }

}
