server:
  port: 9977
  servlet:
    context-path: /common/auth

spring:
  application:
    name: common-auth
  main:
    allow-bean-definition-overriding: true
  # 选择开发环境或者生产环境
  profiles:
    active: local

  cloud:
    nacos:
      discovery:
        metadata:
          management:
            context-path: /common/auth/actuator

feign:
  sentinel:
    enabled: true #打开sentinel对feign的支持

# 设置RPC的默认超时时间 30S
ribbon:
  ReadTimeout: 30000
  ConnectTimeout: 30000

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always

logging:
  config: classpath:logback-spring.xml