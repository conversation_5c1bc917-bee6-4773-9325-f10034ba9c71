package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.DwsDataAuthDao;
import com.taiyi.shuduoduo.ims.entity.DwsBasicAuth;
import com.taiyi.shuduoduo.ims.entity.DwsDataAuth;
import com.taiyi.shuduoduo.ims.vo.DwsAuthVo;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.api.service.UserRpcService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 指标数据认证服务
 *
 * <AUTHOR>
 */
@Service
public class DwsDataAuthService extends CommonMysqlService<DwsDataAuthDao, DwsDataAuth> {
    @Override
    public Class<DwsDataAuth> getEntityClass() {
        return DwsDataAuth.class;
    }

    @Autowired
    private DwsBasicAuthService dwsBasicAuthService;

    @Autowired
    private UserRpcService userRpcService;

    @Transactional(rollbackFor = Exception.class)
    public boolean saveDwsAuth(DwsAuthVo.AddRequestVo t) {
        boolean f = false;
        for (DwsAuthVo vo : t.getAuthList()) {
            vo.setAuthedBy(CurrentUserUtil.get().getId());
            if (vo.getType() == 1) {
                f = dwsBasicAuthService.save(BeanUtil.copy(vo, DwsBasicAuth.class));
            } else {
                f = super.save(BeanUtil.copy(vo, DwsDataAuth.class));
            }
        }
        return f;
    }

    public DwsDataAuth getByDwsId(String dwsId) {
        QueryWrapper<DwsDataAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId).orderByDesc("create_time").last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 根据指标ID获取最新权限数据
     *
     * @param type  权限类型
     * @param dwsId 指标ID
     * @return 权限数据
     */
    public DwsAuthVo.Detail getByDwsId(int type, String dwsId) {
        DwsAuthVo.Detail res = null;
        if (type == 1) {
            DwsBasicAuth basicAuth = dwsBasicAuthService.getByDwsId(dwsId);
            if (null != basicAuth) {
                res = BeanUtil.copy(basicAuth, DwsAuthVo.Detail.class);
            }
        } else {
            DwsDataAuth dataAuth = getByDwsId(dwsId);
            if (null != dataAuth) {
                res = BeanUtil.copy(dataAuth, DwsAuthVo.Detail.class);
            }
        }
        if (null != res && StringUtils.isNotBlank(res.getAuthedBy())) {
            UserDTO userDTO = userRpcService.getUserInfoById(res.getAuthedBy());
            res.setAuthName(null != userDTO ? userDTO.getRealName() : "");
        }
        return res;
    }

    public List<DwsDataAuth> getAuthListByDwsId(String dwsId) {
        QueryWrapper<DwsDataAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId).orderByDesc("create_time");
        return super.list(wrapper);
    }

    /**
     * 根据指标ID获取权限数据集合
     *
     * @param type  权限类型
     * @param dwsId 指标ID
     * @return 权限数据集合
     */
    public List<DwsAuthVo.ListResponse> getAuthListByDwsId(int type, String dwsId) {
        if (type == 1) {
            return BeanUtil.copyList(dwsBasicAuthService.getAuthListByDwsId(dwsId), DwsAuthVo.ListResponse.class);
        } else {
            return BeanUtil.copyList(getAuthListByDwsId(dwsId), DwsAuthVo.ListResponse.class);
        }
    }

    public List<DwsAuthVo.ListResponse> getDwsAuthListByDwsId(String dwsId) {
        List<DwsAuthVo.ListResponse> res = getAuthListByDwsId(1, dwsId);
        for (DwsAuthVo.ListResponse vo : res) {
            vo.setType(1);
        }
        List<DwsAuthVo.ListResponse> res1 = getAuthListByDwsId(2, dwsId);
        for (DwsAuthVo.ListResponse vo : res1) {
            vo.setType(2);
        }
        res.addAll(res1);
        for (DwsAuthVo.ListResponse vo : res) {
            if (StringUtils.isNotBlank(vo.getAuthedBy())) {
                UserDTO userDTO = userRpcService.getUserInfoById(vo.getAuthedBy());
                vo.setAuthName(null != userDTO ? userDTO.getRealName() : "");
            }
        }
        return res;
    }
}