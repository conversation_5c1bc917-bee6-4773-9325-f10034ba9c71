package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_insight_execute_log")
public class InsightExecuteLog extends CommonMySqlEntity {

    private String companyId;


    /**
     * 流程ID
     */
    private String flowId;

    /**
     * 执行ID
     */
    private String executionId;

    /**
     * 洞察ID
     */
    private String insightId;

    /**
     * 执行输出 json字符串
     */
    private String outputs;

    private String execUser;

}