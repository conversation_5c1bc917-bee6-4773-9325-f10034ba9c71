package com.taiyi.shuduoduo.ums.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.dao.UserDao;
import com.taiyi.shuduoduo.ums.entity.Company;
import com.taiyi.shuduoduo.ums.entity.User;
import com.taiyi.shuduoduo.ums.vo.LoginVo;
import com.taiyi.shuduoduo.ums.vo.UserPageVo;
import com.taiyi.shuduoduo.ums.vo.UserVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户业务层
 *
 * <AUTHOR>
 */
@Service
public class UserService extends CommonMysqlService<UserDao, User> {
    @Override
    public Class<User> getEntityClass() {
        return User.class;
    }

    @Autowired
    public UserCompanyService userCompanyService;

    @Autowired
    public UserRoleService userRoleService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private DepartmentService departmentService;

    public boolean lockUser(LoginVo.AdminVo adminVo) {
        QueryWrapper<User> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("nickname", adminVo.getName());
        User user = super.getOne(userQueryWrapper);
        if (null == user) {
            return false;
        }
        UpdateWrapper<User> wrapper = new UpdateWrapper<>();
        wrapper.set("if_lock", true)
                .eq("id", user.getId());
        return super.update(wrapper);
    }

    /**
     * 分页查询带参数if_backend
     *
     * @param userPageVo 带参数vo类
     * @return 分页列表
     */
    public PageResult<User> page(UserPageVo userPageVo) {
        Page<User> page = new Page<>(userPageVo.getPageNo(), userPageVo.getPageSize());
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false)
                .eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_backend", userPageVo.isIf_backend())
                .orderByAsc("nickname");
        IPage<User> iPage = super.page(page, wrapper);
        return getPageResult(page, iPage);
    }

    /**
     * 查询用户列表
     *
     * @param keyWord 关键字搜索
     * @return List
     */
    public List<Map<String, Object>> userList(String keyWord) {
        Company company = companyService.getById(CurrentUserUtil.get().getCompanyId());
        if (null == company) {
            return null;
        }
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", company.getId()).eq("emp_status", 1).eq("if_lock", false);
        if (StringUtils.isNotBlank(keyWord)) {
            wrapper.and(queryWrapper -> queryWrapper.like("real_name", keyWord)
                    .or()
                    .like("third_user_id", keyWord));
        }
        List<User> users = super.list(wrapper);
        List<Map<String, Object>> resList = new ArrayList<>();
        for (User user : users) {
            Map<String, Object> map = new HashMap<>(4);
            map.put("id", user.getId());
            map.put("name", user.getRealName());
            map.put("thirdUserId", user.getThirdUserId());
            map.put("ifAdmin", user.getIfAdmin());
            resList.add(map);
        }
        return resList;
    }

    /**
     * 根据公司ID和三方ID获取用户信息
     *
     * @param companyId   公司ID
     * @param thirdUserId 三方ID
     * @return 用户信息
     */
    public User getUserInfo(String companyId, String thirdUserId) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("if_lock", false).eq("company_id", companyId).eq("third_user_id", thirdUserId).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public User getByThirdUserId(String companyId, String thirdUserId) {
        if (StringUtils.isEmpty(thirdUserId)) {
            return null;
        }
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId).eq("third_user_id", thirdUserId);
        return this.getOne(wrapper);
    }

    public List<UserVo.ListResponse> getUserListByIds(List<String> ids) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .in("id", ids);
        List<User> list = super.list(wrapper);
        List<UserVo.ListResponse> res = BeanUtil.copyList(list, UserVo.ListResponse.class);
        for (UserVo.ListResponse response : res) {
            response.setName(response.getRealName());
        }
        return res;
    }

    public User findUserByCompanyIdAndNickName(String companyId, String name) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId).eq("nickname", name);
        return this.getOne(wrapper);
    }

    /**
     * 更新用户Dremio密码
     *
     * @param dremioInfo Dremio信息
     * @return bool
     */
    public boolean updateUserDremioInfoById(UserDTO.DremioDTO dremioInfo) {
        UpdateWrapper<User> wrapper = new UpdateWrapper<>();
        wrapper.eq("id", dremioInfo.getId()).set("dremio_pwd_digest", dremioInfo.getDremioPwdDigest());
        return super.update(wrapper);
    }
}