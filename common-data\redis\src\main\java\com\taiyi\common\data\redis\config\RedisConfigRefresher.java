package com.taiyi.common.data.redis.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.taiyi.common.listener.NacosConfigRefreshListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

import java.util.Map;

@Component
public class RedisConfigRefresher implements ApplicationListener<NacosConfigRefreshListener.NacosConfigUpdateEvent> {

    private static final Logger log = LoggerFactory.getLogger(RedisConfigRefresher.class);

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Override
    public void onApplicationEvent(NacosConfigRefreshListener.NacosConfigUpdateEvent event) {
        Map<String, Object> configMap = new Yaml().load(event.getContent());
        Map<String, Object> redisProps = MapUtil.get(configMap, "spring.redis", Map.class);
        if (CollUtil.isNotEmpty(redisProps)) {
            log.info("[Redis刷新] 检测到 redis 配置变更，重新加载连接工厂");
            // 重新构建 JedisConnectionFactory，替换掉旧的
            // 实际可考虑将 jedisConnectionFactory 设计为 RefreshableBean
        }
    }
}
