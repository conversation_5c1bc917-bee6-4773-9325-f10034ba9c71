package com.taiyi.common.auth.service;

import com.taiyi.common.data.redis.util.RedisRepository;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.JwtUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TokenService {

    @Autowired
    private RedisRepository redisRepository;

    public static final String TOKEN_KEY_PREFIX = "USER_TOKEN_";

    /**
     * 验证token
     *
     * @param token token
     * @return true/false
     */
    public boolean verify(String token) {
        if (StringUtils.isEmpty(token)) {
            return false;
        }
        boolean b = JwtUtil.verifyToken(token);
        if (!b) {
            return false;
        }
        // 解析token对象
        CurrentUserUtil.CurrentUser user = JwtUtil.decodeToken(token);
        // 校验redis中是否有该token
        Object userToken = redisRepository.get(TOKEN_KEY_PREFIX + user.getId());
        return null != userToken;
    }
}
