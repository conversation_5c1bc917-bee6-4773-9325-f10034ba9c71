package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.AuthNocodb;
import com.taiyi.shuduoduo.ims.service.AuthNocodbService;
import com.taiyi.shuduoduo.ims.service.PublicStoreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * nocodb权限控制
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth/nocodb")
@Validated
public class AuthNocodbController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private AuthNocodbService authNocodbService;

    /**
     * 获取权限nocodb信息
     *
     * @return T
     */
    @GetMapping("/info/{comId}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable String comId) {
        try {
            AuthNocodb t = authNocodbService.getAuthInfoByCompanyId(comId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.error("获取公司nocodb信息失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 获取权限表
     *
     * @return T
     */
    @GetMapping("/showTables")
    public ResponseEntity<ResponseVo.ResponseBean> getAuthTableList() {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, authNocodbService.getAuthTableList(CurrentUserUtil.get().getCompanyId()));
        } catch (Exception e) {
            logger.error("获取公司nocodb表失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取权限表字段
     *
     * @param table 权限表
     * @return T
     */
    @GetMapping("/showColumns/{table}")
    public ResponseEntity<ResponseVo.ResponseBean> getColumns(@PathVariable("table") String table) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, authNocodbService.getColumns(table));
        } catch (Exception e) {
            logger.error("获取公司nocodb表字段失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

}
