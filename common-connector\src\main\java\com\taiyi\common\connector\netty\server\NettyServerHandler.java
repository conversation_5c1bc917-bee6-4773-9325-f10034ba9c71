package com.taiyi.common.connector.netty.server;

import cn.hutool.json.JSONUtil;
import com.taiyi.common.connector.service.DbQueryService;
import com.taiyi.common.connector.netty.vo.DataEngineMsg;
import com.taiyi.common.connector.netty.vo.MsgType;
import com.taiyi.common.connector.util.SessionManager;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler.Sharable;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Sharable
@Component
public class NettyServerHandler extends ChannelInboundHandlerAdapter {

    private static final Logger logger = LoggerFactory.getLogger(NettyServerHandler.class);


    private SessionManager sessionManager;

    @Resource
    private DbQueryService dbQueryService;


    public NettyServerHandler() {
        this.sessionManager = SessionManager.getInstance();
    }

    private static NettyServerHandler nettyServerHandler;

    @PostConstruct
    public void init() {
        nettyServerHandler = this;
        nettyServerHandler.dbQueryService = this.dbQueryService;
    }

    /**
     * 客户端连接后触发
     *
     * @see io.netty.channel.ChannelInboundHandlerAdapter#channelActive(io.netty.channel.ChannelHandlerContext)
     */
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        // 加入连接池中
        String clientName = ctx.channel().remoteAddress().toString();
        sessionManager.setChannel(clientName, ctx.channel());
        super.channelActive(ctx);
        logger.warn("客户端已连接{}", ctx.channel().remoteAddress());
    }


    /**
     * channelInactive: 客户端断开连接后触发
     *
     * @see io.netty.channel.ChannelInboundHandlerAdapter#channelInactive(io.netty.channel.ChannelHandlerContext)
     */
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        // 移出连接池中
        String clientName = ctx.channel().remoteAddress().toString();
        sessionManager.removeChannel(clientName);
        super.channelInactive(ctx);
        logger.warn("客户端断开连接!{}", ctx.channel().remoteAddress());
    }


    /**
     * 收到客户端消息触发
     *
     * @param ctx ctx
     * @param msg msg
     * @throws Exception e
     */
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        DataEngineMsg message = (DataEngineMsg) msg;
        if (message.getType() == MsgType.HEART_BEAT) {
            logger.warn("收到客户端心跳消息{}", JSONUtil.toJsonStr(message));
        } else {
            logger.warn("收到客户端业务消息{}", DbQueryService.consoleLog(msg));
            nettyServerHandler.dbQueryService.ackSyncMsg(message);
        }
    }

    /**
     * 异常处理
     *
     * @param ctx   ctx
     * @param cause e
     */
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.writeAndFlush(cause);
    }


    /**
     * 服务端发送消息
     *
     * @return bool
     */
    public Channel getChannel(DataEngineMsg message) {
        if (sessionManager.getChannelMap().size() == 0) {
            return null;
        }
        for (String key : sessionManager.getChannelMap().keySet()) {
            Channel channel = sessionManager.getChannelMap().get(key);
            if (channel.isActive() && key.startsWith("/" + message.getRequest().getEngineHost())) {
                return channel;
            }
        }
        return null;
    }


}
