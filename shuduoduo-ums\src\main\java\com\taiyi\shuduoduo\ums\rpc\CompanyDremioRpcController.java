package com.taiyi.shuduoduo.ums.rpc;

import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.service.CompanyDremioService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/rpc/dremio")
public class CompanyDremioRpcController {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private CompanyDremioService companyDremioService;

    /**
     * 根据公司ID 获取dremio信息
     *
     * @param comId 公司ID
     * @return dremio信息
     */
    @GetMapping("/{comId}")
    public CompanyDremioDTO getCompanyById(@PathVariable("comId") String comId) {
        return BeanUtil.copy(companyDremioService.getDremioInfoByComId(comId), CompanyDremioDTO.class);
    }
}
