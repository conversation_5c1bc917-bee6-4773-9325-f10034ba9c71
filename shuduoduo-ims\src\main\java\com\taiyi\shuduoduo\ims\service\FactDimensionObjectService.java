package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.StringUtil;
import com.taiyi.shuduoduo.ims.dao.FactDimensionObjectDao;
import com.taiyi.shuduoduo.ims.entity.Dimension;
import com.taiyi.shuduoduo.ims.entity.FactDimensionObject;
import com.taiyi.shuduoduo.ims.vo.FactDimensionObjectVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FactDimensionObjectService extends CommonMysqlService<FactDimensionObjectDao, FactDimensionObject> {
    @Override
    public Class<FactDimensionObject> getEntityClass() {
        return FactDimensionObject.class;
    }

    @Autowired
    private DimensionService dimensionService;

    public FactDimensionObject getByDimIds(String dimIds) {
        QueryWrapper<FactDimensionObject> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("company_id", CurrentUserUtil.get().getCompanyId()).eq("dim_ids", dimIds)
                .last("LIMIT 1");
        return super.getOne(wrapper);

    }

    public List<FactDimensionObject> getListByDimIdAndType(String dimId, Integer dataType) {
        QueryWrapper<FactDimensionObject> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("company_id", CurrentUserUtil.get().getCompanyId());
        if (dataType == 1) {
            wrapper.like("dim_ids", dimId);
        } else {
            wrapper.like("fact_ids", dimId);
        }
        return super.list(wrapper);
    }

    public List<FactDimensionObjectVo.ListResponse> getObjectList() {
        List<FactDimensionObjectVo.ListResponse> res = new ArrayList<>();
        QueryWrapper<FactDimensionObject> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId()).eq("if_deleted", false).like("fact_ids", StringPool.COMMA);
        List<FactDimensionObject> list = super.list(wrapper);
        if (!list.isEmpty()) {
            for (FactDimensionObject object : list) {
                FactDimensionObjectVo.ListResponse response = new FactDimensionObjectVo.ListResponse();
                response.setId(object.getId());
                String[] dimIds = object.getDimIds().split(StringPool.COMMA);
                List<String> name = new ArrayList<>();
                for (String s : dimIds) {
                    Dimension dimension = dimensionService.getById(s);
                    name.add(dimension.getName());
                }
                response.setDimName(StringUtil.listToString(name, StringPool.PLUS) + "全景宽表");
            }
        }
        return res;
    }
}