package com.taiyi.shuduoduo.ims.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.RawAuth;
import com.taiyi.shuduoduo.ims.vo.RawAuthVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface RawAuthDao extends CommonMysqlMapper<RawAuth> {

    /**
     * 分页获取台账权限表
     *
     * @param page    分页
     * @param wrapper 筛选
     * @return page
     */
    @Select("select a.id,a.auth_table_id,a.status,a.operator,b.auth_table as authTableName from ims_raw_auth as a left join ims_auth_table as b " +
            "on a.auth_table_id = b.id ${ew.customSqlSegment}")
    IPage<RawAuthVo.PageResult> getPage(Page<RawAuthVo.PageResult> page, @Param("ew") QueryWrapper<RawAuthVo.PageResult> wrapper);
}