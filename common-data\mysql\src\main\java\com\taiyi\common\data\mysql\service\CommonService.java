package com.taiyi.common.data.mysql.service;

import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.entity.Query;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
public interface CommonService<T> {

    /**
     * 获取实体对应的class
     *
     * @return
     */
    public Class<T> getEntityClass();

    /**
     * 保存数据
     *
     * @param t
     * @return
     */
    public boolean save(@RequestBody T t);

    /**
     * 计算所有的数据量
     *
     * @return
     */
    public long countAll();


    /**
     * 计算所有逻辑删除数量
     *
     * @return
     */
    public long countAllLogicDeleted();


    /**
     * 计算某个条件的数据量
     *
     * @param t
     * @return
     */
    public long count(T t);


    /**
     * 根据ID获取数据
     *
     * @param id
     * @return
     */
    public T getById(String id);


    /**
     * 根据ID更新数据字段
     * <p>
     * 其中值为 NULL 字段 数据库中更新为NULL
     *
     * @param id
     * @param t
     * @return
     */
    public boolean updateWithNull(String id, T t);

    /**
     * 根据ID物理删除数据
     *
     * @param id
     * @return
     */
    public boolean deleteById(String id);


    /**
     * 根据ID逻辑删除数据
     * <p>
     * 调用方法前需要确定是否有if_delete逻辑删除字段
     *
     * @param id
     * @return
     */
    public boolean logicDeleteById(String id);


    /**
     * 根据ID 将逻辑删除数据恢复
     * <p>
     * 调用方法前需要确定是否有if_delete逻辑删除字段
     *
     * @param id
     * @return
     */
    public boolean unLogicDeleteById(String id);


    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    public PageResult page(Query query);

    /**
     * 根据id更新数据
     * <p>
     * 其中值为 NULL 字段 数据库不做更新
     * <p>
     * 默认不使用乐观锁
     *
     * @param id
     * @param t
     * @return
     */
    public boolean updateById(String id, T t);

    /**
     * 根据id以及版本号更新数据
     * 乐观锁更新
     *
     * @param id
     * @param t
     * @param version
     * @return
     */
    public boolean updateWithVersion(String id, T t, Long version);

    /**
     * 否存在 匹配t的数据
     *
     * @param t
     * @return
     */
    public boolean exists(T t);

    /**
     * 除去id记录外，是否存在匹配t的数据
     *
     * @param id
     * @param t
     * @return
     */
    public boolean exists(String id, T t);

}
