package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.exceptions.DremioException;
import com.taiyi.shuduoduo.ims.service.DremioSourceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Dremio 管理
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/dremio/source")
@Validated
public class DremioSourceController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DremioSourceService dremioSourceService;

    /**
     * 获取数据源列表
     *
     * @return 数据源列表
     */
    @GetMapping("/list")
    public ResponseEntity<ResponseVo.ResponseBean> getList() {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dremioSourceService.getList());
        } catch (Exception e) {
            logger.error("获取数据源列表失败,{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e.getMessage());
        }
    }

    /**
     * 单点登录
     *
     * @return 登录结果
     */
    @GetMapping("/oauth2")
    public ResponseEntity<ResponseVo.ResponseBean> oauth2() {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dremioSourceService.getDremioToken());
        } catch (DremioException e) {
            logger.error(e.getMessage());
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e.getMessage());
        } catch (Exception e) {
            logger.error("单点登录失败,{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e.getMessage());
        }
    }

}
