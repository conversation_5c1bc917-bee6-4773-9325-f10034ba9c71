package com.taiyi.shuduoduo.ims.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.Raw;
import com.taiyi.shuduoduo.ims.vo.RawVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface RawDao extends CommonMysqlMapper<Raw> {

    @Select("SELECT \n" +
            "    a.*,b.total\n" +
            "FROM\n" +
            "    ims_raw AS a\n" +
            "RIGHT JOIN\n" +
            "    (SELECT \n" +
            "        raw_id, COUNT(raw_id) AS total\n" +
            "    FROM\n" +
            "        ims_query_topic\n" +
            "\twhere if_deleted = false\n" +
            "    GROUP BY raw_id\n" +
            "    ORDER BY total DESC) AS b \n" +
            "ON a.id = b.raw_id ${ew.customSqlSegment}")
    IPage<Raw> selectPageByTotal(Page<Raw> page, @Param("ew") QueryWrapper<Raw> wrapper);

    @Select("SELECT  id,name,attrName FROM\n" +
            "(\n" +
            "SELECT \n" +
            " id,name,attrName,row_number() over(partition by id,name) as rn\n" +
            "FROM\n" +
            "\t(\n" +
            "\t\tSELECT id, name,null as attrName\n" +
            "\t\tFROM\n" +
            "\t\t\tims_raw\n" +
            "\t\t${ew.customSqlSegment}\n" +
            "\tUNION ALL  \n" +
            "\t\tSELECT distinct r.id,r.name as name,a.name as attrName\n" +
            "\t\tFROM\n" +
            "\t\t\tims_raw_table_attr AS a\n" +
            "\t\t\t\tLEFT JOIN\n" +
            "\t\t\tims_raw_table AS b ON a.raw_table_id = b.id\n" +
            "\t\t\t\tLEFT JOIN\n" +
            "\t\t\tims_raw AS r ON b.raw_id = r.id\n" +
            "\t\tWHERE a.if_deleted = FALSE AND a.company_id = '${companyId}' AND a.name LIKE '%${keyWord}%'\n" +
            "\t\t\t\tAND b.if_deleted = FALSE AND b.company_id = '${companyId}'\n" +
            "\t\t\t\tAND r.if_deleted = FALSE AND r.company_id = '${companyId}'\n" +
            ") a\n" +
            ") b\n" +
            "WHERE rn=1\n" +
            "ORDER BY  attrName ,id;\n")
    List<RawVo.SearchResponse> selectSearchRawList(@Param("ew") QueryWrapper<Raw> wrapper, @Param("keyWord") String keyWord, @Param("companyId") String companyId);
}