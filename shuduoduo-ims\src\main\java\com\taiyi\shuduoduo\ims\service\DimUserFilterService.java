package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.DimUserFilterDao;
import com.taiyi.shuduoduo.ims.entity.DimUserFilter;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class DimUserFilterService extends CommonMysqlService<DimUserFilterDao, DimUserFilter> {
    @Override
    public Class<DimUserFilter> getEntityClass() {
        return DimUserFilter.class;
    }

    public DimUserFilter getFilterByDimId(String companyId, String dimId, String topicId) {
        QueryWrapper<DimUserFilter> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false)
                .eq("company_id", companyId)
                .eq("dim_id", dimId)
                .eq("topic_id", topicId)
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }
}