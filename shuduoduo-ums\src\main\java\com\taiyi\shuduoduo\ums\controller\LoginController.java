package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSONObject;
import com.taiyi.common.aspect.ApiLogPointCut;
import com.taiyi.common.data.redis.util.RedisRepository;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.JwtUtil;
import com.taiyi.shuduoduo.ums.aspect.MethodResponseException;
import com.taiyi.shuduoduo.ums.entity.User;
import com.taiyi.shuduoduo.ums.service.CompanyService;
import com.taiyi.shuduoduo.ums.service.LoginService;
import com.taiyi.shuduoduo.ums.service.UserCompanyService;
import com.taiyi.shuduoduo.ums.service.UserService;
import com.taiyi.shuduoduo.ums.vo.LoginVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 登录控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/login")
@Validated
public class LoginController {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private LoginService loginService;
    @Autowired
    private RedisRepository redisRepository;
    @Autowired
    private UserCompanyService userCompanyService;
    @Autowired
    private CompanyService companyService;
    @Autowired
    private UserService userService;

    /**
     * 管理员登录
     *
     * @param adminVo 登录vo
     * @return ResponseEntity
     */
    @ApiLogPointCut(name = "用户登录", description = "管理员登录")
    @MethodResponseException
    @PostMapping("/adminlogin")
    public ResponseEntity<ResponseVo.ResponseBean> adminLogin(@RequestBody @Validated LoginVo.AdminVo adminVo) {
        // 根据公司id，用户名查找用户
        User user = userService.findUserByCompanyIdAndNickName(adminVo.getCompanyId(), adminVo.getName());
        if (user == null) {
            return ResponseVo.response(MessageCode.USER_LOGIN_ERROR, "用户名或密码错误");
        }
        if (user.getIfLock()) {
            return ResponseVo.response(MessageCode.USER_LOGIN_ERROR, "账号已被锁定");
        }
        try {
            JSONObject jsonObject = loginService.adminLogin(adminVo);
            if (jsonObject.containsKey("error")) {
                return ResponseVo.response(MessageCode.REQUEST_ERROR, jsonObject.getString("error"));
            } else {
                return ResponseVo.response(MessageCode.SUCCESS, jsonObject.getObject("user", LoginVo.AdminUserVo.class));
            }
        } catch (Exception e) {
            logger.error("管理员登录失败,{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


    /**
     * 退出登录
     *
     * @return bool
     */
    @PostMapping("/logout")
    public ResponseEntity<ResponseVo.ResponseBean> logOut() {
        boolean logout;
        try {
            CurrentUserUtil.CurrentUser currentUser = CurrentUserUtil.get();
            String id = currentUser.getId();
            logout = loginService.logOut(id);
            if (logout) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR, "退出失败");
            }
        } catch (Exception e) {
            logger.error("退出登录失败,{}",ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


    /**
     * 丽尚国潮登录
     *
     * @param ticket ticket
     * @return T
     */
    @ApiLogPointCut(name = "用户登录", description = "丽尚国潮登录")
    @GetMapping("/liShangLogin")
    public ResponseEntity<ResponseVo.ResponseBean> liShangLogin(@RequestParam("ticket") String ticket, @RequestParam("callbackUri") String callbackUri) {
        if (StringUtils.isBlank(ticket)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "用户token为空");
        }
        try {
            JSONObject jsonObject = loginService.liShangLogin(ticket, callbackUri);
            if (jsonObject.containsKey("error")) {
                return ResponseVo.response(MessageCode.REQUEST_ERROR, jsonObject.getString("error"));
            } else {
                return ResponseVo.response(MessageCode.SUCCESS, jsonObject.getObject("user", LoginVo.UserVo.class));
            }
        } catch (Exception e) {
            logger.error(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


    /**
     * 刷新token
     *
     * @return T
     */
    @GetMapping("/refresh/token")
    public ResponseEntity<ResponseVo.ResponseBean> refreshToken() {
        try {
            String token = JwtUtil.getToken(CurrentUserUtil.get());
            return ResponseVo.response(MessageCode.SUCCESS, null, token);
        } catch (Exception e) {
            logger.error(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据用户openId 获取token
     *
     * @return T
     */
    @GetMapping("/get/token")
    public ResponseEntity<ResponseVo.ResponseBean> getTokenByOpenId(@RequestParam("companyId") String companyId, @RequestParam("openId") String openId) {
        try {
            String token = loginService.getTokenByOpenId(companyId, openId);
            return ResponseVo.response(MessageCode.SUCCESS, null, token);
        } catch (Exception e) {
            logger.error(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 用户-获取用户详情
     *
     * @return T
     */
    @PostMapping("/info/{token}")
    public ResponseEntity<ResponseVo.ResponseBean> getUserInfo(@PathVariable("token") String token) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, loginService.getLoginInfo(token));
        } catch (Exception e) {
            logger.error(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, "无效TOKEN");
        }
    }
}
