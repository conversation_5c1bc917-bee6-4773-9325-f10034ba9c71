package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.DimensionAuth;
import com.taiyi.shuduoduo.ims.service.DimensionAuthService;
import com.taiyi.shuduoduo.ims.vo.DimAuthVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/dimension/auth")
@Validated
public class DimensionAuthController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DimensionAuthService dimensionAuthService;

    /**
     * 新增数据
     *
     * @param t
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated DimensionAuth t) {
        boolean f;
        try {
            f = dimensionAuthService.save(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 查询单条数据
     *
     * @param id 主键
     * @return
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            DimensionAuth t = dimensionAuthService.getById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 删除数据
     *
     * @param id 主键
     * @return
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = dimensionAuthService.deleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 分页查询
     *
     * @param pageVo
     * @return
     */
    @PostMapping("/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody DimAuthVo.PageVo pageVo) {
        try {
            PageResult page = dimensionAuthService.selectPage(pageVo);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 更新部分字段信息
     *
     * @param t
     * @return
     */
    @PatchMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> patch(@PathVariable String id, @RequestBody @Validated DimensionAuth t) {
        boolean f;
        try {
            f = dimensionAuthService.updateById(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
