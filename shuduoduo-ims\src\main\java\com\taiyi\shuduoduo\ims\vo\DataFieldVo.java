package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 数据域
 *
 * <AUTHOR>
 */
@Data
public class DataFieldVo {

    private String id;
    private String name;
    private String code;
    private String plateId;

    private Long orderBy;

    private Integer total;

    @Data
    public static class AddDataField {
        @NotBlank
        private String plateId;
        @NotBlank
        private String name;
        @NotBlank
        private String code;
        private String description;
    }

    @Data
    public static class AddDataFieldList {
        private String id;
        @NotNull
        private String plateId;
        @NotNull
        private String plateName;
        @NotNull
        private String name;
        @NotNull
        private String code;
        @NotNull
        private String description;

        private Long orderBy;

        private Integer type;
    }

    /**
     * 分页参数
     */
    @Data
    public static class DataFieldPageVo extends CommonMySqlPageVo {

        private String plateId;
    }

}
