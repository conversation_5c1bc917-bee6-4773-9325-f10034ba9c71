package com.taiyi.shuduoduo.ums.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ums_user_role")
public class UserRole extends CommonMySqlEntity {
    /**
     * 是否锁定 0:否 1: 是
     */
    private Boolean ifLock;

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 用户id
     */
    private String userId;

    private String companyId;
}