package com.taiyi.shuduoduo.ims.entity;

/**
 * 授权对象枚举类
 *
 * <AUTHOR>
 */
public enum AuthAgentEnum {

    USER(1, "账号"),
    ROLE(2, "角色"),
    DEPT(3, "部门"),
    NAME(4, "姓名");

    private Integer type;

    private String value;

    AuthAgentEnum(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public static String getValue(Integer type) {
        for (AuthAgentEnum agentEnum : values()) {
            if (agentEnum.getType().intValue() == type.intValue()) {
                return agentEnum.value;
            }
        }
        return "";
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
