package com.taiyi.common.connector.netty.utils;

import com.taiyi.common.connector.netty.vo.DataEngineMsg;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;

/**
 * <AUTHOR>
 */
public class DataMessageEncoder extends MessageToByteEncoder<DataEngineMsg> {
    @Override
    public void encode(ChannelHandlerContext ctx, DataEngineMsg msg, ByteBuf out) {
        byte[] bytes = ProtostuffUtil.serialize(msg);
//        byte[] delimiter = "&".getBytes();
//        byte[] total = new byte[delimiter.length + bytes.length];
//        System.arraycopy(delimiter, 0, total, bytes.length, delimiter.length);
//        System.arraycopy(bytes, 0, total, 0, bytes.length);
        out.writeInt(bytes.length);
        out.writeBytes(bytes);
    }
}
