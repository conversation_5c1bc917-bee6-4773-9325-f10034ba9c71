package com.taiyi.shuduoduo.sms.rpc;

import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.sms.api.dto.DictDTO;
import com.taiyi.shuduoduo.sms.service.DictService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 数据字典
 */
@RestController
@RequestMapping("/rpc/dict")
public class DictRpcController {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DictService dictService;

    /**
     * 根据类型查询数据字典
     *
     * @return bool
     */
    @GetMapping("/list")
    private List<DictDTO> list(@RequestParam("dictType") @Validated @NotNull String dictType) {
        return BeanUtil.copyList(dictService.listByType(dictType), DictDTO.class);
    }

    @PostMapping("/detail/value/{dictType}")
    private DictDTO detailByValue(@PathVariable("dictType") String dictType, @RequestBody @Validated @NotBlank String dictValue) {
        return BeanUtil.copy(dictService.getNameByTypeAndValue(dictType, Integer.valueOf(dictValue)), DictDTO.class);
    }

    @PostMapping("/detail/name/{dictType}")
    private DictDTO detailByName(@PathVariable("dictType") String dictType, @RequestBody @Validated @NotBlank String dictName) {
        return BeanUtil.copy(dictService.getValueByTypeAndName(dictType, dictName), DictDTO.class);
    }
}
