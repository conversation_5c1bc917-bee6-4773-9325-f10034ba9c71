package com.taiyi.shuduoduo.sms.rpc;

import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.sms.api.dto.LogDTO;
import com.taiyi.shuduoduo.sms.entity.Log;
import com.taiyi.shuduoduo.sms.service.LogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 日志采集
 *
 * <AUTHOR>
 * @since 2022/8/25
 */
@RestController
@RequestMapping("/rpc/log")
public class LogRpcController {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private LogService logService;

    /**
     * 保存日志
     *
     * @param logDTO 添加信息
     * @return bool
     */
    @PostMapping
    private boolean saveLog(@RequestBody @Validated LogDTO logDTO) {
        Log log = BeanUtil.copy(logDTO, Log.class);
        return logService.save(log);
    }
}
