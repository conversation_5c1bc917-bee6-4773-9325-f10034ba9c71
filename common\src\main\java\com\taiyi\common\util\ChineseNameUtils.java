package com.taiyi.common.util;

import java.util.*;

/**
 * 中文姓名处理工具类
 */
public class ChineseNameUtils {

    // 常见复姓集合（来自中国公安部的常见复姓列表）
    private static final Set<String> COMPOUND_SURNAMES = new HashSet<>(Arrays.asList(
            "欧阳", "上官", "司马", "诸葛", "令狐", "皇甫", "宇文", "长孙", "慕容", "尉迟",
            "司徒", "司空", "南宫", "西门", "东门", "东郭", "南郭", "北郭", "百里", "第五",
            "申屠", "公孙", "公羊", "公冶", "公良", "公叔", "仲孙", "钟离", "淳于", "单于",
            "太史", "端木", "巫马", "乐正", "拓跋", "鲜于", "闻人", "谭谈", "逯安", "石匠",
            "即墨", "相里", "相望", "独孤", "南门", "呼延", "羊舌", "微生", "梁丘", "左丘",
            "东楼", "西楼", "夹谷", "段干", "段干", "公冶", "公祖", "公西", "南荣", "南宫",
            "东方", "东野", "东郭", "南郭", "北宫", "西门", "第五", "第二", "第三", "第四"
    ));

    /**
     * 自动分割中文姓名为姓氏和名字
     *
     * @param fullName 中文姓名
     * @return 包含 firstName（姓）和 lastName（名）的 Map
     */
    public static Map<String, String> splitChineseName(String fullName) {
        Map<String, String> result = new HashMap<>();
        if (fullName == null || fullName.length() < 2) {
            throw new IllegalArgumentException("姓名长度不合法");
        }

        String surname;
        String givenName;

        // 如果是复姓
        if (COMPOUND_SURNAMES.contains(fullName.substring(0, 2))) {
            surname = fullName.substring(0, 2);
            givenName = fullName.substring(2);
        } else {
            // 单姓
            surname = fullName.substring(0, 1);
            givenName = fullName.substring(1);
        }

        result.put("firstName", surname);
        result.put("lastName", givenName);
        return result;
    }

    /**
     * 获取姓氏
     */
    public static String getFirstName(String fullName) {
        return splitChineseName(fullName).get("firstName");
    }

    /**
     * 获取名字
     */
    public static String getLastName(String fullName) {
        return splitChineseName(fullName).get("lastName");
    }
}