package com.taiyi.common.data.taskManager.controller;

import com.taiyi.common.data.taskManager.registry.TaskRegistry;
import com.taiyi.common.data.taskManager.service.ControllableTask;
import com.taiyi.common.entity.*;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * 任务控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/task")
public class TaskControllerBase {

    /**
     * 手动触发任务
     *
     * @param name 任务名称
     * @return 提示信息
     */
    @PostMapping("/{name}/run")
    public ResponseEntity run(@PathVariable String name) {
        ControllableTask task = TaskRegistry.get(name);
        if (task == null) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "任务不存在");
        }
        task.execute();
        return ResponseVo.response(MessageCode.SUCCESS, "已手动触发：" + name);
    }

    /**
     * 暂停任务
     *
     * @param name 任务名称
     * @return 提示信息
     */
    @PostMapping("/{name}/stop")
    public ResponseEntity stop(@PathVariable String name) {
        ControllableTask task = TaskRegistry.get(name);
        if (task == null) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "任务不存在");
        }
        task.stop();
        return ResponseVo.response(MessageCode.SUCCESS, "任务已暂停：" + name);
    }

    /**
     * 恢复任务
     *
     * @param name 任务名称
     * @return 提示信息
     */
    @PostMapping("/{name}/start")
    public ResponseEntity start(@PathVariable String name) {
        ControllableTask task = TaskRegistry.get(name);
        if (task == null) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "任务不存在");
        }
        task.start();
        return ResponseVo.response(MessageCode.SUCCESS, "任务已恢复：" + name);
    }

    /**
     * 获取任务状态
     *
     * @return 任务状态
     */
    @GetMapping("/status")
    public ResponseEntity status() {
        return ResponseVo.response(MessageCode.SUCCESS, TaskRegistry.all().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().getStatus().name()
                )));
    }
}

