package com.taiyi.common.auth;

import com.taiyi.common.entity.RequestMapping;
import com.taiyi.common.service.ApplicationRequestMappingService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
@AutoConfigureMockMvc
class AuthApplicationTest {

    @Autowired
    private ApplicationRequestMappingService service;

    @Test
    void requestMapping() {
        List<RequestMapping> list = service.getRequestMapping();
        list.forEach(System.out::println);
        System.out.println(list.size());
        Assertions.assertFalse(list.isEmpty());
    }
}
