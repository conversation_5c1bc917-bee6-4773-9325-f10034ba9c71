package com.taiyi.shuduoduo.ums.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 角色类
 *
 * <AUTHOR>
 */
@Data
public class UserRoleVo {

    private String companyId;


    /**
     * 是否锁定 0:否 1: 是
     */
    private Boolean ifLock;

    /**
     * 角色id
     */
    private String roleId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 分页查询参数
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class PageParam extends CommonMySqlPageVo {
    }

    /**
     * 批量添加角色
     */
    @Data
    public static class InsertRoleBatchRequest {
        private List<UserRoleVo> list;
    }

    /**
     * 批量添加用户
     */
    @Data
    public static class InsertUserBatchRequest {

        @NotBlank
        private String roleId;

        @NotEmpty
        private List<String> userIds;
    }

    /**
     * 批量删除
     */
    @Data
    public static class DeleteBatchRequest {
        private List<String> ids;
    }

    /**
     * 分页返回参数
     */
    @Data
    public static class PageResponse {

        private String id;

        /**
         * 角色id
         */
        private String roleId;

        /**
         * 用户id
         */
        private String userId;

        /**
         * 角色名称
         */
        private String name;
        private String realName;

        private String nickname;

        private String thirdUserId;

        private List<String> dept;
    }
}
