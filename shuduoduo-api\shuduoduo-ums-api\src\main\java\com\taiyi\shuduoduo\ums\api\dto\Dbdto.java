package com.taiyi.shuduoduo.ums.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
public class Dbdto {

    private String engineHost;

    /**
     * 地址
     */
    @NotBlank
    private String host;

    /**
     * 端口
     */
    @NotBlank
    private Integer port;

    /**
     * 用户名
     */
    @NotBlank
    private String username;

    /**
     * 密码
     */
    @NotBlank
    private String password;

    /**
     * 数据库类型
     */
    @NotBlank
    private String type;

    /**
     * 数据库
     */
    @NotBlank
    private String database;

    /**
     * schema
     */
    private String schema;

    public enum DbType {
        /**
         * mysql
         */
        MYSQL, POSTGRE, ORACLE;

    }

}
