package com.taiyi.shuduoduo.ums;

import com.taiyi.common.entity.MicroServer;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

/**
 * 用户模块
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableFeignClients(MicroServer.BASE_PACKAGES)
@EnableEncryptableProperties
@MapperScan(basePackages = {"com.taiyi.shuduoduo.ums.dao"})
public class ShuduoduoUmsApplication {
    public static void main(String[] args) {
        SpringApplication.run(ShuduoduoUmsApplication.class, args);
    }
}
