package com.taiyi.shuduoduo.sms.api.service;

import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.sms.api.dto.DbTypeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoSms.SERVER_NAME)
public interface DbTypeRpcService {

    /**
     * 根据类型获取数仓类型
     *
     * @param type 类型
     * @return 数仓类型
     */
    @GetMapping(MicroServer.ShuduoduoSms.SERVER_PREFIX + "/rpc/dbType/getByType")
    DbTypeDTO getByType(@RequestParam("type") Integer type);
}
