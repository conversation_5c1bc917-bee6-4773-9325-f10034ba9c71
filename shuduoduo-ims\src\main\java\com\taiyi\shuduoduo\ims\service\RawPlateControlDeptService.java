package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.RawPlateControlDeptDao;
import com.taiyi.shuduoduo.ims.entity.RawPlateControlDept;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RawPlateControlDeptService extends CommonMysqlService<RawPlateControlDeptDao, RawPlateControlDept> {
    @Override
    public Class<RawPlateControlDept> getEntityClass() {
        return RawPlateControlDept.class;
    }

    /**
     * 根据台账ID查询关联关系数据
     *
     * @param rawId 台账ID
     * @return List
     */
    public List<RawPlateControlDept> listByRawId(String rawId) {
        QueryWrapper<RawPlateControlDept> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(rawId), "raw_id", rawId).eq("if_deleted", false);
        return super.list(wrapper);
    }

    /**
     * 根据台账ID删除关联关系
     *
     * @param rawId 台账ID
     * @return boolean
     */
    public boolean logicDeleteByRawId(String rawId) {
        List<RawPlateControlDept> tables = listByRawId(rawId);
        if (tables.isEmpty()) {
            return true;
        }
        UpdateWrapper<RawPlateControlDept> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", true)
                .eq("raw_id", rawId);
        return this.update(wrapper);
    }

    /**
     * 根据台账ID 批量保存关联关系
     *
     * @param companyId 企业ID
     * @param rawId     台账ID
     * @param ids       数据来源ID列表
     * @return bool
     */
    public boolean saveBatchByRaw(String companyId, String rawId, List<String> ids) {
        List<RawPlateControlDept> list = new ArrayList<>();
        for (String id : ids) {
            RawPlateControlDept rawPlateControlDept = new RawPlateControlDept();
            rawPlateControlDept.setCompanyId(companyId);
            rawPlateControlDept.setRawId(rawId);
            rawPlateControlDept.setControlDeptId(id);
            list.add(rawPlateControlDept);
        }
        return super.saveBatch(list);
    }

    /**
     * 根据台账ID 数据来源ID列表
     *
     * @param rawId 台账ID
     * @return list
     */
    public List<String> detailListByRawId(String rawId) {
        return listByRawId(rawId).stream().collect(ArrayList::new, (list, rawPlateControlDept) -> list.add(rawPlateControlDept.getControlDeptId()), ArrayList::addAll);
    }

    public List<String> getRawListByControlDeptId(String controlDeptId) {
        QueryWrapper<RawPlateControlDept> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(controlDeptId), "control_dept_id", controlDeptId).eq("if_deleted", false);
        return super.list(wrapper).stream().collect(ArrayList::new, (list, rawPlateControlDept) -> list.add(rawPlateControlDept.getRawId()), ArrayList::addAll);
    }
}