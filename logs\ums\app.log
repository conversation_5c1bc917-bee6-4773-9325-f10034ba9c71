2025-07-19 17:17:25.721 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 17:17:25.724 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:17:25.725 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:25.725 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:25.726 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:17:25.727 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:17:25.727 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:27.250 [main] INFO  c.t.s.ums.ShuduoduoUmsApplication - The following profiles are active: local
2025-07-19 17:17:28.552 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-19 17:17:28.556 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 17:17:28.603 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25ms. Found 0 Redis repository interfaces.
2025-07-19 17:17:28.692 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 17:17:28.842 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=b0e3db8b-b581-30cf-98ee-c72eb0a42977
2025-07-19 17:17:28.883 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 17:17:28.884 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:17:28.884 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 17:17:28.885 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 17:17:28.885 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:28.885 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:17:28.886 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:17:28.886 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:28.886 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:28.887 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:28.887 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:28.887 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:28.887 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:17:29.532 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 17:17:29.544 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 17:17:29.549 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 17:17:29.581 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:17:29.604 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$a5d7bd62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:17:29.636 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:17:29.728 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:17:29.884 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:17:31.303 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8870 (http)
2025-07-19 17:17:31.319 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 17:17:31.320 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.37]
2025-07-19 17:17:31.556 [main] INFO  o.a.c.c.C.[.[.[/shuduoduo/ums] - Initializing Spring embedded WebApplicationContext
2025-07-19 17:17:31.557 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4285 ms
2025-07-19 17:17:31.846 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 17:17:32.892 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join properties config complete
2025-07-19 17:17:33.018 [main] WARN  com.zaxxer.hikari.HikariConfig - mysql - idleTimeout has been set but has no effect because the pool is operating as a fixed size pool.
2025-07-19 17:17:33.021 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Starting...
2025-07-19 17:17:33.477 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Start completed.
2025-07-19 17:17:33.481 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Starting...
2025-07-19 17:17:33.554 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Start completed.
2025-07-19 17:17:33.554 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sqlite] success
2025-07-19 17:17:33.554 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [mysql] success
2025-07-19 17:17:33.555 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [mysql]
2025-07-19 17:17:33.632 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join SqlInjector init
2025-07-19 17:17:36.440 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.taiyi.shuduoduo.ums.dao.RoleDao.selectOne] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.github.yulichang.method.mp.SelectOne]
2025-07-19 17:17:36.452 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.taiyi.shuduoduo.ums.dao.RoleDao.selectPage] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.github.yulichang.method.mp.SelectPage]
2025-07-19 17:17:36.692 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 17:17:36.692 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 17:17:36.700 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 17:17:36.700 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 17:17:37.000 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-07-19 17:17:37.098 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-19 17:17:38.822 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-07-19 17:17:41.788 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 19 endpoint(s) beneath base path '/actuator'
2025-07-19 17:17:41.879 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8870 (http) with context path '/shuduoduo/ums'
2025-07-19 17:17:41.880 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 17:17:41.881 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 17:17:41.881 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 17:17:41.882 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 17:17:41.882 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 17:17:41.882 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application-local.yml] refreshed
2025-07-19 17:17:41.882 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.properties] refreshed
2025-07-19 17:17:41.882 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.yml] refreshed
2025-07-19 17:17:41.882 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudDefaultProperties refreshed
2025-07-19 17:17:41.882 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source cachedrandom refreshed
2025-07-19 17:17:41.882 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:17:41.883 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 17:17:41.883 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-19 17:17:41.883 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
2025-07-19 17:17:41.940 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP shuduoduo-ums *************:8870 register finished
2025-07-19 17:17:42.950 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 17:17:42.951 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 17:17:42.951 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 17:17:42.951 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 17:17:42.951 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 17:17:42.952 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:17:42.952 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:17:42.952 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:17:42.952 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:17:42.958 [main] INFO  c.t.c.l.NacosConfigRefreshListener - [配置监听] Nacos配置动态监听器已加载
2025-07-19 17:17:42.976 [main] INFO  c.t.s.ums.ShuduoduoUmsApplication - Started ShuduoduoUmsApplication in 18.795 seconds (JVM running for 19.347)
2025-07-19 17:17:43.020 [main] INFO  c.taiyi.shuduoduo.ums.init.DataInit - UMS 启动成功，初始化数据开始。
2025-07-19 17:46:55.606 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-07-19 17:46:55.625 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 17:46:55.628 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-19 17:46:55.628 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-07-19 17:46:55.630 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-19 17:46:55.632 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-19 17:46:55.632 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown initiated...
2025-07-19 17:46:55.636 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown completed.
2025-07-19 17:46:55.637 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown initiated...
2025-07-19 17:46:55.639 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown completed.
2025-07-19 17:46:55.639 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-19 21:32:35.236 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:32:35.242 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:32:35.245 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:35.245 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:35.246 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:32:35.246 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:32:35.246 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:36.865 [main] INFO  c.t.s.ums.ShuduoduoUmsApplication - The following profiles are active: local
2025-07-19 21:32:38.293 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-19 21:32:38.298 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 21:32:38.336 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19ms. Found 0 Redis repository interfaces.
2025-07-19 21:32:38.418 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 21:32:38.556 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=b0e3db8b-b581-30cf-98ee-c72eb0a42977
2025-07-19 21:32:38.596 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:32:38.596 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:32:38.596 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:32:38.598 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:32:38.598 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:38.598 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:32:38.598 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:32:38.598 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:38.599 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:38.599 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:38.599 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:38.599 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:38.599 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:32:39.222 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 21:32:39.234 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 21:32:39.239 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 21:32:39.265 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:32:39.291 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$547bb525] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:32:39.331 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:32:39.449 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:32:39.620 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:32:41.315 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8870 (http)
2025-07-19 21:32:41.330 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 21:32:41.331 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.37]
2025-07-19 21:32:41.564 [main] INFO  o.a.c.c.C.[.[.[/shuduoduo/ums] - Initializing Spring embedded WebApplicationContext
2025-07-19 21:32:41.564 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4671 ms
2025-07-19 21:32:41.838 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 21:32:43.006 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join properties config complete
2025-07-19 21:32:43.180 [main] WARN  com.zaxxer.hikari.HikariConfig - mysql - idleTimeout has been set but has no effect because the pool is operating as a fixed size pool.
2025-07-19 21:32:43.182 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Starting...
2025-07-19 21:32:43.621 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Start completed.
2025-07-19 21:32:43.625 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Starting...
2025-07-19 21:32:43.703 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Start completed.
2025-07-19 21:32:43.703 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sqlite] success
2025-07-19 21:32:43.704 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [mysql] success
2025-07-19 21:32:43.704 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [mysql]
2025-07-19 21:32:43.777 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join SqlInjector init
2025-07-19 21:32:46.703 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.taiyi.shuduoduo.ums.dao.RoleDao.selectOne] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.github.yulichang.method.mp.SelectOne]
2025-07-19 21:32:46.713 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.taiyi.shuduoduo.ums.dao.RoleDao.selectPage] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.github.yulichang.method.mp.SelectPage]
2025-07-19 21:32:46.969 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 21:32:46.970 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 21:32:46.979 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 21:32:46.979 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 21:32:47.299 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-07-19 21:32:47.394 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-19 21:32:49.292 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-07-19 21:32:52.050 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 19 endpoint(s) beneath base path '/actuator'
2025-07-19 21:32:52.128 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8870 (http) with context path '/shuduoduo/ums'
2025-07-19 21:32:52.129 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 21:32:52.129 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 21:32:52.129 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 21:32:52.130 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 21:32:52.130 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 21:32:52.130 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application-local.yml] refreshed
2025-07-19 21:32:52.130 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.properties] refreshed
2025-07-19 21:32:52.130 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.yml] refreshed
2025-07-19 21:32:52.130 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudDefaultProperties refreshed
2025-07-19 21:32:52.130 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source cachedrandom refreshed
2025-07-19 21:32:52.130 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:32:52.130 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:32:52.130 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-19 21:32:52.130 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
2025-07-19 21:32:52.187 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP shuduoduo-ums *************:8870 register finished
2025-07-19 21:32:53.217 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 21:32:53.218 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 21:32:53.218 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 21:32:53.218 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 21:32:53.218 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 21:32:53.220 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:32:53.220 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:32:53.220 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:32:53.220 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:32:53.228 [main] INFO  c.t.c.l.NacosConfigRefreshListener - [配置监听] Nacos配置动态监听器已加载
2025-07-19 21:32:53.252 [main] INFO  c.t.s.ums.ShuduoduoUmsApplication - Started ShuduoduoUmsApplication in 20.317 seconds (JVM running for 21.065)
2025-07-19 21:32:53.313 [main] INFO  c.taiyi.shuduoduo.ums.init.DataInit - UMS 启动成功，初始化数据开始。
2025-07-19 21:33:17.481 [http-nio-8870-exec-1] INFO  o.a.c.c.C.[.[.[/shuduoduo/ums] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 21:33:17.481 [http-nio-8870-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 21:33:17.490 [http-nio-8870-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 9 ms
2025-07-19 21:33:17.581 [http-nio-8870-exec-1] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$b24fea77 - request path: /shuduoduo/ums/company/list
2025-07-19 21:33:17.606 [http-nio-8870-exec-1] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :bbdfd369-717e-4f25-b231-cbb8a6a0e486, device: null, path: /shuduoduo/ums/company/list, method: POST, request params :[演示], request json: ["演示"]
2025-07-19 21:33:17.754 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.CompanyDao.selectList - ==>  Preparing: SELECT id,agent_id,corp_id,display_id,name,remark,secret,state,tenant_key,type,if_dws_authed,version,create_time,update_time FROM ums_company WHERE (if_deleted = ? AND name LIKE ?)
2025-07-19 21:33:17.773 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.CompanyDao.selectList - ==> Parameters: 0(Integer), %演示%(String)
2025-07-19 21:33:17.809 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.CompanyDao.selectList - <==      Total: 1
2025-07-19 21:33:17.855 [http-nio-8870-exec-1] DEBUG c.t.common.aspect.WebApiLogAspect - api request :bbdfd369-717e-4f25-b231-cbb8a6a0e486, device: null, response data: {"body":{"code":"A000","data":[{"agentId":"0","corpId":"cli_a14bf0c5e538d00d","id":"7172070a1c264c2bb6bb201e17bf8ee7","name":"演示租户","type":3}],"message":"返回正常","tips":"返回正常"},"headers":{},"statusCode"
2025-07-19 21:33:28.724 [http-nio-8870-exec-2] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$b24fea77 - request path: /shuduoduo/ums/login/adminlogin
2025-07-19 21:33:28.810 [http-nio-8870-exec-2] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :0c9e8870-0923-4111-b85e-8642350ca4eb, device: null, path: /shuduoduo/ums/login/adminlogin, method: POST, request params :[LoginVo.AdminVo(name=admin, pwd=1578214679e95319fe07ad6352c68c40, companyId=7172070a1c264c2bb6bb201e17bf8ee7)], request json: [{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","name":"admin","pwd":"1578214679e95319fe07ad6352c68c40"}]
2025-07-19 21:33:28.842 [http-nio-8870-exec-2] DEBUG c.t.s.ums.dao.UserDao.selectList - ==>  Preparing: SELECT id,company_id,birthday,gender,id_card,if_admin,if_backend,if_lock,mobile,open_id,third_user_id,nickname,pwd_digest,real_name,avatar_uri,emp_status,dremio_pwd_digest,version,create_time,update_time FROM ums_user WHERE (company_id = ? AND nickname = ?)
2025-07-19 21:33:28.842 [http-nio-8870-exec-2] DEBUG c.t.s.ums.dao.UserDao.selectList - ==> Parameters: 7172070a1c264c2bb6bb201e17bf8ee7(String), admin(String)
2025-07-19 21:33:28.874 [http-nio-8870-exec-2] DEBUG c.t.s.ums.dao.UserDao.selectList - <==      Total: 1
2025-07-19 21:33:28.878 [http-nio-8870-exec-2] DEBUG c.t.s.ums.dao.UserDao.selectList - ==>  Preparing: SELECT id,company_id,birthday,gender,id_card,if_admin,if_backend,if_lock,mobile,open_id,third_user_id,nickname,pwd_digest,real_name,avatar_uri,emp_status,dremio_pwd_digest,version,create_time,update_time FROM ums_user WHERE (company_id = ? AND nickname = ?)
2025-07-19 21:33:28.878 [http-nio-8870-exec-2] DEBUG c.t.s.ums.dao.UserDao.selectList - ==> Parameters: 7172070a1c264c2bb6bb201e17bf8ee7(String), admin(String)
2025-07-19 21:33:28.906 [http-nio-8870-exec-2] DEBUG c.t.s.ums.dao.UserDao.selectList - <==      Total: 1
2025-07-19 21:33:28.921 [http-nio-8870-exec-2] ERROR c.t.s.ums.controller.LoginController - 管理员登录失败,java.lang.NullPointerException
	at com.taiyi.shuduoduo.ums.service.LoginService.adminLogin(LoginService.java:158)
	at com.taiyi.shuduoduo.ums.controller.LoginController.adminLogin(LoginController.java:67)
	at com.taiyi.shuduoduo.ums.controller.LoginController$$FastClassBySpringCGLIB$$917bca23.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:771)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:55)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:47)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:56)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:749)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:88)
	at com.taiyi.shuduoduo.ums.aspect.ApiLoginAspect.doAround(ApiLoginAspect.java:90)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(
2025-07-19 21:33:28.922 [http-nio-8870-exec-2] DEBUG c.t.common.aspect.WebApiLogAspect - api request :0c9e8870-0923-4111-b85e-8642350ca4eb, device: null, response data: {"body":{"code":"B300","data":{},"message":"用户请求异常","tips":"用户请求异常"},"headers":{},"statusCode":"BAD_REQUEST","statusCodeValue":400}
2025-07-19 21:33:28.928 [http-nio-8870-exec-2] INFO  c.t.s.ums.aspect.ApiLoginAspect - 性能监控（耗时） : 146毫秒
2025-07-19 21:33:28.936 [http-nio-8870-exec-2] DEBUG c.t.c.c.FeignInterceptorConfig$$EnhancerBySpringCGLIB$$3a5e8267 - Feign rpc request add request id: 0c9e8870-0923-4111-b85e-8642350ca4eb ,token: null
2025-07-19 21:33:29.159 [http-nio-8870-exec-2] INFO  c.n.config.ChainedDynamicProperty - Flipping property: shuduoduo-sms.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:33:29.174 [http-nio-8870-exec-2] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: shuduoduo-sms instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=shuduoduo-sms,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-07-19 21:33:29.181 [http-nio-8870-exec-2] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-07-19 21:33:29.213 [http-nio-8870-exec-2] INFO  c.n.config.ChainedDynamicProperty - Flipping property: shuduoduo-sms.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:33:29.216 [http-nio-8870-exec-2] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client shuduoduo-sms initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=shuduoduo-sms,current list of Servers=[*************:8860],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:*************:8860;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@35d11d5c
2025-07-19 21:33:30.184 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: shuduoduo-sms.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-07-19 21:35:02.866 [http-nio-8870-exec-3] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$b24fea77 - request path: /shuduoduo/ums/login/adminlogin
2025-07-19 21:35:02.894 [http-nio-8870-exec-3] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :de304582-6799-4851-ab89-a09ac720fc58, device: null, path: /shuduoduo/ums/login/adminlogin, method: POST, request params :[LoginVo.AdminVo(name=admin, pwd=1801c3547cec87a6acd385ecc1fd2a88, companyId=7172070a1c264c2bb6bb201e17bf8ee7)], request json: [{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","name":"admin","pwd":"1801c3547cec87a6acd385ecc1fd2a88"}]
2025-07-19 21:35:02.914 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.UserDao.selectList - ==>  Preparing: SELECT id,company_id,birthday,gender,id_card,if_admin,if_backend,if_lock,mobile,open_id,third_user_id,nickname,pwd_digest,real_name,avatar_uri,emp_status,dremio_pwd_digest,version,create_time,update_time FROM ums_user WHERE (company_id = ? AND nickname = ?)
2025-07-19 21:35:02.915 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.UserDao.selectList - ==> Parameters: 7172070a1c264c2bb6bb201e17bf8ee7(String), admin(String)
2025-07-19 21:35:02.932 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.UserDao.selectList - <==      Total: 1
2025-07-19 21:35:02.934 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.UserDao.selectList - ==>  Preparing: SELECT id,company_id,birthday,gender,id_card,if_admin,if_backend,if_lock,mobile,open_id,third_user_id,nickname,pwd_digest,real_name,avatar_uri,emp_status,dremio_pwd_digest,version,create_time,update_time FROM ums_user WHERE (company_id = ? AND nickname = ?)
2025-07-19 21:35:02.934 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.UserDao.selectList - ==> Parameters: 7172070a1c264c2bb6bb201e17bf8ee7(String), admin(String)
2025-07-19 21:35:02.952 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.UserDao.selectList - <==      Total: 1
2025-07-19 21:35:02.979 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - ==>  Preparing: SELECT id,if_lock,role_id,user_id,company_id,version,create_time,update_time FROM ums_user_role WHERE (if_lock = ? AND user_id = ?)
2025-07-19 21:35:02.980 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - ==> Parameters: false(Boolean), 16aec66e2ef54634950d26b532113571(String)
2025-07-19 21:35:02.996 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - <==      Total: 0
2025-07-19 21:35:03.003 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.MenusDao.selectList - ==>  Preparing: SELECT id,if_deleted,disabled,name,order_by,pid,route_url,web_id,version,create_time,update_time FROM ums_menus WHERE (id IN (?,?,?))
2025-07-19 21:35:03.003 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.MenusDao.selectList - ==> Parameters: 1(String), 10(String), 11(String)
2025-07-19 21:35:03.022 [http-nio-8870-exec-3] DEBUG c.t.s.ums.dao.MenusDao.selectList - <==      Total: 3
2025-07-19 21:35:03.028 [http-nio-8870-exec-3] DEBUG c.t.common.aspect.WebApiLogAspect - api request :de304582-6799-4851-ab89-a09ac720fc58, device: null, response data: {"body":{"code":"A000","data":{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","expiresIn":2592000,"ifAdmin":true,"permissions":["web-home","web-dataBrowser","web-dataBrowser-generalLedger"],"realName"
2025-07-19 21:35:03.030 [http-nio-8870-exec-3] INFO  c.t.s.ums.aspect.ApiLoginAspect - 性能监控（耗时） : 137毫秒
2025-07-19 21:35:03.031 [http-nio-8870-exec-3] DEBUG c.t.c.c.FeignInterceptorConfig$$EnhancerBySpringCGLIB$$3a5e8267 - Feign rpc request add request id: de304582-6799-4851-ab89-a09ac720fc58 ,token: null
2025-07-19 21:36:39.928 [http-nio-8870-exec-5] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$b24fea77 - request path: /shuduoduo/ums/company/list
2025-07-19 21:36:39.928 [http-nio-8870-exec-5] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :5256c082-b87e-4904-9a54-6d27b58b6ebe, device: null, path: /shuduoduo/ums/company/list, method: POST, request params :[演示], request json: ["演示"]
2025-07-19 21:36:39.948 [http-nio-8870-exec-5] DEBUG c.t.s.ums.dao.CompanyDao.selectList - ==>  Preparing: SELECT id,agent_id,corp_id,display_id,name,remark,secret,state,tenant_key,type,if_dws_authed,version,create_time,update_time FROM ums_company WHERE (if_deleted = ? AND name LIKE ?)
2025-07-19 21:36:39.948 [http-nio-8870-exec-5] DEBUG c.t.s.ums.dao.CompanyDao.selectList - ==> Parameters: 0(Integer), %演示%(String)
2025-07-19 21:36:39.965 [http-nio-8870-exec-5] DEBUG c.t.s.ums.dao.CompanyDao.selectList - <==      Total: 1
2025-07-19 21:36:39.966 [http-nio-8870-exec-5] DEBUG c.t.common.aspect.WebApiLogAspect - api request :5256c082-b87e-4904-9a54-6d27b58b6ebe, device: null, response data: {"body":{"code":"A000","data":[{"agentId":"0","corpId":"cli_a14bf0c5e538d00d","id":"7172070a1c264c2bb6bb201e17bf8ee7","name":"演示租户","type":3}],"message":"返回正常","tips":"返回正常"},"headers":{},"statusCode"
2025-07-19 21:36:53.495 [http-nio-8870-exec-6] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$b24fea77 - request path: /shuduoduo/ums/login/adminlogin
2025-07-19 21:36:53.512 [http-nio-8870-exec-6] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :2479689c-28b8-43ec-aa5e-ae5ae67d4422, device: null, path: /shuduoduo/ums/login/adminlogin, method: POST, request params :[LoginVo.AdminVo(name=admin, pwd=1801c3547cec87a6acd385ecc1fd2a88, companyId=7172070a1c264c2bb6bb201e17bf8ee7)], request json: [{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","name":"admin","pwd":"1801c3547cec87a6acd385ecc1fd2a88"}]
2025-07-19 21:36:53.530 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.UserDao.selectList - ==>  Preparing: SELECT id,company_id,birthday,gender,id_card,if_admin,if_backend,if_lock,mobile,open_id,third_user_id,nickname,pwd_digest,real_name,avatar_uri,emp_status,dremio_pwd_digest,version,create_time,update_time FROM ums_user WHERE (company_id = ? AND nickname = ?)
2025-07-19 21:36:53.531 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.UserDao.selectList - ==> Parameters: 7172070a1c264c2bb6bb201e17bf8ee7(String), admin(String)
2025-07-19 21:36:53.549 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.UserDao.selectList - <==      Total: 1
2025-07-19 21:36:53.551 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.UserDao.selectList - ==>  Preparing: SELECT id,company_id,birthday,gender,id_card,if_admin,if_backend,if_lock,mobile,open_id,third_user_id,nickname,pwd_digest,real_name,avatar_uri,emp_status,dremio_pwd_digest,version,create_time,update_time FROM ums_user WHERE (company_id = ? AND nickname = ?)
2025-07-19 21:36:53.551 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.UserDao.selectList - ==> Parameters: 7172070a1c264c2bb6bb201e17bf8ee7(String), admin(String)
2025-07-19 21:36:53.566 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.UserDao.selectList - <==      Total: 1
2025-07-19 21:36:53.577 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - ==>  Preparing: SELECT id,if_lock,role_id,user_id,company_id,version,create_time,update_time FROM ums_user_role WHERE (if_lock = ? AND user_id = ?)
2025-07-19 21:36:53.577 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - ==> Parameters: false(Boolean), 16aec66e2ef54634950d26b532113571(String)
2025-07-19 21:36:53.592 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - <==      Total: 0
2025-07-19 21:36:53.593 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.MenusDao.selectList - ==>  Preparing: SELECT id,if_deleted,disabled,name,order_by,pid,route_url,web_id,version,create_time,update_time FROM ums_menus WHERE (id IN (?,?,?))
2025-07-19 21:36:53.594 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.MenusDao.selectList - ==> Parameters: 1(String), 10(String), 11(String)
2025-07-19 21:36:53.610 [http-nio-8870-exec-6] DEBUG c.t.s.ums.dao.MenusDao.selectList - <==      Total: 3
2025-07-19 21:36:53.611 [http-nio-8870-exec-6] DEBUG c.t.common.aspect.WebApiLogAspect - api request :2479689c-28b8-43ec-aa5e-ae5ae67d4422, device: null, response data: {"body":{"code":"A000","data":{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","expiresIn":2592000,"ifAdmin":true,"permissions":["web-home","web-dataBrowser","web-dataBrowser-generalLedger"],"realName"
2025-07-19 21:36:53.613 [http-nio-8870-exec-6] INFO  c.t.s.ums.aspect.ApiLoginAspect - 性能监控（耗时） : 102毫秒
2025-07-19 21:36:53.614 [http-nio-8870-exec-6] DEBUG c.t.c.c.FeignInterceptorConfig$$EnhancerBySpringCGLIB$$3a5e8267 - Feign rpc request add request id: 2479689c-28b8-43ec-aa5e-ae5ae67d4422 ,token: null
2025-07-19 21:45:49.177 [http-nio-8870-exec-8] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$b24fea77 - request path: /shuduoduo/ums/company/list
2025-07-19 21:45:49.177 [http-nio-8870-exec-8] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :fd132cf0-eac2-4c68-9671-01ce0341a7bc, device: null, path: /shuduoduo/ums/company/list, method: POST, request params :[演示], request json: ["演示"]
2025-07-19 21:45:49.195 [http-nio-8870-exec-8] DEBUG c.t.s.ums.dao.CompanyDao.selectList - ==>  Preparing: SELECT id,agent_id,corp_id,display_id,name,remark,secret,state,tenant_key,type,if_dws_authed,version,create_time,update_time FROM ums_company WHERE (if_deleted = ? AND name LIKE ?)
2025-07-19 21:45:49.196 [http-nio-8870-exec-8] DEBUG c.t.s.ums.dao.CompanyDao.selectList - ==> Parameters: 0(Integer), %演示%(String)
2025-07-19 21:45:49.214 [http-nio-8870-exec-8] DEBUG c.t.s.ums.dao.CompanyDao.selectList - <==      Total: 1
2025-07-19 21:45:49.215 [http-nio-8870-exec-8] DEBUG c.t.common.aspect.WebApiLogAspect - api request :fd132cf0-eac2-4c68-9671-01ce0341a7bc, device: null, response data: {"body":{"code":"A000","data":[{"agentId":"0","corpId":"cli_a14bf0c5e538d00d","id":"7172070a1c264c2bb6bb201e17bf8ee7","name":"演示租户","type":3}],"message":"返回正常","tips":"返回正常"},"headers":{},"statusCode"
2025-07-19 21:46:01.324 [http-nio-8870-exec-9] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$b24fea77 - request path: /shuduoduo/ums/login/adminlogin
2025-07-19 21:46:01.349 [http-nio-8870-exec-9] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :3986bc49-faa4-4e3b-b365-fc0757e8c52c, device: null, path: /shuduoduo/ums/login/adminlogin, method: POST, request params :[LoginVo.AdminVo(name=admin, pwd=1801c3547cec87a6acd385ecc1fd2a88, companyId=7172070a1c264c2bb6bb201e17bf8ee7)], request json: [{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","name":"admin","pwd":"1801c3547cec87a6acd385ecc1fd2a88"}]
2025-07-19 21:46:01.367 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.UserDao.selectList - ==>  Preparing: SELECT id,company_id,birthday,gender,id_card,if_admin,if_backend,if_lock,mobile,open_id,third_user_id,nickname,pwd_digest,real_name,avatar_uri,emp_status,dremio_pwd_digest,version,create_time,update_time FROM ums_user WHERE (company_id = ? AND nickname = ?)
2025-07-19 21:46:01.367 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.UserDao.selectList - ==> Parameters: 7172070a1c264c2bb6bb201e17bf8ee7(String), admin(String)
2025-07-19 21:46:01.388 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.UserDao.selectList - <==      Total: 1
2025-07-19 21:46:01.390 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.UserDao.selectList - ==>  Preparing: SELECT id,company_id,birthday,gender,id_card,if_admin,if_backend,if_lock,mobile,open_id,third_user_id,nickname,pwd_digest,real_name,avatar_uri,emp_status,dremio_pwd_digest,version,create_time,update_time FROM ums_user WHERE (company_id = ? AND nickname = ?)
2025-07-19 21:46:01.391 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.UserDao.selectList - ==> Parameters: 7172070a1c264c2bb6bb201e17bf8ee7(String), admin(String)
2025-07-19 21:46:01.411 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.UserDao.selectList - <==      Total: 1
2025-07-19 21:46:01.415 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - ==>  Preparing: SELECT id,if_lock,role_id,user_id,company_id,version,create_time,update_time FROM ums_user_role WHERE (if_lock = ? AND user_id = ?)
2025-07-19 21:46:01.415 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - ==> Parameters: false(Boolean), 16aec66e2ef54634950d26b532113571(String)
2025-07-19 21:46:01.438 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - <==      Total: 0
2025-07-19 21:46:01.440 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.MenusDao.selectList - ==>  Preparing: SELECT id,if_deleted,disabled,name,order_by,pid,route_url,web_id,version,create_time,update_time FROM ums_menus WHERE (id IN (?,?,?))
2025-07-19 21:46:01.440 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.MenusDao.selectList - ==> Parameters: 1(String), 10(String), 11(String)
2025-07-19 21:46:01.461 [http-nio-8870-exec-9] DEBUG c.t.s.ums.dao.MenusDao.selectList - <==      Total: 3
2025-07-19 21:46:01.461 [http-nio-8870-exec-9] DEBUG c.t.common.aspect.WebApiLogAspect - api request :3986bc49-faa4-4e3b-b365-fc0757e8c52c, device: null, response data: {"body":{"code":"A000","data":{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","expiresIn":2592000,"ifAdmin":true,"permissions":["web-home","web-dataBrowser","web-dataBrowser-generalLedger"],"realName"
2025-07-19 21:46:01.463 [http-nio-8870-exec-9] INFO  c.t.s.ums.aspect.ApiLoginAspect - 性能监控（耗时） : 116毫秒
2025-07-19 21:46:01.464 [http-nio-8870-exec-9] DEBUG c.t.c.c.FeignInterceptorConfig$$EnhancerBySpringCGLIB$$3a5e8267 - Feign rpc request add request id: 3986bc49-faa4-4e3b-b365-fc0757e8c52c ,token: null
2025-07-19 21:46:39.385 [http-nio-8870-exec-10] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$b24fea77 - request path: /shuduoduo/ums/company/list
2025-07-19 21:46:39.386 [http-nio-8870-exec-10] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :1b561327-c958-4604-ab3c-13879f62040f, device: null, path: /shuduoduo/ums/company/list, method: POST, request params :[演示], request json: ["演示"]
2025-07-19 21:46:39.404 [http-nio-8870-exec-10] DEBUG c.t.s.ums.dao.CompanyDao.selectList - ==>  Preparing: SELECT id,agent_id,corp_id,display_id,name,remark,secret,state,tenant_key,type,if_dws_authed,version,create_time,update_time FROM ums_company WHERE (if_deleted = ? AND name LIKE ?)
2025-07-19 21:46:39.405 [http-nio-8870-exec-10] DEBUG c.t.s.ums.dao.CompanyDao.selectList - ==> Parameters: 0(Integer), %演示%(String)
2025-07-19 21:46:39.423 [http-nio-8870-exec-10] DEBUG c.t.s.ums.dao.CompanyDao.selectList - <==      Total: 1
2025-07-19 21:46:39.424 [http-nio-8870-exec-10] DEBUG c.t.common.aspect.WebApiLogAspect - api request :1b561327-c958-4604-ab3c-13879f62040f, device: null, response data: {"body":{"code":"A000","data":[{"agentId":"0","corpId":"cli_a14bf0c5e538d00d","id":"7172070a1c264c2bb6bb201e17bf8ee7","name":"演示租户","type":3}],"message":"返回正常","tips":"返回正常"},"headers":{},"statusCode"
2025-07-19 21:46:51.784 [http-nio-8870-exec-1] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$b24fea77 - request path: /shuduoduo/ums/login/adminlogin
2025-07-19 21:46:51.788 [http-nio-8870-exec-1] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :02551f6a-12d6-43e9-8e21-eda358888e21, device: null, path: /shuduoduo/ums/login/adminlogin, method: POST, request params :[LoginVo.AdminVo(name=admin, pwd=1801c3547cec87a6acd385ecc1fd2a88, companyId=7172070a1c264c2bb6bb201e17bf8ee7)], request json: [{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","name":"admin","pwd":"1801c3547cec87a6acd385ecc1fd2a88"}]
2025-07-19 21:46:51.806 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.UserDao.selectList - ==>  Preparing: SELECT id,company_id,birthday,gender,id_card,if_admin,if_backend,if_lock,mobile,open_id,third_user_id,nickname,pwd_digest,real_name,avatar_uri,emp_status,dremio_pwd_digest,version,create_time,update_time FROM ums_user WHERE (company_id = ? AND nickname = ?)
2025-07-19 21:46:51.806 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.UserDao.selectList - ==> Parameters: 7172070a1c264c2bb6bb201e17bf8ee7(String), admin(String)
2025-07-19 21:46:51.823 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.UserDao.selectList - <==      Total: 1
2025-07-19 21:46:51.825 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.UserDao.selectList - ==>  Preparing: SELECT id,company_id,birthday,gender,id_card,if_admin,if_backend,if_lock,mobile,open_id,third_user_id,nickname,pwd_digest,real_name,avatar_uri,emp_status,dremio_pwd_digest,version,create_time,update_time FROM ums_user WHERE (company_id = ? AND nickname = ?)
2025-07-19 21:46:51.825 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.UserDao.selectList - ==> Parameters: 7172070a1c264c2bb6bb201e17bf8ee7(String), admin(String)
2025-07-19 21:46:51.843 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.UserDao.selectList - <==      Total: 1
2025-07-19 21:46:51.845 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - ==>  Preparing: SELECT id,if_lock,role_id,user_id,company_id,version,create_time,update_time FROM ums_user_role WHERE (if_lock = ? AND user_id = ?)
2025-07-19 21:46:51.845 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - ==> Parameters: false(Boolean), 16aec66e2ef54634950d26b532113571(String)
2025-07-19 21:46:51.862 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.UserRoleDao.selectList - <==      Total: 0
2025-07-19 21:46:51.863 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.MenusDao.selectList - ==>  Preparing: SELECT id,if_deleted,disabled,name,order_by,pid,route_url,web_id,version,create_time,update_time FROM ums_menus WHERE (id IN (?,?,?))
2025-07-19 21:46:51.864 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.MenusDao.selectList - ==> Parameters: 1(String), 10(String), 11(String)
2025-07-19 21:46:51.880 [http-nio-8870-exec-1] DEBUG c.t.s.ums.dao.MenusDao.selectList - <==      Total: 3
2025-07-19 21:46:51.880 [http-nio-8870-exec-1] DEBUG c.t.common.aspect.WebApiLogAspect - api request :02551f6a-12d6-43e9-8e21-eda358888e21, device: null, response data: {"body":{"code":"A000","data":{"companyId":"7172070a1c264c2bb6bb201e17bf8ee7","expiresIn":2592000,"ifAdmin":true,"permissions":["web-home","web-dataBrowser","web-dataBrowser-generalLedger"],"realName"
2025-07-19 21:46:51.883 [http-nio-8870-exec-1] INFO  c.t.s.ums.aspect.ApiLoginAspect - 性能监控（耗时） : 98毫秒
2025-07-19 21:46:51.884 [http-nio-8870-exec-1] DEBUG c.t.c.c.FeignInterceptorConfig$$EnhancerBySpringCGLIB$$3a5e8267 - Feign rpc request add request id: 02551f6a-12d6-43e9-8e21-eda358888e21 ,token: null
2025-07-19 21:50:13.349 [Thread-33] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-07-19 21:50:13.787 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-07-19 21:50:13.807 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 21:50:13.810 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-19 21:50:13.811 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-07-19 21:50:13.811 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-19 21:50:13.814 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-19 21:50:13.814 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown initiated...
2025-07-19 21:50:13.818 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown completed.
2025-07-19 21:50:13.820 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown initiated...
2025-07-19 21:50:13.823 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown completed.
2025-07-19 21:50:13.823 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
