package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.RawAuthForeignDao;
import com.taiyi.shuduoduo.ims.entity.RawAuthForeign;
import com.taiyi.shuduoduo.ims.vo.RawAuthForeignVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RawAuthForeignService extends CommonMysqlService<RawAuthForeignDao, RawAuthForeign> {
    @Override
    public Class<RawAuthForeign> getEntityClass() {
        return RawAuthForeign.class;
    }

    @Autowired
    private RawAuthService rawAuthService;

    @Autowired
    private RawAuthForeignService rawAuthForeignService;

    /**
     * 编辑权限关联关系
     *
     * @param t 关联参数
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRawAuth(RawAuthForeignVo.InsertVo t) {
        boolean f = false;
        // 查询是否已绑定关联关系
        RawAuthForeignVo.DetailVo detail = rawAuthForeignService.getDetail(t.getRawAuthId());
        if (detail != null) {
            // 删除以前的关联关系
            rawAuthForeignService.deleteByRawAuthId(t.getRawAuthId());
        }
        for (RawAuthForeignVo.ForeignVo foreign : t.getForeigns()) {
            RawAuthForeign rawAuthForeign = new RawAuthForeign();
            rawAuthForeign.setRawAuthId(t.getRawAuthId());
            rawAuthForeign.setAuthAgent(t.getAuthAgent());
            rawAuthForeign.setAuthColumn(t.getAuthColumn());
            rawAuthForeign.setRawColumn(foreign.getRawColumn());
            rawAuthForeign.setTableColumn(foreign.getTableColumn());
            f = super.save(rawAuthForeign);
        }
        if (f) {
            // 修改状态
            return rawAuthService.updateBySetting(t.getRawAuthId(), t.getStatus(), t.getOperator());
        }
        return f;
    }

    @Transactional(rollbackFor = Exception.class)
    private void deleteByRawAuthId(String rawAuthId) {
        QueryWrapper<RawAuthForeign> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("raw_auth_id", rawAuthId);
        List<RawAuthForeign> list = super.list(wrapper);
        list.forEach(rawAuthForeign -> {
            super.logicDeleteById(rawAuthForeign.getId());
        });
    }

    public RawAuthForeignVo.DetailVo getDetail(String rawAuthId) {
        RawAuthForeignVo.DetailVo res = new RawAuthForeignVo.DetailVo();
        QueryWrapper<RawAuthForeign> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("raw_auth_id", rawAuthId);
        List<RawAuthForeign> list = super.list(wrapper);
        List<RawAuthForeignVo.ForeignVo> foreignVoList = new ArrayList<>();
        if (list.size() > 0) {
            res.setAuthAgent(list.get(0).getAuthAgent());
            res.setAuthColumn(list.get(0).getAuthColumn());
            for (RawAuthForeign rawAuthForeign : list) {
                RawAuthForeignVo.ForeignVo foreignVo = new RawAuthForeignVo.ForeignVo();
                foreignVo.setRawColumn(rawAuthForeign.getRawColumn());
                foreignVo.setTableColumn(rawAuthForeign.getTableColumn());
                foreignVoList.add(foreignVo);
            }
        }
        res.setForeigns(foreignVoList);
        return res;
    }

    public List<RawAuthForeign> getListByRawAuthId(String rawAuthId) {
        QueryWrapper<RawAuthForeign> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("raw_auth_id", rawAuthId);
        return super.list(wrapper);
    }
}