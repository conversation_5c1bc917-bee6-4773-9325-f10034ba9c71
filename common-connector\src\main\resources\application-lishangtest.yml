spring:
  cloud:
    nacos:
      discovery:
        server-addr: *************:18848,*************:18849
      config:
        server-addr: *************:18848,*************:18849
      username: nacos
      password: shu<PERSON><PERSON><PERSON>2023!

  redis:
    host: *************
    port: 16379
    password: LsGuoChao2023!
    timeout: 3000ms
    jedis:
      pool:
        max-idle: 500
        min-idle: 50
        max-active: 2000
        max-wait: 1000ms

netty:
  websocket:
    port: 49988
    ip: 0.0.0.0
    path: /socket/{}
    max-frame-size: 10240

# 服务端 ip , port
web:
  server:
    host: *************
    port: 9999