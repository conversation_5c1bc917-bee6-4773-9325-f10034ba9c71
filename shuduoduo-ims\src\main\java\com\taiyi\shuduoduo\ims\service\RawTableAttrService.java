package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.RawTableAttrDao;
import com.taiyi.shuduoduo.ims.entity.DimAttribute;
import com.taiyi.shuduoduo.ims.entity.RawTable;
import com.taiyi.shuduoduo.ims.entity.RawTableAttr;
import com.taiyi.shuduoduo.ims.entity.RawTableGroup;
import com.taiyi.shuduoduo.ims.vo.RawTableAttrVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class RawTableAttrService extends CommonMysqlService<RawTableAttrDao, RawTableAttr> {
    @Override
    public Class<RawTableAttr> getEntityClass() {
        return RawTableAttr.class;
    }

    @Autowired
    private RawTableAttrDao dao;

    @Autowired
    private RawTableGroupService tableGroupService;

    @Autowired
    private RawTableService rawTableService;

    public List<RawTableAttr> listByRawTableId(String rawTableId) {
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(rawTableId), "raw_table_id", rawTableId).eq("if_deleted", false).orderByAsc("order_by");
        return super.list(wrapper);
    }

    public boolean logicDeleteByRawTableId(String rawTableId) {
        List<RawTableAttr> tables = listByRawTableId(rawTableId);
        if (tables.isEmpty()) {
            return true;
        }
        UpdateWrapper<RawTableAttr> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", 1)
                .eq("raw_table_id", rawTableId);
        return this.update(wrapper);
    }

    /**
     * 保存台账表字段信息及字段分组信息
     *
     * @param rawTableId       台账表ID
     * @param rowTableAttrList 表字段信息
     * @return boolean
     */
    public boolean saveBatchByRawTableId(String companyId, String rawTableId, List<RawTableAttrVo.InsertParam> rowTableAttrList) {
        boolean f = false;
        for (RawTableAttrVo.InsertParam param : rowTableAttrList) {
            RawTableAttr tableAttr = BeanUtil.copy(param, RawTableAttr.class);
            tableAttr.setCompanyId(companyId);
            tableAttr.setRawTableId(rawTableId);
            f = super.save(tableAttr);
            if (f) {
                if (StringUtils.isNotBlank(param.getGroupName())) {
                    // 保存表字段分组、分组字段关联
                    f = tableGroupService.saveBatchByRawTableId(companyId, rawTableId, tableAttr.getId(), param);
                }
            }
        }
        return f;
    }

    public RawTableAttr getByRawTableIdAndDimAttrId(String rawTableId, String dimAttrId) {
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(rawTableId), "raw_table_id", rawTableId)
                .eq("dim_attr_id", dimAttrId)
                .eq("if_deleted", false)
                .orderByAsc("order_by")
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 根据台账表Id 查询表字段列表
     *
     * @param rawTableId 台账表ID
     * @return list
     */
    public List<RawTableAttrVo.InsertParam> detailListByRawTableId(String rawTableId) {
        List<RawTableAttrVo.InsertParam> res = BeanUtil.copyList(listByRawTableId(rawTableId), RawTableAttrVo.InsertParam.class);
        for (RawTableAttrVo.InsertParam param : res) {
            // 根据表及字段ID 查询分组
            RawTableGroup tableGroup = tableGroupService.getNameByRawTableIdAndTableAttrId(rawTableId, param.getId());
            param.setGroupName(null == tableGroup ? null : tableGroup.getName());
        }
        return res;
    }

    /**
     * 查询显示的字段列表
     *
     * @param rawTableId 台账表ID
     * @return list
     */
    public List<RawTableAttr> showListByRawTableId(String rawTableId) {
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq(CurrentUserUtil.get() != null && StringUtils.isNotBlank(CurrentUserUtil.get().getCompanyId()), "company_id", CurrentUserUtil.get().getCompanyId())
                .eq("raw_table_id", rawTableId)
                .eq("if_deleted", false)
                .eq("if_show", true)
                .orderByAsc("order_by");
        return super.list(wrapper);
    }

    /**
     * 查询未挂在分组下的字段列表
     *
     * @param rawTableId 台账表ID
     * @return list
     */
    public List<RawTableAttr> showOptionListByRawTableId(String rawTableId) {
        return dao.showOptionListByRawTableId(rawTableId);
    }

    /**
     * 查询台账详情下展示字段列表
     *
     * @param rawTableId 台账表ID
     * @return 字段列表
     */
    public List<RawTableAttrVo.DetailResponse> browserDetailByRawTableId(String rawTableId) {
        return BeanUtil.copyList(this.showListByRawTableId(rawTableId), RawTableAttrVo.DetailResponse.class);
    }

    /**
     * 根据台账表ID和分组ID查询字段列表
     *
     * @param rawTableId   台账表ID
     * @param tableGroupId 分组ID
     * @return 字段列表
     */
    public List<RawTableAttr> listByRawTableIdAndGroupId(String rawTableId, String tableGroupId) {
        return dao.listByRawTableIdAndGroupId(rawTableId, tableGroupId);
    }

    /**
     * 查询未挂在分组下的字段列表
     *
     * @param rawTableId 台账表ID
     * @return list
     */
    public List<RawTableAttrVo.DetailResponse> optionListByRawTableId(String rawTableId) {
        return BeanUtil.copyList(this.showOptionListByRawTableId(rawTableId), RawTableAttrVo.DetailResponse.class);
    }

    /**
     * 校验字段是否存在
     *
     * @param rawTableId 台账表ID
     * @param field      字段
     * @return bool
     */
    public boolean checkAttrExist(String rawTableId, String field) {
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("raw_table_id", rawTableId)
                .eq("code", field)
                .eq("if_deleted", false).last("LIMIT 1");
        return null != getOne(wrapper);
    }

    /**
     * 将维度属性字段转为台账表字段
     *
     * @param attribute 维度属性字段
     * @return 台账表字段
     */
    public RawTableAttrVo.InsertParam convertToRawTableAttrVo(DimAttribute attribute) {
        RawTableAttrVo.InsertParam res = BeanUtil.copy(attribute, RawTableAttrVo.InsertParam.class);
        res.setDimAttrId(attribute.getId());
        res.setIfDefault(false);
        res.setIfShow(false);
        res.setGroupName(null);
        return res;
    }

    /**
     * 获取默认显示的字段ID
     *
     * @param rawTableId 台账表ID
     * @return 字段ID
     */
    public List<String> defaultShowAttrListByRawTableId(String rawTableId) {
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(rawTableId), "raw_table_id", rawTableId).eq("if_deleted", false).eq("if_default", true).orderByAsc("order_by");
        return super.list(wrapper).stream().map(RawTableAttr::getId).collect(Collectors.toList());
    }

    public RawTableAttr getByCodeAndAttrIds(String companyId, String attrCode, List<String> queryColumns) {
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("code", attrCode)
                .in("id", queryColumns).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public boolean updateDwsInfo(List<String> tableIds, String dwsId) {
        boolean f = false;
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false)
                .in("raw_table_id", tableIds)
                .eq("dws_id", dwsId);
        List<RawTableAttr> tableAttrList = super.list(wrapper);
        for (RawTableAttr tableAttr : tableAttrList) {
            UpdateWrapper<RawTableAttr> wrapper1 = new UpdateWrapper<>();
            wrapper1.eq("id", tableAttr.getId()).set("dws_id", null);
            f = super.update(wrapper1);
        }
        return f;
    }

    public RawTableAttr getOneByRawTableIdAndName(@NotNull String companyId, @NotNull String rawTableId, @NotNull String name) {
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("raw_table_id", rawTableId)
                .eq("if_deleted", false)
                .eq("name", name)
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public List<String> getListNameByRawTableIdAndCompanyId(@NotNull String companyId, @NotNull String rawTableId) {
        List<String> res = new ArrayList<>();
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("raw_table_id", rawTableId)
                .eq("if_deleted", false);
        List<RawTableAttr> raws = super.list(wrapper);
        raws.forEach(raw -> res.add(raw.getName()));
        return res;
    }

    public List<String> getListNameByRawIdAndCompanyId(@NotNull String companyId, @NotNull String rawId) {
        List<String> res = new ArrayList<>();
        List<String> rawTableIds = new ArrayList<>();
        rawTableService.listByRawId(rawId).forEach(rawTable -> rawTableIds.add(rawTable.getId()));
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .in("raw_table_id", rawTableIds)
                .eq("if_deleted", false);
        List<RawTableAttr> raws = super.list(wrapper);
        raws.forEach(raw -> res.add(raw.getName()));
        return res;
    }

    public List<RawTableAttr> listByIds(List<String> idList) {
        List<RawTableAttr> res = new ArrayList<>();
        for (String s : idList) {
            res.add(super.getById(s));
        }
        return res;
    }

    public List<RawTableAttr> getListByRawTableId(String companyId, String rawTableId) {
        QueryWrapper<RawTableAttr> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("raw_table_id", rawTableId)
                .eq("if_deleted", false);
        return super.list(wrapper);
    }
}