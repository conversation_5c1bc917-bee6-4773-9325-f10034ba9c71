package com.taiyi.shuduoduo.ims.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RawForeignKeyVo {

    @ApiModel("台账外键关连参数")
    @Data
    public static class InsertParam {

        /**
         * 台账ID
         */
        @ApiModelProperty("台账ID")
        private String rawId;

        /**
         * 台账表ID
         */
        @ApiModelProperty("台账表ID")
        private String rawTableId;

        /**
         * 维度/事实表ID
         */
        @ApiModelProperty("维度/事实表ID")
        private String dimId;

        /**
         * 表编码
         */
        @ApiModelProperty("表编码")
        private String dimCode;

        /**
         * 表名称
         */
        @ApiModelProperty("表名称")
        private String dimName;

        /**
         * 表字段id
         */
        @ApiModelProperty("表字段id")
        private String dimAttrId;

        /**
         * 表字段名称
         */
        @ApiModelProperty("表字段名称")
        private String dimAttrName;

        /**
         * 表字段编码
         */
        @ApiModelProperty("表字段编码")
        private String dimAttrCode;

        /**
         * 关联类型1：左关联2：右关联
         */
        @ApiModelProperty("关联类型1：左关联2：右关联")
        private Integer unionType;

        /**
         * 关联台账表ID
         */
        @ApiModelProperty("关联台账表ID")
        private String unionRawTableId;

        /**
         * 关联维度/事实表ID
         */
        @ApiModelProperty("关联维度/事实表ID")
        private String unionDimId;

        /**
         * 关联表编码
         */
        @ApiModelProperty("关联表编码")
        private String unionDimCode;

        /**
         * 关联表名称
         */
        @ApiModelProperty("关联表名称")
        private String unionDimName;

        /**
         * 关联表字段ID
         */
        @ApiModelProperty("关联表字段ID")
        private String unionDimAttrId;

        /**
         * 关联表字段名称
         */
        @ApiModelProperty("关联表字段名称")
        private String unionDimAttrName;

        /**
         * 关联表字段编码
         */
        @ApiModelProperty("关联表字段编码")
        private String unionDimAttrCode;

        /**
         * 排序
         */
        private Long orderBy;

    }
}
