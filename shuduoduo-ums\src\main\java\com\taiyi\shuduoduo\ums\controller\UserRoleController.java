package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ums.entity.UserRole;
import com.taiyi.shuduoduo.ums.vo.RoleVo;
import com.taiyi.shuduoduo.ums.vo.UserRoleVo;
import com.taiyi.shuduoduo.ums.service.UserRoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户-角色
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/user-role")
@Validated
public class UserRoleController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private UserRoleService userRoleService;

    /**
     * 新增数据
     *
     * @param t t
     * @return bool
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> saveRoleBatch(@RequestBody @Validated UserRoleVo.InsertRoleBatchRequest t) {
        boolean f;
        String companyId = CurrentUserUtil.get().getCompanyId();
        // 设置企业ID
        t.getList().forEach(z -> z.setCompanyId(companyId));
        try {
            f = userRoleService.saveBatch(BeanUtil.copyList(t.getList(), UserRole.class));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 批量添加用户
     *
     * @param t t
     * @return ResponseEntity
     */
    @PostMapping("/saveAll")
    public ResponseEntity<ResponseVo.ResponseBean> saveUserBatch(@RequestBody @Validated UserRoleVo.InsertUserBatchRequest t) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, userRoleService.saveUserBatch(t));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 删除数据
     *
     * @param id 角色ID
     * @return bool
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = userRoleService.deleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 批量删除
     *
     * @param t t
     * @return ResponseEntity
     */
    @PostMapping("/delAll")
    public ResponseEntity<ResponseVo.ResponseBean> delAll(@RequestBody UserRoleVo.DeleteBatchRequest t) {
        boolean f;
        try {
            f = userRoleService.deleteBatchByIds(t.getIds());
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据用户id分页查询角色列表
     *
     * @param userid 用户id
     * @param param  分页参数
     * @return ResponseEntity
     */
    @PostMapping("/page-role/{userid}")
    public ResponseEntity<ResponseVo.ResponseBean> rolePage(@PathVariable("userid") String userid, @RequestBody(required = false) RoleVo.PageParam param) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, userRoleService.selectRolePage(userid, param));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据角色id分页查询用户列表
     *
     * @param roleid 角色id
     * @param param  分页参数
     * @return ResponseEntity
     */
    @PostMapping("/page-user/{roleid}")
    public ResponseEntity<ResponseVo.ResponseBean> userPage(@PathVariable("roleid") String roleid, @RequestBody(required = false) RoleVo.PageParam param) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, userRoleService.selectUserPage(roleid, param));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
