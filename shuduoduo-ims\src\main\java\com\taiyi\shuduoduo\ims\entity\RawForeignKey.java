package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw_foreign_key")
public class RawForeignKey extends CommonMySqlEntity {
    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 台账ID
     */
    private String rawId;

    /**
     * 台账表ID
     */
    private String rawTableId;

    /**
     * 维度/事实表ID
     */
    private String dimId;

    /**
     * 表编码
     */
    private String dimCode;

    /**
     * 表名称
     */
    private String dimName;

    /**
     * 表字段id
     */
    private String dimAttrId;

    /**
     * 表字段名称
     */
    private String dimAttrName;

    /**
     * 表字段编码
     */
    private String dimAttrCode;

    /**
     * 关联类型1：左关联2：右关联
     */
    private Integer unionType;

    /**
     * 关联台账表ID
     */
    private String unionRawTableId;

    /**
     * 关联维度/事实表ID
     */
    private String unionDimId;

    /**
     * 关联表编码
     */
    private String unionDimCode;

    /**
     * 关联表名称
     */
    private String unionDimName;

    /**
     * 关联表字段ID
     */
    private String unionDimAttrId;

    /**
     * 关联表字段名称
     */
    private String unionDimAttrName;

    /**
     * 关联表字段编码
     */
    private String unionDimAttrCode;

    private Long orderBy;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

}