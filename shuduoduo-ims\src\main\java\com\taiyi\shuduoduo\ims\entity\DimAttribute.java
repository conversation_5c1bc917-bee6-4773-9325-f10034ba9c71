package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 维度属性实体
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "ims_dimension_attr")
public class DimAttribute extends CommonMySqlEntity {

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 维度ID
     */
    private String dimId;

    /**
     * 指标表ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String dwsId;

    /**
     * 字段名称
     */
    private String name;

    /**
     * 字段编码
     */
    private String code;

    /**
     * 别名
     */
    private String aliasName;
    /**
     * 创建类型：1、新建 2、开通
     */
    private Integer type;

    /**
     * 字段类型 json
     */
    private String columnType;

    /**
     * 数据格式 json
     */
    private String dataFormat;

    /**
     * 字典值--周期值
     */
    private Integer dictValue;

    /**
     * 字典类型--周期
     */
    private String dictType;

    /**
     * 是否主键
     */
    private Boolean ifPrimaryKey;

    /**
     * 排序字段
     */
    private Long orderBy;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

    /**
     * 统计周期
     */
    private String periodValue;

    /**
     * 挂接类型
     */
    private String unionType;

    /**
     * 可用分析
     */
    private String analysis;

    /**
     * 关联的维度信息
     */
    private String dimension;


}