package com.taiyi.shuduoduo.ums.listener;

import com.taiyi.common.listener.NacosConfigRefreshListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

import java.util.Map;

/**
 * 任务开关监听器
 *
 * <AUTHOR>
 */
@Component
public class TaskRunningSwitchListener implements ApplicationListener<NacosConfigRefreshListener.NacosConfigUpdateEvent> {

    private static final Logger log = LoggerFactory.getLogger(TaskRunningSwitchListener.class);

    private volatile boolean isRunning = true;

    @Override
    public void onApplicationEvent(NacosConfigRefreshListener.NacosConfigUpdateEvent event) {
        Map<String, Object> yaml = new Yaml().load(event.getContent());
        Object taskMap = yaml.get("task");
        if (taskMap instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> task = (Map<String, Object>) taskMap;
            Object runningObj = task.get("running");
            if (runningObj instanceof Boolean) {
                isRunning = (Boolean) runningObj;
                log.info("[任务开关] 定时任务运行状态切换为: {}", isRunning);
            }
        }
    }

    public boolean isTaskRunning() {
        return isRunning;
    }
}
