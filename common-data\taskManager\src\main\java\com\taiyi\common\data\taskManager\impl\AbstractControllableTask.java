package com.taiyi.common.data.taskManager.impl;

import com.taiyi.common.data.taskManager.entity.TaskStatus;
import com.taiyi.common.data.taskManager.service.ControllableTask;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 任务控制实现
 *
 * <AUTHOR>
 */
public abstract class AbstractControllableTask implements ControllableTask {
    private final AtomicBoolean enabled = new AtomicBoolean(true);
    private final AtomicBoolean running = new AtomicBoolean(false);

    @Override
    public boolean isEnabled() {
        return enabled.get();
    }

    @Override
    public void stop() {
        enabled.set(false);
    }

    @Override
    public void start() {
        enabled.set(true);
    }

    @Override
    public TaskStatus getStatus() {
        if (running.get()) {
            return TaskStatus.RUNNING;
        }
        if (!enabled.get()) {
            return TaskStatus.STOPPED;
        }
        return TaskStatus.IDLE;
    }

    protected boolean beginRun() {
        return running.compareAndSet(false, true);
    }

    protected void endRun() {
        running.set(false);
    }

    protected boolean shouldStop() {
        return !enabled.get();
    }
}

