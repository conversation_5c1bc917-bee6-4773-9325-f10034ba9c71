package com.taiyi.shuduoduo.ims.nocodb.service;

import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class NocoDbApiServiceTest {


    @Autowired
    NocoDbApiService apiService;
    @Test
    void getTableData() {
        List<Map> list = apiService.getTableData("早见商城", "房开业务过程");
        list.forEach(System.out::println);
        System.out.println(list.size());
    }
}