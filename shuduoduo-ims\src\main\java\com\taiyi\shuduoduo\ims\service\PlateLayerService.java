package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.PlateLayerDao;
import com.taiyi.shuduoduo.ims.entity.PlateLayer;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PlateLayerService extends CommonMysqlService<PlateLayerDao, PlateLayer> {
    @Override
    public Class<PlateLayer> getEntityClass() {
        return PlateLayer.class;
    }

    public Long getMaxOrder(String plateId) {
        QueryWrapper<PlateLayer> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("plate_id", plateId).orderByDesc("order_by").last("LIMIT 1");
        PlateLayer plateLayer = super.getOne(wrapper);
        return null == plateLayer ? 1L : plateLayer.getOrderBy() + 1;
    }

    public List<PlateLayer> listByPlateId(String plateId) {
        QueryWrapper<PlateLayer> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("plate_id", plateId).orderByDesc("order_by");
        return super.list(wrapper);
    }

    /**
     * 根据板块ID 删除数据分层信息 物理删除
     *
     * @param plateId 板块ID
     * @return bool
     */
    public boolean logicDeleteByPlateId(String plateId) {
        List<PlateLayer> list = this.listByPlateId(plateId);
        if (list.isEmpty()) {
            return true;
        }
        for (PlateLayer plateLayer : list) {
            plateLayer.setIfDeleted(true);
        }
        return super.updateBatchById(list);
    }

    public List<PlateLayer> getListByPlateId(String plateId) {
        QueryWrapper<PlateLayer> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("plate_id", plateId).orderByDesc("order_by");
        return super.list(wrapper);
    }

    public PlateLayer getPlateLayerByPlateAndCode(String plateId, String code) {
        QueryWrapper<PlateLayer> wrapper = new QueryWrapper<>();
        wrapper.eq("plate_id", plateId).eq("if_deleted", 0).eq("code", code).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public boolean logicDeleteByIds(List<String> delPlateLayers) {
        if (delPlateLayers.isEmpty()) {
            return true;
        }
        boolean f = false;
        for (String s : delPlateLayers) {
            f = super.logicDeleteById(s);
        }
        return f;
    }
}