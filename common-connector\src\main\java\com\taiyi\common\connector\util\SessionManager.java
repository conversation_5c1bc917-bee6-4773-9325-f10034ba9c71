package com.taiyi.common.connector.util;

import io.netty.channel.Channel;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class SessionManager {


    /**
     * 客户端连接管道
     */
    private Map<String, Channel> channelMap;

    private static volatile SessionManager instance = null;

    public static SessionManager getInstance() {
        if (instance == null) {
            synchronized (SessionManager.class) {
                if (instance == null) {
                    instance = new SessionManager();
                }
            }
        }
        return instance;
    }

    public SessionManager() {
        this.channelMap = new ConcurrentHashMap<>();
    }

    public synchronized void setChannel(String key, Channel channel) {
        this.channelMap.put(key, channel);
    }

    public synchronized void removeChannel(String key) {
        this.channelMap.remove(key);
    }

    public synchronized Map<String, Channel> getChannelMap() {
        return this.channelMap;
    }
}
