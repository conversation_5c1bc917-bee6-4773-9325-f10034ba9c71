package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw_table")
public class RawTable extends CommonMySqlEntity {
    /**
     * 台账ID
     */
    private String rawId;

    /**
     * 关联的表ID
     */
    private String dimId;

    /**
     * 指标表ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String dwsId;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 板块ID
     */
    private String plateId;

    /**
     * 数据域ID
     */
    private String dataFieldId;

    /**
     * 业务过程ID
     */
    private String busProcessId;

    /**
     * 表名
     */
    private String name;

    /**
     * 表英文名
     */
    private String code;

    private Long orderBy;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

}