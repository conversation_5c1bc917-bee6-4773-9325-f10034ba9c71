package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

import java.util.Date;

/**
 * 维度、事实 表实体
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_dimension")
public class Dimension extends CommonMySqlEntity {

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 板块ID
     */
    private String plateId;

    /**
     * 数据域ID
     */
    private String dataFieldId;

    /**
     * 业务过程ID
     */
    private String busProcessId;

    /**
     * 类型：1、维度 2、事实表
     */
    private Integer dataType;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 数仓分层ID
     */
    private String plateLayerId;

    /**
     * 描述
     */
    private String description;

    /**
     * 类型 1、新建 2、开通
     */
    private Integer type;

    /**
     * 状态 1、未同步 2、已同步
     */
    private Integer status;


    /**
     * 排序字段
     */
    private Long orderBy;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

    /**
     * 表文档
     */
    private String doc;

    /**
     * dremio 数据集ID
     */
    private String datasetId;

    /**
     * 检查状态
     */
    private Integer checkStatus;

    /**
     * 检查描述
     */
    private String checkMessage;

    /**
     * 检查时间
     */
    private Date checkTime;

    /**
     * 智能洞察标记
     */
    private boolean  ifInsight = false;

    /**
     * 智能洞察ID
     */
    private String insightId;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 条线id
     */
    private String lineId;

    /**
     * 业务品质id
     */
    private String bvId;

    /**
     * 来源系统id
     */
    private String sourceId;
}