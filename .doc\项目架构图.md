![img.png](项目流程图.png)

## 架构说明
1. **用户层**：用户通过客户端发起请求。
2. **网关层**：`common-gateway` 接收所有请求，并根据请求类型进行路由分发。
3. **网关路由**：将请求分发到对应的业务模块。
4. **业务模块**：
   - `common-auth`：处理白名单控制。
   - `common-connector`：提供与数据湖的连接和查询功能。
   - `shuduoduo-ims`：处理数据规划、台账、指标等数据相关操作。
   - `shuduoduo-sms`：处理系统配置信息。
   - `shuduoduo-ums`：处理用户管理和权限控制。
   - `shuduoduo-nocodb`：处理数据权限校验。
5. **公共模块**：`common` 模块为各业务模块提供基础工具类和配置支持。
6. **数据层**：
   - `MySQL`：主数据库存储核心业务数据。
   - `Redis`：缓存热点数据，提高访问效率。
   - `SQLite`：轻量级数据库，用于数据权限校验。

此架构图清晰地展示了系统的分层结构以及各模块之间的依赖关系。