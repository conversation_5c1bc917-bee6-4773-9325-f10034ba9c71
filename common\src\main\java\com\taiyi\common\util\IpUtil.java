package com.taiyi.common.util;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public class IpUtil {


    /**
     * 判断IP是否是内网地址
     *
     * @param ipAddress ip地址
     * @return 是否是内网地址
     * @description 私有IP：A类  10.0.0.0-**************
     * B类  **********-**************
     * C类  ***********-***************
     * 还有127这个网段是环回地址
     */
    public static boolean isInnerIP(String ipAddress) {
        boolean isInnerIp;
        long ipNum = getIpNum(ipAddress);
        long aBegin = getIpNum("10.0.0.0");
        long aEnd = getIpNum("**************");

        long bBegin = getIpNum("**********");
        long bEnd = getIpNum("**************");

        long cBegin = getIpNum("***********");
        long cEnd = getIpNum("***************");
        isInnerIp = isInner(ipNum, aBegin, aEnd) || isInner(ipNum, bBegin, bEnd) || isInner(ipNum, cBegin, cEnd)
                || "127.0.0.1".equals(ipAddress);
        return isInnerIp;
    }

    /**
     * 获取网段尾IP
     *
     * @param ipAddress 网段起始IP
     * @return 尾IP
     */
    private static long getIpNum(String ipAddress) {
        String[] ip = ipAddress.split("\\.");
        long a = Integer.parseInt(ip[0]);
        long b = Integer.parseInt(ip[1]);
        long c = Integer.parseInt(ip[2]);
        long d = Integer.parseInt(ip[3]);

        return a * 256 * 256 * 256 + b * 256 * 256 + c * 256 + d;
    }

    /**
     * 比较ip是否位于网段内
     *
     * @param userIp 实际IP
     * @param begin  起始ip
     * @param end    尾ip
     * @return bool
     */
    private static boolean isInner(long userIp, long begin, long end) {
        return (userIp >= begin) && (userIp <= end);
    }

    /**
     * 获取真实IP
     *
     * @param request req
     * @return IP
     */
    public static String getRealIP(HttpServletRequest request) {
        // 获取客户端ip地址
        String clientIp = request.getHeader("x-forwarded-for");

        if (clientIp == null || clientIp.length() == 0 || "unknown".equalsIgnoreCase(clientIp)) {
            clientIp = request.getRemoteAddr();
        }

        String[] clientIps = clientIp.split(",");
        if (clientIps.length <= 1) {
            return clientIp.trim();
        }

        // 判断是否来自CDN
        if (isComefromCDN(request)) {
            if (clientIps.length >= 2) {
                return clientIps[clientIps.length - 2].trim();
            }
        }

        return clientIps[clientIps.length - 1].trim();
    }

    private static boolean isComefromCDN(HttpServletRequest request) {
        String host = request.getHeader("host");
        return host.contains("www.189.cn") || host.contains("shouji.189.cn") || host.contains(
                "image2.chinatelecom-ec.com") || host.contains(
                "image1.chinatelecom-ec.com");
    }

}
