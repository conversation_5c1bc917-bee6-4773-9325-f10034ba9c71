package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.TopicApi;
import com.taiyi.shuduoduo.ims.service.TopicApiLogService;
import com.taiyi.shuduoduo.ims.service.TopicApiService;
import com.taiyi.shuduoduo.ims.vo.TopicApiVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 数据服务
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/topic/api")
@Validated
public class TopicApiController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private TopicApiService topicApiService;

    @Autowired
    private TopicApiLogService topicApiLogService;

    /**
     * 新增 API
     *
     * @param t t
     * @return T
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated TopicApi t) {
        CurrentUserUtil.CurrentUser user = CurrentUserUtil.get();
        boolean f;
        try {
            t.setCompanyId(user.getCompanyId());
            t.setCreateBy(user.getId());
            f = topicApiService.save(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 获取API详情
     *
     * @param id API id
     * @return T
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            TopicApi t = topicApiService.getById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 统计API个数
     *
     * @return int
     */
    @GetMapping("/total")
    public ResponseEntity<ResponseVo.ResponseBean> getCount() {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, topicApiService.getApiCount());
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 删除API
     *
     * @param id API id
     * @return T
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = topicApiService.logicDeleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 分页查询API
     *
     * @param query 分页参数
     * @return T
     */
    @PostMapping("/page")
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody(required = false) TopicApiVo.PageVo query) {
        try {
            PageResult page = topicApiService.selectPage(query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 统计近{#int}天的调用次数
     *
     * @param days 天数
     * @return T
     */
    @GetMapping("/statistics")
    public ResponseEntity<ResponseVo.ResponseBean> getStatistics(@RequestParam(value = "days", defaultValue = "30") int days) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, topicApiLogService.getCountList(days));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

}
