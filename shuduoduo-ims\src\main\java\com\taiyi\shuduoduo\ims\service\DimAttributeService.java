package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.DimAttribute;
import com.taiyi.shuduoduo.ims.dao.DimAttributeDao;
import com.taiyi.shuduoduo.ims.entity.Dimension;
import com.taiyi.shuduoduo.ims.vo.DimAttributeVo;
import com.taiyi.shuduoduo.ims.vo.DwsVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 维度属性业务层
 *
 * <AUTHOR>
 */
@Service
public class DimAttributeService extends CommonMysqlService<DimAttributeDao, DimAttribute> {

    @Autowired
    private DimDwsService dimDwsService;

    @Autowired
    private DimensionService dimensionService;

    @Override
    public Class<DimAttribute> getEntityClass() {
        return DimAttribute.class;
    }

    /**
     * 新增维度属性
     *
     * @param addDimAttribute 维度属性vo类，带有维度id
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addDimAttribute(DimAttributeVo.AddDimAttribute addDimAttribute) {
        DimAttribute dimAttribute = BeanUtil.copy(addDimAttribute, DimAttribute.class);
        dimAttribute.setCompanyId(CurrentUserUtil.get().getCompanyId());
        if (StringUtils.isBlank(addDimAttribute.getId())) {
            return this.save(dimAttribute);
        } else {
            return this.updateById(dimAttribute.getId(), dimAttribute);
        }
    }

    /**
     * 通过维度id查询维度属性
     *
     * @param dimId 维度id
     * @return 维度属性集合
     */
    public List<DimAttribute> listByDimId(String dimId) {
        //通过维度id查询维度属性
        QueryWrapper<DimAttribute> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(dimId), "dim_id", dimId).eq("if_deleted", false).orderByAsc("order_by");
        return super.list(wrapper);
    }

    /**
     * 根据维度id查询维度属性列表
     *
     * @param dimId 维度id
     * @return 维度属性列表
     */
    public List<DimAttribute> getList(String dimId) {
        QueryWrapper<DimAttribute> wrapper = new QueryWrapper<>();
        wrapper.eq("dim_id", dimId)
                .eq("if_deleted", false).orderByAsc("order_by");
        return this.list(wrapper);
    }

    /**
     * 根据维度/事实表ID获取已关联的字段列表
     *
     * @param dimId 维度/事实表ID
     * @return list
     */
    public List<DimAttribute> getHasDwsList(String dimId) {
        QueryWrapper<DimAttribute> wrapper = new QueryWrapper<>();
        wrapper.eq("dim_id", dimId)
                .eq("if_deleted", false)
                .ne("dws_id", "")
                .orderByAsc("order_by");
        return this.list(wrapper);
    }

    /**
     * 维度属性列表查询--根据维度ID查询
     *
     * @param dimId 维度ID
     * @return list
     */
    public List<Map<String, Object>> getColumnList(String dimId) {
        List<Map<String, Object>> res = new ArrayList<>();
        List<DimAttribute> dimAttributeList = getList(dimId);
        for (DimAttribute dimAttribute : dimAttributeList) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", dimAttribute.getId());
            map.put("columnCode", dimAttribute.getCode());
            map.put("columnName", dimAttribute.getName());
            map.put("aliasName", dimAttribute.getAliasName());
            map.put("columnType", dimAttribute.getColumnType());
            map.put("orderBy", dimAttribute.getOrderBy());
            res.add(map);
        }
        return res;
    }

    /**
     * 可选指标列表可展示维度属性
     *
     * @param dimId 维度ID
     * @return 维度属性列表
     */
    public List<DimAttribute> getShowList(String dimId) {
        QueryWrapper<DimAttribute> wrapper = new QueryWrapper<>();
        wrapper.eq("dim_id", dimId)
                .isNotNull("name")
                .isNotNull("description")
                .eq("if_deleted", 0)
                .orderByAsc("order_by");
        return this.list(wrapper);
    }

    /**
     * 根据维度id删除所有维度表字段
     *
     * @param dimId 维度id
     * @return boolean
     */
    public boolean deletedByDimId(String dimId) {
        if (listByDimId(dimId).isEmpty()) {
            return true;
        }
        List<DimAttribute> dwsList = getHasDwsList(dimId);
        for (DimAttribute attribute : dwsList) {
            // 删除维度指标关联关系
            dimDwsService.deletedByDimIdAndAttrIdAndDwsId(dimId, attribute.getId(), attribute.getDwsId());
        }
        UpdateWrapper<DimAttribute> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", 1)
                .eq("dim_id", dimId)
                .eq("if_deleted", 0);
        return this.update(wrapper);
    }

    /**
     * 查询维度主键列表
     *
     * @param dimId 维度ID
     * @return T
     */
    public List<DimAttribute> getPrimaryList(String dimId) {
        QueryWrapper<DimAttribute> wrapper = new QueryWrapper<>();
        wrapper.eq("dim_id", dimId)
                .eq("if_deleted", false)
                .eq("if_primary_key", true).orderByAsc("order_by");
        return super.list(wrapper);
    }

    public List<DimAttribute> getListByDimId(String dimId) {
        QueryWrapper<DimAttribute> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("dim_id", dimId)
                .eq("if_deleted", false)
                .orderByAsc("order_by");
        return super.list(wrapper);
    }

    /**
     * 根据指标修改维度关联指标信息
     *
     * @param dwsId     指标ID
     * @param unionList 关联列表
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateByDwsIdAndUnionAttrIds(String companyId, String dwsId, List<DwsVo.DimensionUnion> unionList) {
        boolean f = false;
        for (DwsVo.DimensionUnion union : unionList) {
            if (null != super.getById(union.getDimAttrId())) {
                UpdateWrapper<DimAttribute> wrapper = new UpdateWrapper<>();
                wrapper.set("dws_id", dwsId)
                        .set("union_type", union.getUnionType())
                        .set("period_value", union.getPeriodValue())
                        .set("dimension", union.getDimension())
                        .set("analysis", union.getAnalysis())
                        .eq("id", union.getDimAttrId());
                f = super.update(wrapper);
            }
        }
        if (f) {
            // 通知维度表关联的台账--有事实表字段关联指标字段(设置台账的关联指标)
            dimensionService.callRawUpdate(companyId, dwsId, null, unionList);
        }
        return f;
    }

    /**
     * 指标关联关系变更，更新维度字段关联
     *
     * @param dwsId 指标ID
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deletedByDwsId(String companyId, String dwsId) {
        boolean f = false;
        QueryWrapper<DimAttribute> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId);
        List<DimAttribute> dimAttributeList = super.list(wrapper);
        for (DimAttribute dimAttribute : dimAttributeList) {
            LambdaUpdateWrapper<DimAttribute> wrapper1 = new LambdaUpdateWrapper<>();
            wrapper1.set(DimAttribute::getDwsId, null).eq(DimAttribute::getId, dimAttribute.getId());
            f = update(wrapper1);
            if (f) {
                // 通知维度表关联的台账--有事实表字段关联指标已被删除(设置台账的关联指标为空)
                dimensionService.callRawUpdate(companyId, null, dimAttribute.getDimId(), null);
            }
        }
        return f;
    }

    public DimAttribute getByDimIdAndCode(String dimId, String code) {
        QueryWrapper<DimAttribute> wrapper = new QueryWrapper<>();
        wrapper.eq("dim_id", dimId)
                .eq("if_deleted", false)
                .eq("code", code).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 获取所有字段带筛选条件
     *
     * @param keyWord 关键字搜索
     * @return list
     */
    public List<DimAttribute> getAllAttrs(String keyWord) {
        QueryWrapper<DimAttribute> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false)
                .eq(StringUtils.isNotBlank(keyWord), "name", keyWord)
                .orderByAsc("order_by");
        return super.list(wrapper);
    }

    public boolean updateDwsId(String id) {
        UpdateWrapper<DimAttribute> wrapper = new UpdateWrapper<>();
        wrapper.set("dws_id", null).eq("id", id);
        return super.update(wrapper);
    }

    /**
     * 根据名称获取字段信息
     *
     * @param nameList 字段名称列表
     * @return
     */
    public List<Map> queryCodeByNames(List<String> nameList) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        List<Map> res = new ArrayList<>();
        for (String s : nameList) {
            Map<String, Object> map = new HashMap<>(2);
            map.put("name", s);
            map.put("list", getInfoByName(companyId, s));
            res.add(map);
        }
        return res;
    }

    /**
     * 根据名称获取字段信息
     *
     * @param companyId 企业ID
     * @param name      字段名称
     * @return
     */
    public List<DimAttribute> getInfoByName(String companyId, String name) {
        QueryWrapper<DimAttribute> wrapper = new QueryWrapper<>();
        wrapper.select("id", "name", "code").eq("company_id", companyId).eq("name", name).eq("if_deleted", false);
        return super.list(wrapper);
    }

    /**
     * 批量删除维度下字段
     *
     * @param delAttrList 字段列表
     * @return bool
     */
    public boolean logicDeleteByIds(List<String> delAttrList) {
        if (null == delAttrList || delAttrList.isEmpty()) {
            return true;
        }
        for (String attrId : delAttrList) {
            DimAttribute attribute = super.getById(attrId);
            if (null == attribute) {
                return false;
            }
            // 删除维度指标关联关系
            dimDwsService.deletedByDimIdAndAttrIdAndDwsId(attribute.getDimId(), attribute.getId(), attribute.getDwsId());
        }
        UpdateWrapper<DimAttribute> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", 1)
                .in("id", delAttrList)
                .eq("if_deleted", 0);
        return this.update(wrapper);
    }
}