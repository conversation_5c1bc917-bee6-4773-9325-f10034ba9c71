spring:
  cloud:
    nacos:
      discovery:
        server-addr: *************:18848,*************:18849
      config:
        server-addr: *************:18848,*************:18849
      username: nacos
      password: shuduoduo2023!

  datasource:
    dynamic:
      primary: mysql
      strict: true
      hikari:
        max-pool-size: 5
        min-idle: 5
        connection-test-query: SELECT 1
        max-lifetime: 60000
        idle-timeout: 60000
      datasource:
        mysql:
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: root
          password: "!QW@3er4"
          url: ************************************************************************************************************************************************************
        sqlite:
          driver-class-name: org.sqlite.JDBC
          url: jdbc:sqlite:/home/<USER>/docker/nocodb/noco.db?date_string_format=yyyy-MM-dd HH:mm:ss
          username:
          password:


  redis:
    host: *************
    port: 16379
    password: LsGuoChao2023!
    timeout: 3000ms
    jedis:
      pool:
        max-idle: 500
        min-idle: 50
        max-active: 2000
        max-wait: 1000ms

lishang:
  host: https://lqt-uat.lsguochao.com
  clientId: 0097a9cfe03e30a2a0781bdadd4c5a53
  username: lsy_uat
  password: lsy_uat


task:
  running: true