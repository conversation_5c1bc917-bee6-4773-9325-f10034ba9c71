package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.PlateSourceDao;
import com.taiyi.shuduoduo.ims.entity.PlateSource;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class PlateSourceService extends CommonMysqlService<PlateSourceDao, PlateSource> {
    @Override
    public Class<PlateSource> getEntityClass() {
        return PlateSource.class;
    }

    /**
     * 获取板块下来源系统列表
     *
     * @param plateId 板块ID
     * @return List
     */
    public List<PlateSource> getList(String plateId,String companyId) {
        QueryWrapper<PlateSource> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("if_deleted", false)
                .eq(StrUtil.isNotBlank(plateId), "plate_id", plateId)
                .orderByAsc("order_by");
        return super.list(wrapper);
    }

    @Override
    public boolean isNameDuplicate(PlateSource t, String name) {
        QueryWrapper<PlateSource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("if_deleted", 0);
        if (t.id != null) {
            queryWrapper.ne("id", t.id);
        }
        queryWrapper.eq("source_name", name);
        queryWrapper.last("LIMIT 1");
        PlateSource one = super.getOne(queryWrapper);
        return one != null;
    }

    public PlateSource getByPlateIdAndName(String plateId, String name) {
        QueryWrapper<PlateSource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("if_deleted", 0).eq("plate_id", plateId)
                .eq("source_name", name)
                .last("LIMIT 1");
        return super.getOne(queryWrapper);
    }

    public List<PlateSource> getListByIds(String plateSourceIds, String companyId) {
        // 检查输入参数
        if (plateSourceIds == null || plateSourceIds.trim().isEmpty()) {
            logger.warn("dimIds is null or empty");
            return super.list(new QueryWrapper<PlateSource>().eq("company_id", companyId).eq("if_deleted", false));
        }
        // 处理 dimIds 并去除空字符串
        List<String> idList = Arrays.stream(plateSourceIds.split(","))
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .collect(Collectors.toList());

        if (idList.isEmpty()) {
            logger.warn("No valid ids found in dimIds");
            return super.list(new QueryWrapper<PlateSource>().eq("company_id", companyId).eq("if_deleted", false));
        }

        // 构建查询条件
        QueryWrapper<PlateSource> wrapper = new QueryWrapper<>();
        wrapper.in("id", idList).eq("company_id", companyId).eq("if_deleted", false);

        return super.list(wrapper);
    }
}