spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:44430
      config:
        server-addr: 127.0.0.1:44430
      username: nacos
      password: nacos

  datasource:
    dynamic:
      primary: mysql
      strict: false
      datasource:
        mysql:
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: shuduoduo
          password: <PERSON><PERSON>(xAyypihAUwpGj2q8juM2w2IcpZebw6SF)
          url: ************************************************************************************************************************************************************************
          hikari:
            maximum-pool-size: 50
            minimum-idle: 10
            connection-test-query: SELECT 1
            max-lifetime: 60000
            idle-timeout: 60000
        sqlite:
          driver-class-name: org.sqlite.JDBC
          url: jdbc:sqlite:C:\Users\<USER>\IdeaProjects\shuduoduo-v2\.doc\docker\nocodb\noco.db?date_string_format=yyyy-MM-dd HH:mm:ss
          username:
          password: