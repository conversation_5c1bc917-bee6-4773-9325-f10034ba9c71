package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.RawAuthDao;
import com.taiyi.shuduoduo.ims.entity.RawAuth;
import com.taiyi.shuduoduo.ims.vo.RawAuthVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 台账权限业务层
 *
 * <AUTHOR>
 */
@Service
public class RawAuthService extends CommonMysqlService<RawAuthDao, RawAuth> {
    @Override
    public Class<RawAuth> getEntityClass() {
        return RawAuth.class;
    }

    @Autowired
    private RawAuthDao rawAuthDao;

    /**
     * 检查是否存在
     *
     * @param rawId       台账ID
     * @param authTableId 权限表ID
     * @return bool
     */
    public boolean isExist(String rawId, String authTableId) {
        QueryWrapper<RawAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("raw_id", rawId).eq("auth_table_id", authTableId).last("LIMIT 1");
        return null == super.getOne(wrapper);
    }

    /**
     * 分页查询
     *
     * @param pageVo 分页参数
     * @return PageResult
     */
    public PageResult<RawAuthVo.PageResult> selectPage(RawAuthVo.PageVo pageVo) {
        Page<RawAuthVo.PageResult> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        QueryWrapper<RawAuthVo.PageResult> wrapper = new QueryWrapper<>();
        wrapper.eq("a.raw_id", pageVo.getRawId()).eq("a.if_deleted", false).orderByDesc("a.status", "a.auth_table_id");
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.like("b.auth_table", pageVo.getKeyWord());
        }
        IPage<RawAuthVo.PageResult> iPage = rawAuthDao.getPage(page, wrapper);
        PageResult<RawAuthVo.PageResult> result = new PageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setTotal(iPage.getTotal());
        result.setList(iPage.getRecords());
        return result;
    }

    /**
     * 根据权限设置更新状态
     *
     * @param rawAuthId 台账权限ID
     * @return bool
     */
    public boolean updateBySetting(String rawAuthId, Boolean status, String operator) {
        RawAuth rawAuth = super.getById(rawAuthId);
        rawAuth.setStatus(status);
        rawAuth.setOperator(operator);
        return super.updateById(rawAuthId, rawAuth);
    }

    /**
     * 根据台账ID查询已开启权限的权限集
     *
     * @param rawId 台账ID
     * @return 权限集
     */
    public List<RawAuth> getListByRawId(String rawId) {
        QueryWrapper<RawAuth> wrapper = new QueryWrapper<>();
        wrapper.eq("raw_id", rawId).eq("if_deleted", false).eq("status", true);
        return super.list(wrapper);
    }
}