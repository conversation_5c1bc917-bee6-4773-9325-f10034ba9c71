package com.taiyi.shuduoduo.ims.entity;

/**
 * 认证常量
 *
 * <AUTHOR>
 */
public enum Certification {
    DOUBLE_CERTIFICATION(1, "双重认证"),
    CERTIFICATION_DEFINITION(2, "认证定义"),
    CERTIFICATION_DATA(3, "认证数据"),
    UNCERTIFIED(4, "未认证");

    private int value;
    private String description;

    Certification(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}

