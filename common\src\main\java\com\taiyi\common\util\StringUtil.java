package com.taiyi.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 */
public class StringUtil {

    /**
     * 数组转字符串
     *
     * @param list      数组
     * @param separator 分割符
     * @return 字符串
     */
    public static String listToString(List<?> list, String separator) {
        return StringUtils.join(list.toArray(), separator);
    }

}
