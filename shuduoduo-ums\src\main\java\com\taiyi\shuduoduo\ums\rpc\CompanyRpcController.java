package com.taiyi.shuduoduo.ums.rpc;

import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDTO;
import com.taiyi.shuduoduo.ums.service.CompanyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公司RPC
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/rpc/company")
public class CompanyRpcController {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private CompanyService companyService;

    @GetMapping("/{id}")
    public CompanyDTO getCompanyById(@PathVariable String id) {
        return BeanUtil.copy(companyService.getById(id), CompanyDTO.class);
    }

    @GetMapping("/list")
    public List<CompanyDTO> getCompanyList() {
        return BeanUtil.copyList(companyService.getList(), CompanyDTO.class);
    }

    @GetMapping("/depts")
    public List<String> getDeptList(@RequestParam("companyId") String companyId, @RequestParam("userId") String userId) {
        return companyService.getDeptList(companyId, userId);
    }

    @GetMapping("/dept/users")
    public List<String> getUserListByDeptId(@RequestParam("companyId") String companyId, @RequestParam("deptId") String deptId) {
        return companyService.getUserListByDeptId(companyId, deptId);
    }
}
