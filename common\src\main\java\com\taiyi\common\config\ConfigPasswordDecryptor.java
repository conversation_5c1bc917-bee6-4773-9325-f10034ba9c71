package com.taiyi.common.config;

import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.stereotype.Component;

/**
 * 配置文件密码解密
 *
 * <AUTHOR>
 */
@Component
public class ConfigPasswordDecryptor implements EnvironmentPostProcessor {

    private static final String PREFIX = "ENC(";
    private static final String PROPERTY_JASYPT_PASSWORD = "jasypt.encryptor.password";
    private static final String PROPERTY_JASYPT_ALGORITHM = "jasypt.encryptor.algorithm";
    private static final String DEFAULT_ALGORITHM = "PBEWithMD5AndDES";

    /**
     * 需要解密的属性列表
     */
    private static final String[] ENCRYPTED_PROPERTIES = {
            "spring.cloud.nacos.password",
            "spring.cloud.nacos.password",
            "spring.datasource.dynamic.datasource.mysql.password",
            "spring.redis.password"
    };

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // 获取 Jasypt 解密配置
        String decryptKey = environment.getProperty(PROPERTY_JASYPT_PASSWORD);
        String algorithm = environment.getProperty(PROPERTY_JASYPT_ALGORITHM, DEFAULT_ALGORITHM);

        if (decryptKey == null || decryptKey.isEmpty()) {
            throw new IllegalArgumentException("Jasypt decryption key is missing.");
        }

        // 初始化加密器
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setPassword(decryptKey);
        encryptor.setAlgorithm(algorithm);

        // 遍历并解密配置项
        for (String propertyName : ENCRYPTED_PROPERTIES) {
            String encryptedValue = environment.getProperty(propertyName);
            if (encryptedValue != null && encryptedValue.startsWith(PREFIX)) {
                try {
                    String decryptedValue = encryptor.decrypt(extractCipherText(encryptedValue));
                    System.setProperty(propertyName, decryptedValue);
                } catch (Exception e) {
                    throw new RuntimeException("Failed to decrypt property: " + propertyName, e);
                }
            }
        }
    }

    /**
     * 提取密文
     *
     * @param encryptedValue 密文
     * @return 密文
     */
    private String extractCipherText(String encryptedValue) {
        return encryptedValue.substring(PREFIX.length(), encryptedValue.length() - 1);
    }
}

