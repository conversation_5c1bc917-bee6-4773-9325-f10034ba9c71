package com.taiyi.common.data.mysql.service;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.entity.Query;
import com.taiyi.common.util.ClassUtil;
import com.taiyi.common.util.CurrentUserUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 公共数据库操作SERVICE
 *
 * <AUTHOR>
 */
public abstract class CommonMysqlService<M extends CommonMysqlMapper<T>, T extends CommonMySqlEntity>
        extends ServiceImpl<M, T> implements CommonService<T> {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    private final String IS_DELETED = "ifDeleted";
    private final String IS_DELETED_DB = "if_deleted";
    private final String NAME = "name";
    private final String ID = "id";
    private final String IS_COMPANY = "companyId";
    private final String IS_COMPANY_DB = "company_id";

    @Override
    public boolean save(T t) {
        if (null == t.getId()) {
            t.setId(IdUtil.simpleUUID());
        }
        Date now = new Date();
        t.setCreateTime(now);
        t.setUpdateTime(now);
        t.setVersion(1L);
        return super.save(t);
    }

    public void saveOrThrow(T t) throws Exception {
        if (!save(t)) {
            throw new Exception("Save to db failed: " + t.toString());
        }
    }

    @Override
    public boolean saveBatch(Collection<T> entityList) {
        return saveBatch(entityList, entityList.size());
    }

    @Override
    public boolean saveBatch(Collection<T> entityList, int batchSize) {
        Date now = new Date();
        for (T t : entityList) {
            if (t.getId() == null) {
                t.setId(IdUtil.simpleUUID());
            }
            if (t.getCreateTime() == null) {
                t.setCreateTime(now);
            }
            if (t.getUpdateTime() == null) {
                t.setUpdateTime(now);
            }
            t.setVersion(1L);
        }
        return super.saveBatch(entityList, batchSize);
    }

    @Override
    public T getOne(Wrapper<T> queryWrapper) {
        List<T> list = this.list(queryWrapper);
        return list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public long countAll() {
        if (ClassUtil.containsField(getEntityClass(), IS_DELETED)) {
            QueryWrapper<T> queryWrapper = new QueryWrapper<>();

            queryWrapper.eq(IS_DELETED_DB, 0);
            return super.count(queryWrapper);
        } else {
            return super.count();
        }
    }

    @Override
    public long countAllLogicDeleted() {
        if (ClassUtil.containsField(getEntityClass(), IS_DELETED)) {
            QueryWrapper<T> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(IS_DELETED_DB, 1);
            return super.count(queryWrapper);
        } else {
            return 0;
        }

    }

    @Override
    public long count(T t) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(t);
        if (ClassUtil.containsField(getEntityClass(), IS_DELETED)) {
            queryWrapper.eq(IS_DELETED_DB, 0);
        }
        return count(queryWrapper);
    }

    /**
     * 通过id查找
     *
     * @param id
     * @return
     */
    @Override
    public T getById(String id) {
        return super.getById(id);
    }

    /**
     * 校验名称是否在数据库中重复
     *
     * @return
     */
    public boolean isNameDuplicate(T t, String name) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        if (ClassUtil.containsField(getEntityClass(), IS_DELETED)) {
            queryWrapper.eq(IS_DELETED_DB, 0);
        }

        if (ClassUtil.containsField(getEntityClass(), IS_COMPANY)) {
            queryWrapper.eq(IS_COMPANY_DB, CurrentUserUtil.get().getCompanyId());
        }

        if (t.id != null) {
            queryWrapper.ne("id", t.id);
        }

        queryWrapper.eq(NAME, name);

        queryWrapper.last("LIMIT 1");

        T one = super.getOne(queryWrapper);
        return one != null;
    }

    public boolean sequence(String id, Integer orderBy, Class<T> toClass) {
        UpdateWrapper<T> wrapper = new UpdateWrapper<>();
        wrapper.set("order_by", orderBy).eq("id", id);
        return super.update(wrapper);
    }

    @Override
    public boolean deleteById(String id) {
        return super.removeById(id);
    }

    @Override
    public boolean logicDeleteById(String id) {
        return logicDeleteById(id, true);
    }

    @Override
    public boolean unLogicDeleteById(String id) {
        return logicDeleteById(id, false);
    }

    private boolean logicDeleteById(String id, boolean delete) {
        if (!ClassUtil.containsField(getEntityClass(), IS_DELETED)) {
            return true;
        }

        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("id", id);
        updateWrapper.set(IS_DELETED_DB, delete ? 1 : 0);
        return super.update(updateWrapper);
    }

    @Override
    public boolean updateWithNull(String id, T t) {
        UpdateWrapper<T> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        Field[] fields = ClassUtil.getField(getEntityClass());
        List<String> notEmptyList = Arrays.asList("id", IS_DELETED);
        for (Field field : fields) {
            if (notEmptyList.contains(field.getName())) {
                continue;
            }
            String name = field.getName();
            if ("id".equals(name)) {
                continue;
            }
            field.setAccessible(true);
            try {
                updateWrapper.set(StrUtil.toUnderlineCase(name), field.get(t));
            } catch (Exception e) {
                logger.warn(ExceptionUtil.stacktraceToString(e));
            }
        }
        updateWrapper.set("id", id);
        updateWrapper.set(IS_DELETED_DB, 0);
        return update(updateWrapper);
    }


    @Override
    public boolean updateById(String id, T t) {
        t.setId(id);
        //设置更新时间
        t.setUpdateTime(new Date());
        return super.updateById(t);
    }

    @Override
    public boolean updateWithVersion(String id, T t, Long version) {
        if (version == null || t == null) {
            return false;
        }
        t.setVersion(version + 1);
        t.setUpdateTime(new Date());

        UpdateWrapper<T> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);
        updateWrapper.eq("version", version);

        return super.baseMapper.update(t, updateWrapper) > 0;
    }

    @Override
    public PageResult page(Query query) {

        Page iPage = query.getPage();
        Page<T> page = super.baseMapper.selectPage(iPage, query.getQueryWrapper());

        List<T> list = page.getRecords();
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setPageNo(page.getCurrent());
        pageResult.setTotal(page.getTotal());
        pageResult.setPageSize(iPage.getSize());
        pageResult.setList(list);

        return pageResult;
    }


    @Override
    public boolean exists(T t) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntity(t);
        if (ClassUtil.containsField(getEntityClass(), IS_DELETED)) {
            queryWrapper.eq(IS_DELETED_DB, 0);
        }

        queryWrapper.last("LIMIT 1");

        T one = super.getOne(queryWrapper);
        return one != null;
    }

    @Override
    public boolean exists(String id, T t) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("id", id);
        queryWrapper.setEntity(t);
        if (ClassUtil.containsField(getEntityClass(), IS_DELETED)) {
            queryWrapper.eq(IS_DELETED_DB, 0);
        }
        queryWrapper.last("LIMIT 1");

        T one = super.getOne(queryWrapper);
        return one != null;
    }

    public PageResult<T> getPageResult(Page iPage, IPage<T> pages) {
        List<T> list = pages.getRecords();
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setPageNo(iPage.getCurrent());
        pageResult.setTotal(pages.getTotal());
        pageResult.setPageSize(iPage.getSize());
        pageResult.setList(list);
        return pageResult;
    }

    public PageResult<T> getPageResult(Page iPage, IPage pages, List resultList) {
        PageResult<T> pageResult = new PageResult<>();
        if (null == resultList) {
            List<T> list = pages.getRecords();
            pageResult.setList(list);
        } else {
            pageResult.setList(resultList);
        }
        pageResult.setPageNo(iPage.getCurrent());
        pageResult.setTotal(pages.getTotal());
        pageResult.setPageSize(iPage.getSize());
        return pageResult;
    }
//    /**
//     * 关联表保存数据
//     *
//     * @param relationId 关联的id，唯一
//     * @param ids        关联到的表的idList
//     * @return 布尔值
//     */
//    public boolean relationSave(String relationId, List<String> ids,) {
//        //通过id查询所有存在的维度
//        QueryWrapper<T> wrapper = new QueryWrapper<>();
//        wrapper.eq("if_deleted", 0);
//        wrapper.in("id", ids);
//        List<Dimension> dimensions = dimensionService.list(wrapper);
//        T t = ;
//
//        List<MetricDim> metricDims = new ArrayList<>();
//        for (Dimension dimension : dimensions) {
//            MetricDim metricDim = new MetricDim();
//            metricDim.setMetricId(uuid);
//            metricDim.setDimId(dimension.getId());
//            metricDims.add(metricDim);
//        }
//        boolean res1 = metricDimService.saveBatch(metricDims);
//    }
}
