server:
  port: 8890
  servlet:
    context-path: /shuduoduo/nocodb
  compression:
    enabled: true
    mime-types: application/json,text/html,text/plain,application/javascript,text/css,text/javascript

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: shuduoduo-nocodb
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  cloud:
    nacos:
      discovery:
        metadata:
          management:
            context-path: /shuduoduo/nocodb/actuator
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 50MB

  # 选择开发环境或者生产环境
  profiles:
    active: local

feign:
  sentinel:
    enabled: true #打开sentinel对feign的支持

# 设置RPC的默认超时时间 30S
ribbon:
  ReadTimeout: 30000
  ConnectTimeout: 30000

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always

logging:
  config: classpath:logback-spring.xml