package com.taiyi.shuduoduo.sms.service;

import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.sms.dao.DbTypeDao;
import com.taiyi.shuduoduo.sms.entity.DbType;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class DbTypeService extends CommonMysqlService<DbTypeDao, DbType> {
    @Override
    public Class<DbType> getEntityClass() {
        return DbType.class;
    }

    private static final String DB_TYPE_SQL_FILE = "db_type.sql";


    /**
     * 初始化数据
     *
     * @throws Exception e
     */
    public void initData() throws Exception {
        ClassPathResource resource = new ClassPathResource(DB_TYPE_SQL_FILE);
        String sql = IOUtils.toString(resource.getInputStream());
        baseMapper.initData(sql);
    }
}
