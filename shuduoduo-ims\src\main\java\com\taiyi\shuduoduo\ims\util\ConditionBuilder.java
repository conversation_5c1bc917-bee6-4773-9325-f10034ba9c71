package com.taiyi.shuduoduo.ims.util;

import cn.hutool.core.clone.CloneSupport;
import cn.hutool.db.sql.Condition;

import java.util.Arrays;
import java.util.List;

public class ConditionBuilder extends CloneSupport<Condition> {
    /**
     * SQL中 LIKE 语句查询方式<br>
     *
     * <AUTHOR>
     */
    public enum LikeType {
        /**
         * 以给定值开头，拼接后的SQL "value%"
         */
        StartWith,
        /**
         * 以给定值开头，拼接后的SQL "%value"
         */
        EndWith,
        /**
         * 包含给定值，拼接后的SQL "%value%"
         */
        Contains
    }

    private static final String OPERATOR_LIKE = "LIKE";
    private static final String OPERATOR_IN = "IN";
    private static final String OPERATOR_IS = "IS";
    private static final String OPERATOR_IS_NOT = "IS NOT";
    private static final String OPERATOR_BETWEEN = "BETWEEN";
    private static final List<String> OPERATORS = Arrays.asList("<>", "<=", "<", ">=", ">", "=", "!=", OPERATOR_IN);

    private static final String VALUE_NULL = "NULL";

    /**
     * 字段
     */
    private String field;
    /**
     * 运算符（大于号，小于号，等于号 like 等）
     */
    private String operator;
    /**
     * 值
     */
    private Object value;
    /**
     * 是否使用条件值占位符
     */
    private boolean isPlaceHolder = true;
    /**
     * between firstValue and secondValue
     */
    private Object secondValue;
}
