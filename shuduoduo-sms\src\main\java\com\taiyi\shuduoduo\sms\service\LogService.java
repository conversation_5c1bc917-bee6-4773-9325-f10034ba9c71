package com.taiyi.shuduoduo.sms.service;

import cn.hutool.core.date.DateUtil;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.DateFormatUtil;
import com.taiyi.shuduoduo.sms.dao.LogDao;
import com.taiyi.shuduoduo.sms.entity.Log;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
public class LogService extends CommonMysqlService<LogDao, Log> {
    @Override
    public Class<Log> getEntityClass() {
        return Log.class;
    }

    @Value("${log.file.path}")
    String logPath;

    @Value("${log.file.name}")
    String name;

    public void saveToFile(Log log, String host) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        File folder = new File(logPath + File.separator + companyId);
        if (!folder.exists()) {
            folder.mkdirs();
        }
        File file = new File(folder.getPath() + File.separator + name);
        try {
            if (!file.exists()) {
                file.createNewFile();
            }
            FileWriter fileWriter = new FileWriter(file, true);
            BufferedWriter bufferedWriter = new BufferedWriter(fileWriter);
            bufferedWriter.newLine(); // 添加换行符
            bufferedWriter.write(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss") + "  "
                    + log.getDoAction() + "  "
                    + log.getPath() + "  "
                    + log.getParameters() + "  "
                    + log.getActionDescription() + "  "
                    + host + "  "
                    + CurrentUserUtil.get().getRealName() + "(" + CurrentUserUtil.get().getThirdUserId() + ")"
            );
            bufferedWriter.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}