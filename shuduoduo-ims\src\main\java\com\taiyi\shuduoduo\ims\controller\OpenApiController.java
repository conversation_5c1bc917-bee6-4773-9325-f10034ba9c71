package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.service.DwsService;
import com.taiyi.shuduoduo.ims.vo.DwsVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 开发API接口
 *
 * <AUTHOR>
 * @Description: 提供开发的 restful风格的WEB服务
 *
 */
@RestController
@RequestMapping("/openapi")
public class OpenApiController {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DwsService dwsService;

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping("/dws/detail/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getDwsDetailById(@PathVariable("id") String id) {
        try {
            DwsVo.OpenAPIResponseDetailVo t = dwsService.getOpenApiDetailById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }
}
