package com.taiyi.shuduoduo.sms.controller;

import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据源类型
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dataSource")
public class DataSourceController {

    @GetMapping("/typeList")
    public ResponseEntity<ResponseVo.ResponseBean> dataSourceTypeList() {
        List<Map<String, String>> typeList = new ArrayList<>();
        String[] nameList = {
                "mysql",
                "h2",
                "postgresql",
                "hive",
                "snowflow",
                "其他"
        };

        for (int i = 0; i < nameList.length; i++) {
            Map<String, String> map = new HashMap<>();
            map.put("id", String.valueOf(i + 1));
            map.put("name", nameList[i]);
            typeList.add(map);
        }

        return ResponseVo.response(MessageCode.SUCCESS, typeList);
    }
}
