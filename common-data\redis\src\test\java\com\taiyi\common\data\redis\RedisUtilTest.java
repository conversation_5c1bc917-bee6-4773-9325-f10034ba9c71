package com.taiyi.common.data.redis;

import com.taiyi.common.data.redis.util.RedisRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;


import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
 class RedisUtilTest {
    @Autowired
    private RedisRepository redisRepository;


    @Test
    public void test() throws InterruptedException {
        //redis存储数据
        redisRepository.set("lys","nb");
        Assertions.assertEquals(redisRepository.get("lys"), "nb");
        //redis存储数据并设置超时时间
        redisRepository.setExpire("1779170601","1231",5);//设置验证码存储时间5
        Assertions.assertEquals(redisRepository.get("1779170601"), "1231");
        TimeUnit.SECONDS.sleep(5);//秒
        Assertions.assertNull(redisRepository.get("1779170601"));
        //删除数据
        redisRepository.del("lys");
        Assertions.assertNull(redisRepository.get("lys"));
        Map map=new HashMap();
        map.put("lys","1");
        map.put("good","2");
        map.put("hello","3");
        redisRepository.set("map",map);
        //存储集合类型数据
        Map res= (Map) redisRepository.get("map");
        Assertions.assertEquals(res.size(),3);
        redisRepository.del("map");
        Assertions.assertNull(redisRepository.get("map"));

    }

}