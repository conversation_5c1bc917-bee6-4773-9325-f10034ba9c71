package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.TopicApiLogDao;
import com.taiyi.shuduoduo.ims.entity.TopicApiLog;
import com.taiyi.shuduoduo.ims.vo.TopicApiLogVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
public class TopicApiLogService extends CommonMysqlService<TopicApiLogDao, TopicApiLog> {
    @Override
    public Class<TopicApiLog> getEntityClass() {
        return TopicApiLog.class;
    }

    @Autowired
    private TopicApiLogDao topicApiLogDao;

    public int getCount(String apiId) {
        QueryWrapper<TopicApiLog> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(apiId), "api_id", apiId);
        return super.count(wrapper);
    }

    /**
     * 统计近{#int}天的调用次数，返回调用次数列表
     */
    public List<TopicApiLogVo.StatisticsVo> getCountList(int days) {
        return topicApiLogDao.getDailyApiCallCount(days);
    }

}