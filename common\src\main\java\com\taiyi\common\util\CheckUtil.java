package com.taiyi.common.util;

import java.util.regex.Pattern;

/**
 * 校验
 *
 * <AUTHOR>
 */
public class CheckUtil {

    /**
     * 检测18位身份证号是否合法
     *
     * @param idCard 身份证号
     * @return true：身份证号合法，反之则反
     */
    public static boolean isIdCard(String idCard) {
        return idCard.matches("^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    }

    /**
     * 检测手机号是否合法
     *
     * @param mobile 手机号
     * @return true：手机号合法，反之则反
     */
    public static boolean isMobile(String mobile) {
        return Pattern.matches("^1(3|4|5|6|7|8|9)\\d{9}$", mobile);
    }

    /**
     * 检测电话号是否合法(可以是手机号，也可是座机号)
     *
     * @param telephoneNo 电话号
     * @return true：手机号合法，反之则反
     */
    public static boolean isTelephoneNo(String telephoneNo) {
        return Pattern.matches("^((0\\d{2,3}-\\d{7,8})|(1(3|4|5|6|7|8|9)\\d{9}))$", telephoneNo);
    }

    /**
     * 校验银行卡号是否正确
     *
     * @param cardNo 银行卡号
     * @return 合法返回true, 反之则反
     */
    public static boolean isCardNo(String cardNo) {
        //char bit = getBankCardCheckCode(cardNo.substring(0, cardNo.length() - 1));
        //return cardNo.charAt(cardNo.length() - 1) == bit;
        //目前只校验是否是数字，且不超过20位（全数字1-20位）
        return Pattern.matches("^(\\d{1,20})$", cardNo);
    }

}
