package com.taiyi.shuduoduo.sms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "sms_log")
public class Log extends CommonMySqlEntity {
    /**
     * 操作内容
     */
    private String actionDescription;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 操作功能
     */
    private String doAction;

    /**
     * 请求IP
     */
    private String ip;

    /**
     * 操作参数
     */
    private String parameters;

    /**
     * 请求路径
     */
    private String path;

    /**
     * 操作人
     */
    private String username;

}