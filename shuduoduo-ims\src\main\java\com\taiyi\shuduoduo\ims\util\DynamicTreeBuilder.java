package com.taiyi.shuduoduo.ims.util;

import com.google.common.collect.ImmutableMap;
import java.util.*;
import java.util.stream.Collectors;

public class DynamicTreeBuilder {

    /**
     * 根据指定字段对列表进行分组
     *
     * @param dataList            需要分组的列表
     * @param groupByFields 分组字段
     * @return 分组后的树状结构
     */
    public static List<Map<String, Object>> buildTree(List<Map<String, Object>> dataList, List<String> groupByFields) {
        return group(dataList, groupByFields.toArray(new String[0]));
    }

    private static List<Map<String, Object>> group(List<Map<String, Object>> dataList, String[] groupingFields) {
        if (groupingFields.length == 0) {
            // 如果没有字段，则返回原始数据
            return dataList.stream()
                    .map(item -> {
                        Map<String, Object> node = new HashMap<>(item);
                        node.put("children", new ArrayList<>());
                        return node;
                    })
                    .collect(Collectors.toList());
        }
        // 当前分组字段
        String currentField = groupingFields[0];
        // 剩余字段
        String[] remainingFields = Arrays.copyOfRange(groupingFields, 1, groupingFields.length);
        // 根据当前字段进行分组
        Map<Object, List<Map<String, Object>>> grouped = dataList.stream()
                .collect(Collectors.groupingBy(item -> item.get(currentField)));
        // 递归处理每一组
        return grouped.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> node = new HashMap<>();
                    node.put(currentField, entry.getKey()); // 当前分组字段的值
                    node.put("children", group(entry.getValue(), remainingFields)); // 递归处理剩余字段
                    return node;
                })
                .collect(Collectors.toList());
    }

    public static void main(String[] args) {
        List<Map<String, Object>> list = Arrays.asList(
                ImmutableMap.of("country", "中国", "city", "陕西", "region", "渭南", "area", 38.8, "people", 500),
                ImmutableMap.of("country", "中国", "city", "陕西", "region", "渭南", "area", 38.8, "people", 600),
                ImmutableMap.of("country", "中国", "city", "陕西", "region", "渭南", "area", 39.8, "people", 700),
                ImmutableMap.of("country", "中国", "city", "陕西", "region", "西安", "area", 39.8, "people", 800),
                ImmutableMap.of("country", "中国", "city", "广东", "region", "广州", "area", 48.8, "people", 900),
                ImmutableMap.of("country", "中国", "city", "广东", "region", "广州", "area", 48.8, "people", 1000),
                ImmutableMap.of("country", "中国", "city", "广东", "region", "广州", "area", 38.8, "people", 1100),
                ImmutableMap.of("country", "中国", "city", "广东", "region", "东莞", "area", 38.8, "people", 1200)
        );

        // 根据country和city分组
        List<String> fieldsToGroupBy = Arrays.asList("country", "city");

        // 生成树状结构
        List<Map<String, Object>> tree = buildTree(list, fieldsToGroupBy);

        // 打印结果
        System.out.println(tree);
    }
}

