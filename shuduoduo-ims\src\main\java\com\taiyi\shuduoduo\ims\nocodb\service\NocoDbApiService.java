package com.taiyi.shuduoduo.ims.nocodb.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.taiyi.shuduoduo.ims.nocodb.api.NocoDbApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RefreshScope
public class NocoDbApiService {

    protected final Logger logger = LoggerFactory.getLogger(NocoDbApiService.class);

    @Autowired
    private NocoDbApi api;

    /**
     * 获取项目列表
     *
     * @return 项目列表
     */
    public List<Map> getProjects() {
        JSONArray jsonArray = api.getUserProjects();
        return jsonArray.toJavaList(Map.class);
    }

    /**
     * 获取项目表列表
     *
     * @param projectId 项目ID
     * @return 表列表
     */
    public List<Map> getProjectTableList(String projectId) {
        JSONArray jsonArray = api.getProjectTableList(projectId);
        return jsonArray.toJavaList(Map.class);
    }

    /**
     * 获取表数据
     *
     * @param projectId 项目ID
     * @param tableName 表名
     * @return 表数据
     */
    public List<Map> getTableData(String projectId, String tableName) {
        JSONArray array = new JSONArray();
        Integer pageSize = 0;
        boolean isLastPage = true;
        while (isLastPage) {
            String respond = api.getTableDataList(projectId, tableName, pageSize);
            JSONObject json = JSON.parseObject(respond);
            if (json.containsKey("list")) {
                array.addAll(json.getJSONArray("list"));
                if (json.containsKey("pageInfo")) {
                    JSONObject object = json.getJSONObject("pageInfo");
                    if (!object.getBoolean("isLastPage")) {
                        pageSize += 25;
                    } else {
                        isLastPage = false;
                    }
                } else {
                    isLastPage = false;
                }
            } else {
                logger.error("Nocodb获取表数据失败，{}", respond);
                isLastPage = false;
            }
        }
        return array.toJavaList(Map.class);
    }

    /**
     * 获取表数据
     *
     * @param projectId 项目ID
     * @param tableName 表名
     * @param option    查询条件
     * @return 表数据
     */
    public List<Map> getTableDataByOption(String projectId, String tableName, String option) {
        JSONArray array = new JSONArray();
        Integer pageSize = 0;
        boolean isLastPage = true;
        while (isLastPage) {
            String respond = api.getTableDataListByOption(projectId, tableName, pageSize, option);
            JSONObject json = JSON.parseObject(respond);
            if (json.containsKey("list")) {
                array.addAll(json.getJSONArray("list"));
                if (json.containsKey("pageInfo")) {
                    JSONObject object = json.getJSONObject("pageInfo");
                    if (!object.getBoolean("isLastPage")) {
                        pageSize += 25;
                    } else {
                        isLastPage = false;
                    }
                } else {
                    isLastPage = false;
                }
            } else {
                logger.error("Nocodb获取表数据失败，{}", respond);
                isLastPage = false;
            }
        }
        return array.toJavaList(Map.class);
    }

    /**
     * 创建表
     *
     * @param projectId 项目ID
     * @param baseId    基础ID
     * @param body      表结构
     * @return 创建结果
     */
    public String createTable(String projectId, String baseId, Map<String, Object> body) {
        return api.createTable(projectId, baseId, body);
    }

    /**
     * 更新表结构
     *
     * @param tableId 表ID
     * @param body    表结构
     * @return 更新结果
     */
    public Boolean updateTable(String tableId, Map<String, Object> body) {
        return api.updateTable(tableId, body);
    }

    /**
     * 删除表
     *
     * @param tableId 表ID
     * @return 删除结果
     */
    public Boolean deleteTable(String tableId) {
        return api.deleteTable(tableId);
    }

    /**
     * 新增表数据
     * {
     * "集团代码": "ccc",
     * "用户账号": "liurr",
     * "用户名称": "刘芮瑞111",
     * "项目名称": "集团项目111111"
     * }
     *
     * @param projectName
     * @param tableName
     * @param body
     * @return bool
     */
    public Boolean insertData(String projectName, String tableName, String body) {
        return api.insertData(projectName, tableName, body);
    }
}
