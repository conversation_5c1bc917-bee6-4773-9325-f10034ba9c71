package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ums.service.UserService;
import com.taiyi.shuduoduo.ums.vo.UserPageVo;
import com.taiyi.shuduoduo.ums.vo.UserVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
@Validated
public class UserController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private UserService userService;

    /**
     * 用户-用户分页查询
     *
     * @param userPageVo 分页参数
     * @return ResponseEntity
     */
    @PostMapping("/page")
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody UserPageVo userPageVo) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, userService.page(userPageVo));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 用户-后台账号分页查询
     *
     * @param userPageVo 分页参数
     * @return ResponseEntity
     */
    @PostMapping("/backend/page")
    public ResponseEntity<ResponseVo.ResponseBean> backendPage(@RequestBody UserPageVo userPageVo) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, userService.page(userPageVo));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 查询用户列表
     *
     * @param keyWord 关键字
     * @return list
     */
    @PostMapping("/list")
    public ResponseEntity<ResponseVo.ResponseBean> userList(@RequestBody UserVo.UserList keyWord) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, userService.userList(keyWord.getKeyWord()));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 查询用户列表
     *
     * @param vo 用户ID 列表
     * @return list
     */
    @PostMapping("/query/list")
    public ResponseEntity<ResponseVo.ResponseBean> getUserListByIds(@RequestBody UserVo.QueryIdsVo vo) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, userService.getUserListByIds(vo.getIds()));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
