package com.taiyi.common.connector.controller;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.taiyi.common.connector.service.DbQueryService;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.connector.util.DateFormatUtil;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping("/db")
public class DbQueryController {

    private static Logger logger = LoggerFactory.getLogger(DbQueryController.class);

    @Resource
    private DbQueryService service;

    @Value("${web.server.host}")
    private String host;

    @Value("${web.server.port}")
    private Integer port;

    /**
     * 获取导出请求参数
     *
     * @param id 请求的参数id
     * @return ResponseEntity
     */
    @GetMapping("/getExportParam/{id}")
    public ResponseEntity getExportParam(@PathVariable("id") String id, HttpServletRequest request) {
        JSONObject param = service.getExportParam(id);
        if (param != null) {
            // host 和 port 从nacos中的NamService中获取
            if (StringUtils.isBlank(host)) {
                host = request.getRemoteHost();
            }
            if (null == port) {
                port = request.getServerPort();
            }
//            String httpUrl = service.getHttpUrl(host, port);
            String httpUrl = "https://api.onedata.aheada.cn";
            return ResponseVo.response(MessageCode.SUCCESS, (Object) (httpUrl + request.getContextPath() + request.getServletPath().replace("getExportParam", "exportExcel")));
        } else {
            return ResponseVo.response(MessageCode.RESOURCE_ERROR, "文件下载链接已过期");
        }
    }

    /**
     * 导出数据表到Excel
     *
     * @param id       导出唯一标识
     * @param response response
     * @throws IOException IO流异常
     */
    @GetMapping("/exportExcel/{id}")
    public void exportExcel(@PathVariable("id") String id, HttpServletResponse response) throws IOException {
        JSONObject jsonObject = service.getExportParam(id);
        if (ObjectUtil.isEmpty(jsonObject)) {
            return;
        }
        String fileName = jsonObject.getString("fileName");
        Map<String, Object> header = (Map<String, Object>) jsonObject.get("header");
        DbDto.DbQueryBySql dto = new DbDto.DbQueryBySql();
        dto.setSql(jsonObject.getString("sql"));
        dto.setHost(jsonObject.getString("host"));
        dto.setPort(Integer.valueOf(jsonObject.getString("port")));
        dto.setDatabase(jsonObject.getString("database"));
        dto.setUsername(jsonObject.getString("username"));
        dto.setPassword(jsonObject.getString("password"));
        dto.setType(jsonObject.getString("type"));
        dto.setSchema(jsonObject.getString("schema"));
        dto.setEngineHost(jsonObject.getString("engineHost"));
        long queryStartTime = System.currentTimeMillis();
        // 需导出的结果集
        List<Map<String, Object>> mapList = service.queryBySql(dto);
        logger.info("{}条数据查询总耗时{}s", mapList.size(), (System.currentTimeMillis() - queryStartTime) / 1000);
        long exportStartTime = System.currentTimeMillis();
        // 创建一个excel文件
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
        SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(xssfWorkbook, 100, true);
        //构建Excel
        createExcel(sxssfWorkbook, fileName, header, mapList);
        //response为HttpServletResponse对象
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=" + new String((new SimpleDateFormat("yyyyMMddHHmmssS").format(new Date()))
                .getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1) + ".xlsx");
        ServletOutputStream out = response.getOutputStream();
        sxssfWorkbook.write(out);
        IoUtil.close(out);
        logger.info("{}条数据导出总耗时{}s", mapList.size(), (System.currentTimeMillis() - exportStartTime) / 1000);
    }

    /**
     * 创建生成Excel
     *
     * @param sxssfWorkbook excel
     * @param fileName      文件名
     * @param header        表头
     * @param mapList       表内容
     */
    private void createExcel(SXSSFWorkbook sxssfWorkbook, String fileName, Map<String, Object> header, List<Map<String, Object>> mapList) {
        SXSSFSheet sheet = sxssfWorkbook.createSheet(fileName);
        Row row = null;
        //创建表头
        if (header.size() > 0) {
            row = sheet.createRow(0);
            for (String key : header.keySet()) {
                int i = Integer.parseInt(((Map<String, Object>) header.get(key)).get("no").toString());
                Cell cell = row.createCell(i);
                cell.setCellValue(((Map<?, ?>) header.get(key)).get("desc").toString());
                //表头自适应宽度
                sheet.setColumnWidth(i, ((Map<?, ?>) header.get(key)).get("desc").toString().getBytes().length * 256);
            }
        }
        //写入表内容
        if (!mapList.isEmpty()) {
            for (int i = 0; i < mapList.size(); i++) {
                row = sheet.createRow(i + 1);
                Map<String, Object> map = mapList.get(i);
                for (String key : header.keySet()) {
                    int j = Integer.parseInt(((Map<String, Object>) header.get(key)).get("no").toString());
                    Cell cell = row.createCell(j);
                    Object cellValue = null;
                    if (map.get(key) != null) {
                        cellValue = map.get(key);
                    }
                    if (cellValue instanceof Date) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue(DateFormatUtil.dateFormat((Date) cellValue));
                    } else if (cellValue instanceof Short) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue((Short) cellValue);
                    } else if (cellValue instanceof Long) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue((Long) cellValue);
                    } else if (cellValue instanceof Integer) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue((Integer) cellValue);
                    } else if (cellValue instanceof Double) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue((Double) cellValue);
                    } else if (cellValue instanceof BigDecimal) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellValue(((BigDecimal) cellValue).doubleValue());
                    } else {
                        cell.setCellType(CellType.STRING);
                        cell.setCellValue((String) cellValue);
                    }
                }
            }
        }
    }
}

