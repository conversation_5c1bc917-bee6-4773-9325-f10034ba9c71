package com.taiyi.shuduoduo.nocodb.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "nc_projects_v2")
public class NcProjects extends CommonMySqlEntity {

    private String baseId;
    /**
     * 名称
     */
    private String title;

    /**
     * 前缀
     */
    private String prefix;

    /**
     * 逻辑删除
     */
    private Integer deleted;

    private Date createdAt;

    private Date updatedAt;

}