package com.taiyi.shuduoduo.ums.aspect;

import com.taiyi.common.data.redis.util.RedisRepository;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ums.service.UserService;
import com.taiyi.shuduoduo.ums.vo.LoginVo;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 登录防暴力切面
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class ApiLoginAspect {

    private final Logger logger = LoggerFactory.getLogger(ApiLoginAspect.class);

    private String redisKey;
    private int count;
    private long defaultTimeOut = 60;

    @Autowired
    RedisRepository redisRepository;

    @Autowired
    UserService userService;


    @Pointcut("execution(public * com.taiyi.shuduoduo.ums.controller.LoginController.adminLogin(..))")
    public void apiLoginPointCutLogin() {

    }

    @Before("apiLoginPointCutLogin()")
    public void before(JoinPoint joinPoint) throws Throwable {
        // 接收到请求，记录请求内容
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String methodUrl = request.getRequestURL().toString();
        /**
         * 获取注解自定义参数
         */
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        MethodResponseException methodResponseException = method.getAnnotation(MethodResponseException.class);

        count = methodResponseException.exceptionCount();

        LoginVo.AdminVo adminVo = (LoginVo.AdminVo) joinPoint.getArgs()[0];
        redisKey = adminVo.getCompanyId() + "_" + adminVo.getName();
        Object s = redisRepository.get(redisKey);

        if (null != s && count == Integer.parseInt(s.toString())) {
            userService.lockUser(adminVo);
            throw new Exception("请求锁定中，请稍后再做尝试。");
        }
    }

    @After("apiLoginPointCutLogin()")
    public void after(JoinPoint joinPoint) throws Throwable {
        Object s = redisRepository.get(redisKey);
        if (null == s) {
            redisRepository.setExpire(redisKey, 1, defaultTimeOut);
        } else {
            redisRepository.incr(redisKey);
        }
    }

    @Around("apiLoginPointCutLogin()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object ob = null;
        // ob 为方法的返回值
        try {
            ob = pjp.proceed();
        } catch (Throwable throwable) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, throwable.getCause().getMessage());
        }
        logger.info("性能监控（耗时） : " + (System.currentTimeMillis() - startTime) + "毫秒");
        return ob;
    }
}
