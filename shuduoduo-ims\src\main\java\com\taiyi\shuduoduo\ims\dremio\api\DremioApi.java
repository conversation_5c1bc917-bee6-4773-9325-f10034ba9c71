package com.taiyi.shuduoduo.ims.dremio.api;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mysql.cj.exceptions.DataReadException;
import com.taiyi.common.data.redis.util.RedisRepository;
import com.taiyi.common.util.ChineseNameUtils;
import com.taiyi.shuduoduo.ims.dremio.vo.DremioVo;
import com.taiyi.shuduoduo.ims.exceptions.DremioException;
import io.swagger.models.auth.In;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * 数据湖API
 *
 * <AUTHOR>
 */
@Component
public class DremioApi {

    protected final Logger logger = LoggerFactory.getLogger(DremioApi.class);

    private static final String DREMIO_KEY = "DREMIO_TOKEN_";

    @Autowired
    private RedisRepository redisRepository;

    /**
     * login
     */
    private static final String GET_TOKEN_URI = "/apiv2/login";

    /**
     * REST API
     * dremio空间接口地址
     */
    private static final String USER_URI = "/api/v3/user";

    /**
     * REST API
     * dremio空间接口地址
     */
    private static final String CATALOG_URI = "/api/v3/catalog";

    /**
     * REST API
     * dremio空间接口地址
     */
    private static final String DATASET_URI = "/apiv2/dataset";

    /**
     * REST API
     * dremio 数据源接口地址
     */
    private static final String SOURCE_URI = "/api/v3/source";

    /**
     * 设置请求头
     *
     * @param username 用户名
     * @param password 密码
     * @param baseUri  api地址
     * @return header
     */
    public Map<String, String> getTokenHeader(String username, String password, String baseUri) {
        Map<String, String> heardMap = new HashMap<>();
        heardMap.put("authorization", getToken(username, password, baseUri).getToken());
        return heardMap;
    }

    /**
     * 获取token
     *
     * @param username 用户名
     * @param password 密码
     * @param baseUri  api地址
     * @return token
     */
    public DremioVo.DremioLoginResponseVo getToken(String username, String password, String baseUri) {
        String cacheToken = (String) redisRepository.get(DREMIO_KEY + username);
        if (null != cacheToken) {
            logger.info("获取token缓存:{}", cacheToken);
            return buildTokenLoginResponseVo(cacheToken);
        }
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("userName", username);
            map.put("password", password);
            String respond = HttpRequest.post(baseUri + GET_TOKEN_URI).body(JSONObject.toJSONString(map)).execute().body();
            logger.warn("DremioApi 获取token:{}", respond);
            JSONObject json = JSON.parseObject(respond);
            if (json.containsKey("token")) {
                String token = json.getString("token");
                long expiresIn = json.getLongValue("expires");
                long now = System.currentTimeMillis();
                redisRepository.setExpire(DREMIO_KEY + username, token, (expiresIn - now) / 1000);
                return buildLoginResponseVo(json);
            }
        } catch (Exception e) {
            logger.error("DremioApi 获取token异常:{}", e.getMessage());
            throw new DremioException("获取token异常:" + ExceptionUtil.stacktraceToString(e));
        }
        return null;
    }

    /**
     * 构建完整的登录响应
     *
     * @param json JSON 响应内容
     * @return DremioLoginResponseVo
     */
    private DremioVo.DremioLoginResponseVo buildLoginResponseVo(JSONObject json) {
        DremioVo.DremioLoginResponseVo responseVo = new DremioVo.DremioLoginResponseVo();
        responseVo.setToken(json.getString("token"));
        responseVo.setUserName(json.getString("userName"));
        responseVo.setFirstName(json.getString("firstName"));
        responseVo.setLastName(json.getString("lastName"));
        responseVo.setExpires(json.getLong("expires"));
        responseVo.setEmail(json.getString("email"));
        responseVo.setUserId(json.getString("userId"));
        responseVo.setAdmin(json.getBoolean("admin"));
        responseVo.setClusterId(json.getString("clusterId"));
        responseVo.setClusterCreatedAt(json.getLong("clusterCreatedAt"));
        responseVo.setVersion(json.getString("version"));
        responseVo.setUserCreatedAt(json.getLong("userCreatedAt"));

        // 处理权限字段
        JSONObject permissionsJson = json.getJSONObject("permissions");
        if (permissionsJson != null) {
            DremioVo.Permissions permissions = new DremioVo.Permissions();
            permissions.setCanUploadProfiles(permissionsJson.getBoolean("canUploadProfiles"));
            permissions.setCanDownloadProfiles(permissionsJson.getBoolean("canDownloadProfiles"));
            permissions.setCanEmailForSupport(permissionsJson.getBoolean("canEmailForSupport"));
            permissions.setCanChatForSupport(permissionsJson.getBoolean("canChatForSupport"));
            permissions.setCanViewAllJobs(permissionsJson.getBoolean("canViewAllJobs"));
            permissions.setCanCreateUser(permissionsJson.getBoolean("canCreateUser"));
            permissions.setCanCreateRole(permissionsJson.getBoolean("canCreateRole"));
            permissions.setCanCreateSource(permissionsJson.getBoolean("canCreateSource"));
            permissions.setCanUploadFile(permissionsJson.getBoolean("canUploadFile"));
            permissions.setCanManageNodeActivity(permissionsJson.getBoolean("canManageNodeActivity"));
            permissions.setCanManageEngines(permissionsJson.getBoolean("canManageEngines"));
            permissions.setCanManageQueues(permissionsJson.getBoolean("canManageQueues"));
            permissions.setCanManageEngineRouting(permissionsJson.getBoolean("canManageEngineRouting"));
            permissions.setCanManageSupportSettings(permissionsJson.getBoolean("canManageSupportSettings"));
            permissions.setCanConfigureSecurity(permissionsJson.getBoolean("canConfigureSecurity"));

            responseVo.setPermissions(permissions);
        }

        return responseVo;
    }

    /**
     * 构建只包含token的响应
     *
     * @param token token
     * @return DremioLoginResponseVo
     */
    private DremioVo.DremioLoginResponseVo buildTokenLoginResponseVo(String token) {
        DremioVo.DremioLoginResponseVo responseVo = new DremioVo.DremioLoginResponseVo();
        responseVo.setToken(token);
        return responseVo;
    }

    /**
     * 创建用户
     *
     * @param username     用户名
     * @param password     密码
     * @param baseUri      api地址
     * @param name         用户名称
     * @param userPassword 密码
     * @param realName     真实姓名
     * @return 用户id
     */
    public boolean createUser(String username, String password, String baseUri, String name, String userPassword, String realName) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("email", name + "@lsy.com");
        map.put("firstName", ChineseNameUtils.getFirstName(realName));
        map.put("lastName", ChineseNameUtils.getLastName(realName));
        map.put("password", userPassword);
        try {
            HttpResponse respond = HttpRequest.post(baseUri + USER_URI).addHeaders(getTokenHeader(username, password, baseUri)).body(JSONObject.toJSONString(map)).execute();
            logger.warn("DremioApi 创建用户:{}", respond);
            if (respond.isOk()) {
                return true;
            } else {
                logger.error("DremioApi 创建用户失败{}", respond.body());
                return false;
            }
        } catch (Exception e) {
            logger.error("DremioApi 创建用户失败{}", ExceptionUtil.stacktraceToString(e));
            throw new DremioException("创建用户失败:" + ExceptionUtil.stacktraceToString(e));
        }
    }


    /**
     * 根据路径获取视图信息
     * {"entityType":"folder","id":"c1be67d5-b192-4630-8c59-059839accc8f","path":["jianzhu","dwd"],"tag":"Q9Xj163M2I4=","children":[{"id":"bdd88846-57d5-494e-a750-b7b89e09bb75","path":["jianzhu","dwd","dwd_ey_jixiao"],"tag":"+fjt1jAmVZg=","type":"DATASET","datasetType":"VIRTUAL","createdAt":"2023-04-28T08:59:38.870Z"},{"id":"6bf18748-1f73-4c7d-822e-77d53814fe99","path":["jianzhu","dwd","dwd_nian_jixiao"],"tag":"kdm7wOaU33U=","type":"DATASET","datasetType":"VIRTUAL","createdAt":"2023-04-28T08:58:37.687Z"},{"id":"c29a34dc-40a6-43d1-9be1-407c08f05e16","path":["jianzhu","dwd","dwd_qianyue"],"tag":"X/pUrPyU3Wo=","type":"DATASET","datasetType":"VIRTUAL","createdAt":"2023-04-26T10:43:15.516Z"},{"id":"f1e5d5f4-31d3-417d-9293-8c71203e966c","path":["jianzhu","dwd","dwd_yy_jixiao"],"tag":"sHdcJRHkxGI=","type":"DATASET","datasetType":"VIRTUAL","createdAt":"2023-04-28T08:59:18.204Z"}]}
     *
     * @param username 用户名
     * @param password 密码
     * @param baseUri  api地址
     * @param path     相对路径
     * @return array
     */
    public JSONArray getDataSetByPath(String username, String password, String baseUri, String path) {
        String respond = HttpRequest.get(baseUri + CATALOG_URI + "/by-path/" + path).addHeaders(getTokenHeader(username, password, baseUri)).execute().body();
        logger.warn("DremioApi 根据路径获取视图信息:{}", respond);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("children")) {
            return json.getJSONArray("children");
        } else if (json.containsKey("errorMessage")) {
            logger.error("根据路径获取视图信息失败:{}", respond);
            throw new DremioException("根据路径获取视图信息失败:" + json.getString("errorMessage"));
        }
        return new JSONArray();
    }

    /**
     * 根据空间id获取空间信息
     *
     * @param username 用户名
     * @param password 密码
     * @param baseUri  api地址
     * @param spaceId  空间id
     * @return json
     */
    public String getSpaceById(String username, String password, String baseUri, String spaceId) {
        String respond = HttpRequest.get(baseUri + CATALOG_URI + "/" + spaceId).addHeaders(getTokenHeader(username, password, baseUri)).execute().body();
        logger.warn("DremioApi 根据空间id获取空间信息:{}", respond);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("id")) {
            return json.getString("id");
        } else if (json.containsKey("errorMessage")) {
            logger.error("根据空间id获取空间信息失败:{}", respond);
            throw new DremioException("根据空间id获取空间信息失败:" + json.getString("errorMessage"));
        }
        return null;
    }

    /**
     * 根据数据集id获取数据集信息
     *
     * @param username  用户名
     * @param password  密码
     * @param baseUri   api地址
     * @param dataSetId 数据集ID
     * @return json
     */
    public JSONObject getDataSetById(String username, String password, String baseUri, String dataSetId) {
        String respond = HttpRequest.get(baseUri + CATALOG_URI + "/" + dataSetId).addHeaders(getTokenHeader(username, password, baseUri)).execute().body();
        logger.warn("DremioApi 根据数据集id获取数据集信息:{}", respond);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("id")) {
            return json;
        } else if (json.containsKey("errorMessage")) {
            logger.error("根据数据集id获取数据集信息:{}", respond);
            throw new DremioException("根据数据集id获取数据集信息:" + json.getString("errorMessage"));
        }
        return null;
    }


    /**
     * 获取数据集详情
     *
     * @param username  用户名
     * @param password  密码
     * @param baseUri   接口地址
     * @param dataSetId 表ID
     * @return 字段列表
     */
    public JSONArray getDataSetDetail(String username, String password, String baseUri, String dataSetId) {
        String respond = HttpRequest.get(baseUri + CATALOG_URI + "/" + dataSetId).addHeaders(getTokenHeader(username, password, baseUri)).execute().body();
        logger.warn("DremioApi 获取表字段:{},请求参数:{}", respond, dataSetId);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("fields")) {
            return json.getJSONArray("fields");
        } else if (json.containsKey("errorMessage")) {
            logger.error("DremioApi 获取表字段失败{}", respond);
            throw new DremioException("获取表字段失败:" + json.getString("errorMessage"));
        }
        return new JSONArray();
    }

    /**
     * 获取视图SQL
     *
     * @param username  用户名
     * @param password  密码
     * @param baseUri   接口地址
     * @param dataSetId 表ID
     * @return 字段列表
     */
    public String getDataSetSql(String username, String password, String baseUri, String dataSetId) {
        String respond = HttpRequest.get(baseUri + CATALOG_URI + "/" + dataSetId).addHeaders(getTokenHeader(username, password, baseUri)).execute().body();
        logger.warn("DremioApi 获取视图SQL:{},请求参数:{}", respond, dataSetId);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("sql")) {
            return json.getString("sql");
        } else if (json.containsKey("errorMessage")) {
            logger.error("DremioApi 获取视图SQL失败{}", respond);
            throw new DremioException("获取视图SQL失败:" + json.getString("errorMessage"));
        }
        return null;
    }

    /**
     * 获取数据源列表
     *
     * @param username 用户名
     * @param password 密码
     * @param baseUri  接口地址
     * @return 数据源列表
     */
    public JSONArray getSourceList(String username, String password, String baseUri) {
        String respond = HttpRequest.get(baseUri + SOURCE_URI).addHeaders(getTokenHeader(username, password, baseUri)).execute().body();
        logger.warn("DremioApi 获取数据源列表:{}", respond);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("data")) {
            return json.getJSONArray("data");
        } else if (json.containsKey("errorMessage")) {
            logger.error("DremioApi 获取数据源列表失败{}", respond);
            throw new DremioException("获取数据源列表失败:" + json.getString("errorMessage"));
        }
        return new JSONArray();
    }

    /**
     * 创建空间
     *
     * @param username   用户名
     * @param password   密码
     * @param entityType 固定值-space
     * @param name       空间名
     */
    public String createSpace(String username, String password, String baseUri, String entityType, String name) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("entityType", entityType);
        String respond = HttpRequest.post(baseUri + CATALOG_URI).addHeaders(getTokenHeader(username, password, baseUri)).body(JSONObject.toJSONString(map)).execute().body();
        logger.warn("DremioApi 创建空间:{}", respond);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("id")) {
            return json.getString("id");
        } else if (json.containsKey("errorMessage")) {
            logger.error("DremioApi 创建空间失败，{}", respond);
            throw new DremioException("创建空间失败:" + json.getString("errorMessage"));
        }
        return null;
    }

    /**
     * 创建文件夹
     *
     * @param username   用户名
     * @param password   密码
     * @param baseUri    接口地址
     * @param entityType 固定值-folder
     * @param name       文件夹名
     * @param spaceName  空间名
     */
    public String createFolder(String username, String password, String baseUri, String entityType, String name, String spaceName, List<String> path) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("entityType", entityType);
        map.put("path", path.isEmpty() ? Arrays.asList(spaceName, name) : path);
        String respond = HttpRequest.post(baseUri + CATALOG_URI).addHeaders(getTokenHeader(username, password, baseUri)).body(JSONObject.toJSONString(map)).execute().body();
        logger.warn("DremioApi 创建文件夹:{},请求参数:{},{},{}", respond, entityType, name, spaceName);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("id")) {
            return json.getString("id");
        } else if (json.containsKey("errorMessage")) {
            logger.error("DremioApi 创建文件夹失败{}", respond);
            throw new DremioException("创建文件夹失败:" + json.getString("errorMessage"));
        }
        return null;
    }

    /**
     * 创建数据集
     *
     * @param username   用户名
     * @param password   密码
     * @param baseUri    接口地址
     * @param entityType 固定值-folder
     * @param name       文件夹名
     * @param spaceName  空间名
     */
    public String createDataSet(String username, String password, String baseUri, String entityType, String name, String spaceName, String folderName, String sql) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", "VIRTUAL_DATASET");
        map.put("name", name);
        map.put("entityType", entityType);
        map.put("path", Arrays.asList(spaceName, folderName, name));
        map.put("sql", sql);
        String respond = HttpRequest.post(baseUri + CATALOG_URI).addHeaders(getTokenHeader(username, password, baseUri)).body(JSONObject.toJSONString(map)).execute().body();
        logger.warn("DremioApi 创建数据集:{},请求参数:{},{},{},{},{}", respond, entityType, name, spaceName, folderName, sql);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("id")) {
            return json.getString("id");
        } else if (json.containsKey("errorMessage")) {
            logger.error("DremioApi 创建数据集失败{}", respond);
            throw new DremioException("创建数据集失败:" + json.getString("errorMessage"));
        }
        return null;
    }

    /**
     * 修改空间
     *
     * @param id         空间ID
     * @param username   用户名
     * @param password   密码
     * @param entityType 固定值-space
     * @param name       空间名
     */
    public String updateSpace(String id, String username, String password, String baseUri, String entityType, String name) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("entityType", entityType);
        String respond = HttpRequest.put(baseUri + CATALOG_URI + "/" + id).addHeaders(getTokenHeader(username, password, baseUri)).body(JSONObject.toJSONString(map)).execute().body();
        logger.warn("DremioApi 修改空间:{}", respond);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("id")) {
            return json.getString("id");
        } else if (json.containsKey("errorMessage")) {
            logger.error("DremioApi 修改空间失败{}", respond);
            throw new DremioException("修改空间失败:" + json.getString("errorMessage"));
        }
        return null;
    }

    /**
     * 修改文件夹
     *
     * @param id         文件夹ID
     * @param username   用户名
     * @param password   密码
     * @param baseUri    接口地址
     * @param entityType 固定值-folder
     * @param name       文件夹名
     * @param spaceName  空间名
     */
    public String updateFolder(String id, String username, String password, String baseUri, String entityType, String name, String spaceName) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("entityType", entityType);
        map.put("path", Arrays.asList(spaceName, name));
        String respond = HttpRequest.put(baseUri + CATALOG_URI + "/" + id).addHeaders(getTokenHeader(username, password, baseUri)).body(JSONObject.toJSONString(map)).execute().body();
        logger.warn("DremioApi 修改文件夹:{}", respond);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("id")) {
            return json.getString("id");
        } else if (json.containsKey("errorMessage")) {
            logger.error("DremioApi 创建文件夹失败{}", respond);
            throw new DremioException("创建文件夹失败:" + json.getString("errorMessage"));
        }
        return null;
    }

    /**
     * 更新数据集
     *
     * @param username   用户名
     * @param password   密码
     * @param baseUri    接口地址
     * @param id         datasetId
     * @param entityType 固定值-folder
     * @param name       文件名
     * @param sql        SQL
     * @param folderName 文件夹名
     * @param spaceName  空间名
     */
    public boolean updateDataSet(String username, String password, String baseUri, String id, String entityType, String name, String spaceName, String folderName, String sql) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", "VIRTUAL_DATASET");
        map.put("id", id);
        map.put("entityType", entityType);
        map.put("path", Arrays.asList(spaceName, folderName, name));
        map.put("sql", sql);
        String respond = HttpRequest.put(baseUri + CATALOG_URI + "/" + id).addHeaders(getTokenHeader(username, password, baseUri)).body(JSONObject.toJSONString(map)).execute().body();
        logger.warn("DremioApi 更新视图SQL:{},请求参数:{},{},{},{},{}", respond, entityType, name, spaceName, folderName, sql);
        JSONObject json = JSON.parseObject(respond);
        if (json.containsKey("id")) {
            return true;
        } else if (json.containsKey("errorMessage")) {
            logger.error("DremioApi 更新视图SQL失败{}", respond);
            throw new DremioException("更新视图SQL失败:" + json.getString("errorMessage"));
        }
        return false;
    }

    /**
     * 重命名数据集
     *
     * @param username   用户名
     * @param password   密码
     * @param baseUri    接口地址
     * @param folderName 文件夹名
     * @param spaceName  空间名
     * @param oldName    旧名称
     * @param newName    新名称
     * @return boolean
     */
    public boolean renameDataSet(String username, String password, String baseUri, String spaceName, String folderName, String oldName, String newName) {
        try {
            String encodedSpace = java.net.URLEncoder.encode("\"" + spaceName + "\"", "UTF-8");
            String encodedFolder = java.net.URLEncoder.encode("\"" + folderName + "\"", "UTF-8");
            String encodedOldName = java.net.URLEncoder.encode("\"" + oldName + "\"", "UTF-8");

            String encodedDatasetPath = String.format("%s.%s.%s", encodedSpace, encodedFolder, encodedOldName);
            // 构造请求 URL
            String url = baseUri + DATASET_URI + "/" + encodedDatasetPath + "/rename/?renameTo=" + newName;
            logger.info("请求地址{}", url);

            HttpResponse respond = HttpRequest.post(url).addHeaders(getTokenHeader(username, password, baseUri)).execute();
            logger.warn("DremioApi 重命名视图:{},请求参数:{},{},{},{}", respond.body(), spaceName, folderName, oldName, newName);
            if (respond.isOk()) {
                return true;
            } else {
                logger.error("DremioApi 重命名视图失败{}", respond.body());
                return false;
            }
        } catch (Exception e) {
            logger.error("DremioApi 重命名视图失败{}", ExceptionUtil.stacktraceToString(e));
            throw new DremioException("重命名视图失败:" + ExceptionUtil.stacktraceToString(e));
        }
    }


    /**
     * 创建数据源连接
     *
     * @param username 用户名
     * @param password 密码
     * @param baseUri  接口地址
     * @param body     数据源连接信息
     * @return 数据源ID
     */
    public boolean createSource(String username, String password, String baseUri, Map<String, Object> body) {
        String respond = HttpRequest.post(baseUri + SOURCE_URI).addHeaders(getTokenHeader(username, password, baseUri)).body(JSONObject.toJSONString(body)).execute().body();
        logger.warn("DremioApi 创建数据源连接:{},请求参数:{},{},{},{}", respond, username, password, baseUri, body);
        JSONObject json = JSON.parseObject(respond);
        return json.containsKey("id");
    }

    /**
     * 删除空间
     *
     * @param username 用户名
     * @param password 密码
     * @param baseUri  接口地址
     * @param spaceId  空间 ID
     * @return bool
     */
    public boolean deleteSpaceById(String username, String password, String baseUri, String spaceId) {
        HttpResponse response = HttpRequest.delete(baseUri + CATALOG_URI + "/" + spaceId).addHeaders(getTokenHeader(username, password, baseUri)).execute();
        logger.warn("DremioApi 删除文件夹请求参数:{},返回结果:{}", spaceId, response.body());
        return response.isOk();
    }

    /**
     * 删除文件夹
     *
     * @param username 用户名
     * @param password 密码
     * @param baseUri  接口地址
     * @param folderId 文件夹ID
     * @return bool
     */
    public boolean deleteFolderById(String username, String password, String baseUri, String folderId) {
        HttpResponse response = HttpRequest.delete(baseUri + CATALOG_URI + "/" + folderId).addHeaders(getTokenHeader(username, password, baseUri)).execute();
        logger.warn("DremioApi 删除文件夹请求参数:{},返回结果:{}", folderId, response.body());
        return response.isOk();
    }

    /**
     * 删除数据集
     *
     * @param username  用户名
     * @param password  密码
     * @param baseUri   接口地址
     * @param datasetId 数据集ID
     * @return bool
     */
    public boolean deleteDataSetById(String username, String password, String baseUri, String datasetId) {
        // 获取数据集详情
        JSONObject json = getDataSetById(username, password, baseUri, datasetId);
        String tag = json.getString("tag");
        JSONArray path = json.getJSONArray("path");
        if (tag == null || path == null) {
            return false;
        }
        try {
            String space = path.getString(0);
            String folder = path.getString(1);
            String name = path.getString(2);
            String encodedSpace = java.net.URLEncoder.encode("\"" + space + "\"", "UTF-8");
            String encodedFolder = java.net.URLEncoder.encode("\"" + folder + "\"", "UTF-8");
            String encodedName = java.net.URLEncoder.encode("\"" + name + "\"", "UTF-8");
            // 编码 tag
            String encodedTag = java.net.URLEncoder.encode(tag, "UTF-8");
            String encodedDatasetPath = String.format("%s.%s.%s", encodedSpace, encodedFolder, encodedName);
            String url = baseUri + DATASET_URI + "/" + encodedDatasetPath + "/?savedTag=" + encodedTag;
            HttpResponse response = HttpRequest.delete(url).addHeaders(getTokenHeader(username, password, baseUri)).execute();
            logger.info("DremioApi 删除数据集请求参数:{},返回结果:{}", datasetId, response.body());
            return response.isOk();
        } catch (Exception e) {
            logger.error("DremioApi 删除数据集失败{}", ExceptionUtil.stacktraceToString(e));
        }
        return false;
    }
}
