package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.util.IdUtil;
import com.taiyi.shuduoduo.ims.entity.Dimension;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DimensionControllerTest {
    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;
    private static final String json_utf8 = "application/json;charset=UTF-8";


    @BeforeEach
    public void env() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    private static final String uuid = IdUtil.simpleUUID();
    private static final String companyId = "";
    private static final String pathName = "/dimension/";
    private static final String pathNamePage = pathName + "page";
    public Dimension dimension;

    public DimensionControllerTest() {
        dimension = new Dimension();
        dimension.setName("1111243124");
        dimension.setId(uuid);
        dimension.setCode("1111sdafdsaf");
        dimension.setDescription("sssswefa");
    }

    @Test
    @Order(10)
    void save() {

    }

    @Test
    @Order(20)
    void updateById() {

    }

    @Test
    @Order(100)
    void deleteById() {
        try {
            String r = mockMvc.perform(MockMvcRequestBuilders.delete(pathName + uuid)
                    .characterEncoding("utf8")
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .accept(json_utf8))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andDo(MockMvcResultHandlers.print())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    @Order(30)
    void getById() {
        try {
            String r = mockMvc.perform(MockMvcRequestBuilders.get(pathName + uuid)
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .accept(json_utf8))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andDo(MockMvcResultHandlers.print())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    @Order(40)
    void page() {
    }

    @Test
    @Order(50)
    void listByCompany() {
        try {
            String r = mockMvc.perform(MockMvcRequestBuilders.post(pathName+"list/" + "7172070a1c264c2bb6bb201e17bf8ee7")
                    .characterEncoding("utf8")
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .accept(json_utf8))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andDo(MockMvcResultHandlers.print())
                    .andReturn().getResponse().getContentAsString();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}