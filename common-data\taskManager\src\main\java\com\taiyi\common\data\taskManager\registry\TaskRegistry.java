package com.taiyi.common.data.taskManager.registry;

import com.taiyi.common.data.taskManager.service.ControllableTask;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务注册中心
 *
 * <AUTHOR>
 */
public class TaskRegistry {
    private static final Map<String, ControllableTask> taskMap = new ConcurrentHashMap<>();

    public static void register(ControllableTask task) {
        taskMap.put(task.getTaskName(), task);
    }

    public static ControllableTask get(String taskName) {
        return taskMap.get(taskName);
    }

    public static Map<String, ControllableTask> all() {
        return taskMap;
    }
}

