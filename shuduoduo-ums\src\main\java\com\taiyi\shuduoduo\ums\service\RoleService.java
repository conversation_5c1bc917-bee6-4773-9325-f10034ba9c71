package com.taiyi.shuduoduo.ums.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ums.dao.RoleDao;
import com.taiyi.shuduoduo.ums.entity.CompanyRole;
import com.taiyi.shuduoduo.ums.entity.Role;
import com.taiyi.shuduoduo.ums.entity.UserRole;
import com.taiyi.shuduoduo.ums.vo.RoleVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色业务层
 *
 * <AUTHOR>
 */
@Service
public class RoleService extends CommonMysqlService<RoleDao, Role> {
    @Override
    public Class<Role> getEntityClass() {
        return Role.class;
    }

    @Autowired
    private CompanyRoleService companyRoleService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private RoleDao roleDao;

    /**
     * 保存角色信息
     *
     * @param role 角色信息
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean save(Role role) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        role.setCompanyId(companyId);
        boolean f = super.save(role);
        if (f) {
            CompanyRole companyRole = new CompanyRole();
            companyRole.setComId(companyId);
            companyRole.setRoleId(role.getId());
            f = companyRoleService.save(companyRole);
        }
        return f;
    }

    /**
     * 重写逻辑删除方法
     *
     * @param id 角色ID
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean logicDeleteById(String id) {
        boolean f = super.logicDeleteById(id);
        if (f) {
            CompanyRole companyRole = companyRoleService.getByComIdAndRoleId(CurrentUserUtil.get().getCompanyId(), id);
            f = companyRoleService.deleteById(companyRole.getId());
        }
        return f;
    }

    /**
     * 分页查询角色列表
     *
     * @param pageVo 分页参数
     * @return PageResult
     */
    public PageResult<Role> selectPage(RoleVo.PageParam pageVo) {
        Page<Role> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        QueryWrapper<Role> wrapper = new QueryWrapper<>();
        wrapper.eq("ur.if_deleted", false).eq("ucr.com_id", CurrentUserUtil.get().getCompanyId());
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.like("ur.name", pageVo.getKeyWord());
        }
        wrapper.orderByDesc("ur.create_time", "ur.name");
        IPage<Role> rolePage = roleDao.selectPage(page, wrapper);
        return getPageResult(page, rolePage);
    }

    /**
     * 判重
     *
     * @param role role
     * @return boolean
     */
    @Override
    public boolean exists(Role role) {
        QueryWrapper<Role> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("ur.name", role.getName());
        queryWrapper.eq("ur.if_deleted", false).eq("ucr.com_id", CurrentUserUtil.get().getCompanyId());
        queryWrapper.last("LIMIT 1");
        Role one = roleDao.selectOne(queryWrapper);
        return one != null;
    }

    /**
     * 根据用户ID获取角色名称列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    public List<String> getRoleStrByUserId(String userId) {
        List<UserRole> userRoleList = userRoleService.getRoleListByUserId(userId);
        if (CollectionUtils.isEmpty(userRoleList)) {
            return Collections.emptyList();
        }
        QueryWrapper<Role> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).in("id", userRoleList.stream().map(UserRole::getRoleId).collect(Collectors.toList()));
        List<Role> list = super.list(wrapper);
        return list.stream().map(Role::getName).collect(Collectors.toList());
    }

    /**
     * 根据用户ID获取角色ID列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    public List<String> getRoleIdsByUserId(String userId) {
        List<UserRole> userRoleList = userRoleService.getRoleListByUserId(userId);
        if (CollectionUtils.isEmpty(userRoleList)) {
            return Collections.emptyList();
        }
        QueryWrapper<Role> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).in("id", userRoleList.stream().map(UserRole::getRoleId).collect(Collectors.toList()));
        List<Role> list = super.list(wrapper);
        return list.stream().map(Role::getId).collect(Collectors.toList());
    }

    public List<String> getUsersByRoleId(String roleId, String companyId) {
        List<UserRole> userRoleList = userRoleService.getUserListByRoleId(roleId, companyId);
        return userRoleList.stream().collect(ArrayList::new,
                (list, userRole) -> list.add(userRole.getUserId()),
                ArrayList::addAll);
    }

    public List<Role> getList(String keyWord) {
        QueryWrapper<Role> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false)
                .eq("company_id", CurrentUserUtil.get().getCompanyId());
        if (StringUtils.isNotBlank(keyWord)) {
            wrapper.like("name", keyWord);
        }
        wrapper.orderByDesc("create_time", "name");
        return super.list(wrapper);
    }
}