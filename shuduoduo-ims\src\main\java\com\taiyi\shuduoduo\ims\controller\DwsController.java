package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.taiyi.common.aspect.ApiLogPointCut;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.DimDws;
import com.taiyi.shuduoduo.ims.entity.Dws;
import com.taiyi.shuduoduo.ims.entity.Plate;
import com.taiyi.shuduoduo.ims.exceptions.QueryFailedException;
import com.taiyi.shuduoduo.ims.service.DimDwsService;
import com.taiyi.shuduoduo.ims.service.DwsService;
import com.taiyi.shuduoduo.ims.service.PlateService;
import com.taiyi.shuduoduo.ims.util.SqlBuilderUtil;
import com.taiyi.shuduoduo.ims.vo.DwsVo;
import com.taiyi.shuduoduo.ims.vo.RawVo;
import com.taiyi.shuduoduo.ims.vo.SqlBuilderVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyDremioRpcService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

/**
 * 指标
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/dws")
@Validated
@ApiOperation(value = "指标模块")
public class DwsController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DwsService dwsService;

    @Autowired
    private DimDwsService dimDwsService;

    @Autowired
    private CompanyDremioRpcService companyDremioRpcService;

    @Autowired
    private PlateService plateService;

    /**
     * 新增数据
     *
     * @param t t
     * @return T
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated DwsVo.InsertParam t) {
        Dws dws = BeanUtil.copy(t, Dws.class);
        dws.setCompanyId(CurrentUserUtil.get().getCompanyId());
        if (dwsService.isExist(dws)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.NAME_DUPLICATE);
        }
        boolean f;
        try {
            f = dwsService.saveOrUpdate(dws, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dwsService.getOpenApiDetailById(id));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        List<DimDws> dimDwsList = dimDwsService.getListByDwsId(id);
        if (!dimDwsList.isEmpty()) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_DIM_LIST_IN_THIS_DWS);
        }
        try {
            f = dwsService.logicDeleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 指标定义-分页查询
     *
     * @param query 筛选条件
     * @return T
     */
    @PostMapping("/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody DwsVo.DwsPageVo query) {
        try {
            PageResult page = dwsService.getPage(query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 指标百科-分页查询
     *
     * @param query 筛选条件
     * @return T
     */
    @PostMapping("/wiki/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> wikiPage(@RequestBody DwsVo.DwsPageVo query) {
        try {
            PageResult page = dwsService.getWikiPage(query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 指标认证-分页查询
     *
     * @param query 筛选条件
     * @return T
     */
    @PostMapping("/auth/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> authPage(@RequestBody DwsVo.DwsPageVo query) {
        try {
            PageResult page = dwsService.getAuthPage(query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 数据挂接-分页查询
     *
     * @param query 筛选条件
     * @return T
     */
    @PostMapping("/dock/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> dockPage(@RequestBody DwsVo.DwsPageVo query) {
        try {
            PageResult page = dwsService.getDockPage(query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 列表查询
     *
     * @param plateId 板块ID
     * @param keyWord 关键字
     * @return T
     */
    @GetMapping("/list/{plateId}")
    public ResponseEntity<ResponseVo.ResponseBean> list(@PathVariable("plateId") String plateId, @RequestParam("keyWord") String keyWord) {
        try {
            List<Dws> page = dwsService.getList(plateId, 1, keyWord);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 列表查询
     *
     * @param keyWord
     * @return
     */
    @GetMapping("/list/name")
    public ResponseEntity<ResponseVo.ResponseBean> list(@RequestParam(value = "keyWord", required = false) String keyWord) {
        try {
            List<Dws> page = dwsService.getListByName(keyWord);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 上移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/up/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> up(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (dwsService.sequence(id, orderBy, Dws.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 下移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/down/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> down(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (dwsService.sequence(id, orderBy, Dws.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 导入
     *
     * @param file 文件
     * @return T
     */
    @PostMapping("/importDws")
    public ResponseEntity<ResponseVo.ResponseBean> importDws(MultipartFile file) {
        if (file.isEmpty()) {
            return ResponseVo.response(MessageCode.FILE_ERROR);
        }
        try {
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Object o = dwsService.importDws(workbook, CurrentUserUtil.get().getCompanyId());
            return ResponseVo.response(MessageCode.SUCCESS, null, o);
        } catch (Exception e) {
            logger.error(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, null, TipsConstant.FILE_DATA_FORMAT_ERROR);
        }
    }

    /**
     * 根据指标查询关联的标签列表
     *
     * @param id 指标id
     * @return T
     */
    @GetMapping("/label/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getDwsListByLabelId(@PathVariable("id") String id) {
        if (null == dwsService.getById(id)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dwsService.getDwsListByDwsId(id));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据指标ID获取认证列表
     *
     * @param id 指标id
     * @return T
     */
    @GetMapping("/auth/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getAuthListByDwsId(@PathVariable("id") String id) {
        if (null == dwsService.getById(id)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dwsService.getAuthListByDwsId(id));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 指标搜索列表
     *
     * @param query q
     * @return T
     */
    @PostMapping("/search/list")
    public ResponseEntity<ResponseVo.ResponseBean> searchList(@RequestBody(required = false) RawVo.SearchParam query) {
        if (query != null && StringUtils.isNotBlank(query.getKeyWord())) {
            if (SqlBuilderUtil.sqlValidate(query.getKeyWord()) || query.getKeyWord().contains("'")) {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.FILTER_PARAM_NOT_ALLOWED);
            }
        }
        try {
            List<DwsVo.SearchResponse> rawList = dwsService.getSearchDwsList(query);
            return ResponseVo.response(MessageCode.SUCCESS, rawList);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 发布/下线
     *
     * @param id        id
     * @param ifPublish ifPublish
     * @return T
     */
    @PatchMapping("/publish/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> publishOrDownDws(@PathVariable("id") String id, @RequestParam("ifPublish") String ifPublish) {
        try {
            if (dwsService.publishOrDownDws(id, Boolean.valueOf(ifPublish))) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 获取日志记录
     *
     * @param id 指标ID
     * @return T
     */
    @GetMapping("/log/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> OperationLogs(@PathVariable("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dwsService.getLogs(id));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 智能台账搜索
     *
     * @param names 名称列表
     * @return 英文名称
     */
    @GetMapping("/query")
    public ResponseEntity<ResponseVo.ResponseBean> queryNames(@RequestParam("names") String names) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dwsService.queryCodeByNames(Arrays.asList(names.split(StrUtil.COMMA))));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 根据维度ID获取指标列表并获取对应的数据域,并根据数据域分组
     *
     * @param dimId 维度ID
     * @return T
     */
    @GetMapping("/group/list/{dimId}")
    public ResponseEntity<ResponseVo.ResponseBean> queryDwsListByDimId(@PathVariable("dimId") String dimId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dwsService.getDwsListByDimId(dimId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

}
