package com.taiyi.common.util;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CurrentUserUtil {

    private static final ThreadLocal<CurrentUser> threadLocal = new ThreadLocal<>();

    public static void put(CurrentUser currentUser) {
        threadLocal.set(currentUser);
    }

    public static CurrentUser get() {
        return threadLocal.get() == null ? new CurrentUser() : threadLocal.get();
    }

    public static void remove() {
        threadLocal.remove();

    }

    public static class CurrentUser {

        /**
         * 数据库id
         */
        private String id;

        /**
         * 用户ID
         */
        private String openId;

        private String thirdUserId;

        /**
         * 大图
         */
        private String avatarBig;

        /**
         * 中图
         */
        private String avatarMiddle;

        /**
         * 小图
         */
        private String avatarThumb;

        /**
         * 用户头像
         */
        private String avatarUrl;

        /**
         * 授权token
         */
        private String accessToken;

        /**
         * 刷新token
         */
        private String refreshToken;

        /**
         * token 类型
         */
        private String tokenType;//Bearer

        /**
         * token过期时间
         */
        private Long expiresIn;//3600

        /**
         * 刷新token 过期时间
         */
        private Long refreshExpiresIn;//2592000

        /**
         * 真实名字
         */
        private String realName;

        /**
         * 用户名
         */
        private String nickName;

        /**
         * 是否管理员账号
         */
        private Boolean ifBackend;

        public Boolean getIfBackend() {
            return ifBackend;
        }

        public void setIfBackend(Boolean ifBackend) {
            this.ifBackend = ifBackend;
        }

        /**
         * 企业key
         */
        private String tenantKey;

        /**
         * 公司唯一标识
         */
        private String companyId;

        /**
         * 用户的角色列表
         */
        private List<String> roles;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getNickName() {
            return nickName;
        }

        public void setNickName(String nickName) {
            this.nickName = nickName;
        }

        public String getOpenId() {
            return openId;
        }

        public void setOpenId(String openId) {
            this.openId = openId;
        }

        public String getThirdUserId() {
            return thirdUserId;
        }

        public void setThirdUserId(String thirdUserId) {
            this.thirdUserId = thirdUserId;
        }

        public String getAvatarBig() {
            return avatarBig;
        }

        public void setAvatarBig(String avatarBig) {
            this.avatarBig = avatarBig;
        }

        public String getAvatarMiddle() {
            return avatarMiddle;
        }

        public void setAvatarMiddle(String avatarMiddle) {
            this.avatarMiddle = avatarMiddle;
        }

        public String getAvatarThumb() {
            return avatarThumb;
        }

        public void setAvatarThumb(String avatarThumb) {
            this.avatarThumb = avatarThumb;
        }

        public String getAvatarUrl() {
            return avatarUrl;
        }

        public void setAvatarUrl(String avatarUrl) {
            this.avatarUrl = avatarUrl;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public String getRefreshToken() {
            return refreshToken;
        }

        public void setRefreshToken(String refreshToken) {
            this.refreshToken = refreshToken;
        }

        public String getTokenType() {
            return tokenType;
        }

        public void setTokenType(String tokenType) {
            this.tokenType = tokenType;
        }

        public Long getExpiresIn() {
            return expiresIn;
        }

        public void setExpiresIn(Long expiresIn) {
            this.expiresIn = expiresIn;
        }

        public Long getRefreshExpiresIn() {
            return refreshExpiresIn;
        }

        public void setRefreshExpiresIn(Long refreshExpiresIn) {
            this.refreshExpiresIn = refreshExpiresIn;
        }

        public String getTenantKey() {
            return tenantKey;
        }

        public void setTenantKey(String tenantKey) {
            this.tenantKey = tenantKey;
        }

        public String getCompanyId() {
            return companyId;
        }

        public void setCompanyId(String companyId) {
            this.companyId = companyId;
        }

        public List<String> getRoles() {
            return roles;
        }

        public void setRoles(List<String> roles) {
            this.roles = roles;
        }

        @Override
        public String toString() {
            return "CurrentUser{" +
                    "openId='" + openId + '\'' +
                    ", nickName='" + nickName + '\'' +
                    ", companyId='" + companyId + '\'' +
                    ", avatarUrl='" + avatarUrl + '\'' +
                    ", roles=" + roles +
                    '}';
        }
    }

}
