package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.data.mysql.entity.MyQuery;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ums.service.DepartmentService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 部门
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/department")
@Validated
public class DepartmentController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DepartmentService departmentService;

    /**
     * 组织架构(部门)-用户-部门下用户分页查询
     *
     * @param id 部门id
     * @return ResponseEntity
     */
    @GetMapping("/department-user/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getUserDepartmentPage(@PathVariable("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, departmentService.getDepartmentUserPage(id));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 组织架构(部门)-用户-公司下用户分页查询
     *
     * @param id    公司id
     * @param query 分页参数
     * @return ResponseEntity
     */
    @PostMapping("/company-user/page/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getCompanyUserPage(@PathVariable("id") String id, @RequestBody MyQuery query) {
        try {
            PageResult page = departmentService.getCompanyUserPage(id, query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 组织架构(部门)-部门树列表
     *
     * @param id 公司id
     * @return ResponseEntity
     */
    @GetMapping("/list")
    public ResponseEntity<ResponseVo.ResponseBean> getDepartmentList(@RequestParam("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, StringUtils.isBlank(id) ? departmentService.getDeptListAsLevel2Node() : departmentService.getDepartmentListByPid(id));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }
}
