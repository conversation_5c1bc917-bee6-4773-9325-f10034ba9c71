package com.taiyi.common.connector.netty.server;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.connector.service.DbQueryService;
import com.taiyi.common.connector.netty.utils.DataMessageDecode;
import com.taiyi.common.connector.netty.utils.DataMessageEncoder;
import com.taiyi.common.connector.netty.vo.DataEngineMsg;
import com.taiyi.common.connector.util.SyncFutureUtil;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Netty服务端
 *
 * <AUTHOR>
 */
@Component
public class NettyServer implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(NettyServer.class);

    @Value("${netty.websocket.port}")
    private int port;

    @Value("${netty.websocket.ip}")
    private String ip;

    @Value("${netty.websocket.path}")
    private String path;

    @Value("${netty.websocket.max-frame-size}")
    private long maxFrameSize;

    @Autowired
    private NettyServerHandler nettyServerHandler;

    @Override
    public void run(ApplicationArguments args) {
        EventLoopGroup bossGroup = new NioEventLoopGroup();
        EventLoopGroup workerGroup = new NioEventLoopGroup();

        ServerBootstrap serverBootstrap = new ServerBootstrap();
        serverBootstrap.group(bossGroup, workerGroup);
        serverBootstrap.childOption(ChannelOption.SO_KEEPALIVE, true);
        serverBootstrap.channel(NioServerSocketChannel.class);
        serverBootstrap.childHandler(new ChannelInitializer<SocketChannel>() {
            @Override
            protected void initChannel(SocketChannel socketChannel) throws Exception {
                socketChannel.config().setRecvByteBufAllocator(new FixedRecvByteBufAllocator(8096));
                ChannelPipeline pipeline = socketChannel.pipeline();
                pipeline.addLast(new DataMessageDecode());
                pipeline.addLast(new DataMessageEncoder());
                //添加自定义handler
                pipeline.addLast(new NettyServerHandler());
            }
        });
        try {
            ChannelFuture channelFuture = serverBootstrap.bind(this.port).sync();
            logger.info("NETTY 服务启动，ip={},port={}", this.ip, this.port);
            channelFuture.channel().closeFuture().sync();
        } catch (InterruptedException e) {
            logger.error(ExceptionUtil.stacktraceToString(e));
        } finally {
            bossGroup.shutdownGracefully();
            workerGroup.shutdownGracefully();
        }
    }

    /**
     * 发送同步消息
     *
     * @param message msg
     */
    public DataEngineMsg sendSyncMsg(DataEngineMsg message, SyncFutureUtil<DataEngineMsg> syncFuture) {
        Channel socketChannel = nettyServerHandler.getChannel(message);
        if (socketChannel == null) {
            message.setResponse("远程主机未连接");
            return message;
        }
        try {
            ChannelFuture future = socketChannel.writeAndFlush(message).sync();
            logger.warn("请求发送参数{}", DbQueryService.consoleLog(message));
            future.addListener((ChannelFutureListener) future1 -> {
                if (!future1.isSuccess()) {
                    logger.error("请求发送失败");
                }
            });
            // 等待 30 秒
            message = syncFuture.get(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            logger.error(ExceptionUtil.stacktraceToString(e));
        }
        return message;
    }

}
