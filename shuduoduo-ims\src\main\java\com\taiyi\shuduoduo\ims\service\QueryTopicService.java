package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.QueryTopicDao;
import com.taiyi.shuduoduo.ims.entity.QueryTopic;
import com.taiyi.shuduoduo.ims.vo.QueryTopicVo;
import com.taiyi.shuduoduo.ims.vo.RawVo;
import com.taiyi.shuduoduo.ims.vo.TopicShareVo;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.api.service.UserRpcService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 主题业务层
 *
 * <AUTHOR>
 */
@Service
public class QueryTopicService extends CommonMysqlService<QueryTopicDao, QueryTopic> {

    @Autowired
    private TopicShareService topicShareService;

    @Autowired
    private UserRpcService userRpcService;

    @Override
    public Class<QueryTopic> getEntityClass() {
        return QueryTopic.class;
    }

    public boolean isNameDuplicate(QueryTopic queryTopic) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        String userId = CurrentUserUtil.get().getId();
        QueryWrapper<QueryTopic> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("subject", queryTopic.getSubject())
                .eq("user_id", userId)
                .eq("if_deleted", false)
                .ne(StringUtils.isNotBlank(queryTopic.getId()), "id", queryTopic.getId());
        QueryTopic topic = this.getOne(wrapper);
        return topic != null;
    }

    /**
     * 新增主题
     *
     * @param queryTopic 主题内容
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean add(QueryTopic queryTopic) {
        queryTopic.setUserId(CurrentUserUtil.get().getId());
        queryTopic.setLastTime(new Date());
        queryTopic.setCreateBy(StringUtils.isNotBlank(CurrentUserUtil.get().getRealName()) ? CurrentUserUtil.get().getRealName() : CurrentUserUtil.get().getOpenId());
        queryTopic.setCompanyId(CurrentUserUtil.get().getCompanyId());
        queryTopic.setSelectedAttr(queryTopic.getSelectedAttr());
        queryTopic.setSelectedFilter(queryTopic.getSelectedFilter());
        queryTopic.setSelectedOrder(queryTopic.getSelectedOrder());
        queryTopic.setDataParse(queryTopic.getDataParse());
        if (StringUtils.isBlank(queryTopic.getId())) {
            queryTopic.setId(null);
            return this.save(queryTopic);
        } else {
            return this.updateById(queryTopic.getId(), queryTopic);
        }
    }

    /**
     * 分页查询主题列表
     *
     * @param topicPageVo 查询参数
     * @param userId      用户ID
     * @return 主题列表
     */
    public PageResult<QueryTopicVo.PageResultVo> pageList(QueryTopicVo.QueryTopicPageVo topicPageVo, String userId) {
        Page<QueryTopic> topicPage = new Page<>(topicPageVo.getPageNo(), topicPageVo.getPageSize());
        topicPage.setOptimizeCountSql(false);
        QueryWrapper<QueryTopic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("if_deleted", false)
                .like(StringUtils.isNotBlank(topicPageVo.getKeyWord()), "subject", topicPageVo.getKeyWord());
        //排序
        if (StringUtils.isNotBlank(topicPageVo.getColumn()) && StringUtils.isNotBlank(topicPageVo.getSortType())) {
            queryWrapper.last("order by " + StrUtil.toSymbolCase(topicPageVo.getColumn(), '_') + " " + topicPageVo.getSortType());
        } else {
            queryWrapper.orderByDesc("create_time");
        }
        IPage<QueryTopic> pageVo = super.page(topicPage, queryWrapper);
        List<QueryTopicVo.PageResultVo> list = BeanUtil.copyList(pageVo.getRecords(), QueryTopicVo.PageResultVo.class);
        for (QueryTopicVo.PageResultVo resultVo : list) {
            UserDTO userDTO = userRpcService.getUserInfoById(StringUtils.isNotBlank(resultVo.getUserId()) ? resultVo.getUserId() : "");
            if (null != userDTO) {
                resultVo.setUsername(StringUtils.isNotBlank(userDTO.getRealName()) ? userDTO.getRealName() : userDTO.getThirdUserId());
                resultVo.setAvatarUri(userDTO.getAvatarUri());
            }
        }
        PageResult<QueryTopicVo.PageResultVo> pageResult = new PageResult<>();
        pageResult.setPageNo(topicPage.getCurrent());
        pageResult.setTotal(pageVo.getTotal());
        pageResult.setPageSize(topicPage.getSize());
        pageResult.setList(list);
        return pageResult;
    }

    /**
     * 根据公司ID、用户ID和维度对象ID查询主题列表
     *
     * @param metricDimId 维度对象ID
     * @param openId      用户ID
     * @param companyId   公司ID
     * @return 主题列表
     */
    public List<QueryTopic> selectListByMetricDimId(String metricDimId, String openId, String companyId) {
        QueryWrapper<QueryTopic> wrapper = new QueryWrapper<>();
        wrapper.eq("com_id", companyId).eq("user_id", openId).eq("dim_id", metricDimId).eq("if_deleted", false);
        return super.list(wrapper);
    }

    /**
     * 根据台账ID 更新主题列表
     *
     * @param topicList 主题列表
     * @param rawId     台账ID
     */
    public void updateBatchWithMetricDimId(List<QueryTopic> topicList, String rawId) {
        if (!topicList.isEmpty()) {
            topicList.forEach(topic -> topic.setRawId(rawId));
            super.updateBatchById(topicList);
        }
    }

    public List<RawVo.QueryTopicVo> browserDetailByRawId(String rawId) {
        List<RawVo.QueryTopicVo> res = new ArrayList<>();
        QueryWrapper<QueryTopic> wrapper = new QueryWrapper<>();
        wrapper.select("id", "subject", "update_time", "create_by", "data_parse")
                .eq("raw_id", rawId)
                .eq("if_deleted", false)
                .eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("user_id", CurrentUserUtil.get().getId())
                .orderByDesc("update_time");
        List<QueryTopic> topicList = super.list(wrapper);
        if (!topicList.isEmpty()) {
            for (QueryTopic topic : topicList) {
                RawVo.QueryTopicVo queryTopicVo = new RawVo.QueryTopicVo();
                queryTopicVo.setTopicId(topic.getId());
                queryTopicVo.setTopicName(topic.getSubject());
                queryTopicVo.setUpdateAt(topic.getUpdateTime());
                queryTopicVo.setUsername(topic.getCreateBy());
                queryTopicVo.setDataParse(topic.getDataParse());
                res.add(queryTopicVo);
            }
        }
        return res;
    }

    public List<RawVo.QueryTopicVo> browserDetailByRawIdExceptMe(String rawId) {
        List<RawVo.QueryTopicVo> res = new ArrayList<>();
        QueryWrapper<QueryTopic> wrapper = new QueryWrapper<>();
        wrapper.select("id", "subject", "update_time", "create_by", "data_parse")
                .eq("raw_id", rawId)
                .eq("if_deleted", false)
                .eq("company_id", CurrentUserUtil.get().getCompanyId())
                .ne("user_id", CurrentUserUtil.get().getId())
                .orderByDesc("update_time");
        List<QueryTopic> topicList = super.list(wrapper);
        if (!topicList.isEmpty()) {
            for (QueryTopic topic : topicList) {
                RawVo.QueryTopicVo queryTopicVo = new RawVo.QueryTopicVo();
                queryTopicVo.setTopicId(topic.getId());
                queryTopicVo.setTopicName(topic.getSubject());
                queryTopicVo.setUpdateAt(topic.getUpdateTime());
                queryTopicVo.setUsername(topic.getCreateBy());
                queryTopicVo.setDataParse(topic.getDataParse());
                res.add(queryTopicVo);
            }
        }
        return res;
    }

    /**
     * 更新最近使用时间
     *
     * @param id 自定义查询ID
     */
    public void updateLastTime(String id) {
        UpdateWrapper<QueryTopic> wrapper = new UpdateWrapper<>();
        wrapper.set("last_time", new Date()).eq("id", id);
        super.update(wrapper);
    }

    /**
     * 获取总条数
     *
     * @return map
     */
    public Map<String, Integer> getTotal(String userId) {
        // 获取由我创建总数
        QueryWrapper<QueryTopic> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("user_id", userId);
        int mineTotal = super.count(wrapper);
        // 获取分享给我条数
        int shareMeTotal = topicShareService.getShareMeTotal(userId, true);
        // 获取分享给我未读条数
        int shareMeUnReadTotal = topicShareService.getShareMeTotal(userId, false);
        // 获取最近使用条数-- 根据我的创建和我的已读分享中获取前20条
        List<TopicShareVo.RecentResponse> list = getRecentList(userId);

        Map<String, Integer> res = new LinkedHashMap<>(4);
        // 最近使用
        res.put("recent", Math.min(list.size(), 20));
        // 由我创建
        res.put("mine", mineTotal);
        // 分享给我-总数
        res.put("shareAll", shareMeTotal);
        // 分享给我-未读
        res.put("unread", shareMeUnReadTotal);
        return res;
    }

    /**
     * 批量删除
     *
     * @param ids id集合
     * @return bool
     */
    public boolean logicDeleteBatchByIds(List<String> ids) {
        UpdateWrapper<QueryTopic> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", true).in("id", ids);
        return super.update(wrapper);
    }

    /**
     * 获取最近使用的由我创建列表 前20条
     *
     * @param userId 用户ID
     * @return list
     */
    public List<TopicShareVo.RecentResponse> getCreateRecentList(String userId) {
        List<TopicShareVo.RecentResponse> res = new ArrayList<>();
        QueryWrapper<QueryTopic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("if_deleted", false)
                .isNotNull("last_time")
                .orderByDesc("last_time")
                .last("LIMIT 20");
        List<QueryTopic> shareList = super.list(queryWrapper);
        UserDTO userDTO = null;
        for (QueryTopic queryTopic : shareList) {
            if (null == userDTO) {
                userDTO = userRpcService.getUserInfoById(userId);
            }
            TopicShareVo.RecentResponse response = new TopicShareVo.RecentResponse();
            response.setId(queryTopic.getId());
            response.setLastTime(queryTopic.getLastTime());
            response.setType(1);
            response.setSubject(queryTopic.getSubject());
            if (null != userDTO) {
                response.setUsername(StringUtils.isNotBlank(userDTO.getRealName()) ? userDTO.getRealName() : userDTO.getThirdUserId());
                response.setAvatarUri(userDTO.getAvatarUri());
            }
            res.add(response);
        }
        return res;
    }

    /**
     * 获取最近使用列表
     *
     * @param userId 用户ID
     * @return list
     */
    public List<TopicShareVo.RecentResponse> getCreateList(String userId) {
        List<TopicShareVo.RecentResponse> res = new ArrayList<>();
        QueryWrapper<QueryTopic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("if_deleted", false)
                .orderByDesc("last_time");
        List<QueryTopic> shareList = super.list(queryWrapper);
        UserDTO userDTO = null;
        for (QueryTopic queryTopic : shareList) {
            if (null == userDTO) {
                userDTO = userRpcService.getUserInfoById(userId);
            }
            TopicShareVo.RecentResponse response = new TopicShareVo.RecentResponse();
            response.setId(queryTopic.getId());
            response.setLastTime(queryTopic.getLastTime());
            response.setType(1);
            response.setUserId(queryTopic.getUserId());
            response.setSubject(queryTopic.getSubject());
            if (null != userDTO) {
                response.setUsername(StringUtils.isNotBlank(userDTO.getRealName()) ? userDTO.getRealName() : userDTO.getThirdUserId());
                response.setAvatarUri(userDTO.getAvatarUri());
            }
            res.add(response);
        }
        return res;
    }

    /**
     * 获取最近使用列表
     *
     * @param userId 用户ID
     * @return list
     */
    public List<TopicShareVo.RecentResponse> getRecentList(String userId) {
        // 获取由我创建列表
        List<TopicShareVo.RecentResponse> createRecentList = getCreateRecentList(userId);
        // 获取分享给我列表
        List<TopicShareVo.RecentResponse> shareRecentList = topicShareService.getShareRecentList(userId);
        List<TopicShareVo.RecentResponse> res = new ArrayList<>(createRecentList);
        res.addAll(shareRecentList);
        res.sort(Comparator.comparing(TopicShareVo.RecentResponse::getLastTime, Comparator.reverseOrder()));
        return res.size() > 20 ? res.subList(0, 20) : res;
    }

    public List<QueryTopic> getTopicListByUserId(String userId, String keyWord) {
        QueryWrapper<QueryTopic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("if_deleted", false)
                .like(StrUtil.isNotBlank(keyWord), "name", keyWord)
                .orderByDesc("create_time");
        return super.list(queryWrapper);
    }

    public List<TopicShareVo.RecentResponse> getTopicAllByUserId(String userId, String keyWord) {
        // 获取由我创建列表
        List<TopicShareVo.RecentResponse> createList = getCreateList(userId);
        // 获取分享给我列表
        List<TopicShareVo.RecentResponse> shareRecentList = topicShareService.getShareList(userId);
        List<TopicShareVo.RecentResponse> res = new ArrayList<>(createList);
        res.addAll(shareRecentList);
        return Optional.ofNullable(keyWord)
                .map(s -> res.stream()
                        .filter(item -> item.getSubject().contains(s))
                        .collect(Collectors.toList()))
                .orElse(res);
    }

    /**
     * 判断rawId是否被使用
     *
     * @param rawId 台账 ID
     * @return boolean
     */
    public boolean hasTopicUsedWithRawId(String rawId) {
        QueryWrapper<QueryTopic> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("raw_id", rawId)
                .eq("if_deleted", false);
        return super.count(queryWrapper) > 0;
    }
}