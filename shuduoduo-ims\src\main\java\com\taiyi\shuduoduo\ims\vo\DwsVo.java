package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import com.taiyi.shuduoduo.ims.entity.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class DwsVo {

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DetailVo extends Dws {

        private String createUserName;

        private String updateUserName;

        /**
         * 关联字段列表
         */
        private List<DimensionUnion> unionList;

        /**
         * 标签列表
         */
        private List<LabelVo> labelList;

        /**
         * 是否认证 0：未认证 1：已认证
         */
        private String ifCertified;

        /**
         * 认证状态 0: 未通过 1：已通过
         */
        private String authStatus;

    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DwsPageVo extends CommonMySqlPageVo {

        private String id;

        /**
         * 板块ID
         */
        private String plateId;

        /**
         * 数据域ID
         */
        private String dataFieldId;

        /**
         * 业务过程ID
         */
        private String busProcessId;

        /**
         * 开始时间
         */
        private Date beginTime;

        /**
         * 结束时间
         */
        private Date endTime;

        /**
         * 指标级别
         */
        private String level;

        /**
         * 认证参数
         */
        @Min(1)
        @Max(4)
        private Integer authType;

        /**
         * 标签ID集合
         */
        private List<String> labelIds;

        /**
         * 类型 1：默认 2：认证指标 3：订阅指标 4：最近浏览
         */
        private Integer queryType = 1;
    }

    @Data
    public static class PageResponseVo {
        private String id;

        /**
         * 板块ID
         */
        private String plateId;
        /**
         * 指标名称
         */
        private String name;

        private String code;

        /**
         * 指标描述
         */
        private String description;

        private Long orderBy;

        private Integer total;

        private Date updateTime;

        /**
         * 技术负责人，以‘，’分割
         */
        private String technicalLeaders;

        /**
         * 技术负责人姓名，以‘，’分割
         */
        private String technicalLeaderNames;

        /**
         * 技术负责人头像，以‘，’分割
         */
        private String technicalLeaderAvatarUris;

        /**
         * 业务负责人：以‘，’分割
         */
        private String businessLeaders;

        /**
         * 业务负责人姓名：以‘，’分割
         */
        private String businessLeaderNames;

        /**
         * 业务负责人头像：以‘，’分割
         */
        private String businessLeaderAvatarUris;

        /**
         * 指标类型：1、基础指标2、衍生指标3、复合指标
         */
        private Integer type;

        /**
         * 更新人
         */
        private String updatedBy;

        private String createdBy;

        /**
         * 指标级别
         */
        private String level;

        /**
         * 是否发布
         */
        private Boolean ifPublished;

        private String createUserName;

        private String updateUserName;

        private String realName;

        private String avatarUri;

        /**
         * 指标定义认证
         */
        private DwsAuthVo.Detail basicAuth;

        /**
         * 指标数据认证
         */
        private DwsAuthVo.Detail dataAuth;

        /**
         * 是否认证 0：未认证 1：已认证
         */
        private String ifCertified;

        /**
         * 认证状态 0: 未通过 1：已通过
         */
        private String authStatus;

        /**
         * 标签
         */
        private List<LabelVo.DwsDetail> labelList;
    }

    @Data
    public static class InsertParam {

        private String id;

        /**
         * 公司ID
         */
        private String companyId;

        /**
         * 板块ID
         */
        private String plateId;

        /**
         * 数据域ID
         */
        private String dataFieldId;

        /**
         * 业务过程ID
         */
        private String busProcessId;

        /**
         * 指标名称
         */
        private String name;

        /**
         * 指标别名，以“，”分割
         */
        private String alias;

        /**
         * 指标编码
         */
        private String code;

        /**
         * 英文简称
         */
        private String enAbbr;

        /**
         * 英文名称
         */
        private String enName;

        /**
         * 指标类型：1、基础指标2、衍生指标3、复合指标
         */
        @NotNull
        @Min(1)
        @Max(3)
        private Integer type;

        /**
         * 指标级别
         */
        private String level;

        /**
         * 指标描述
         */
        private String description;

        /**
         * 数据格式
         */
        private String dataFormat;

        /**
         * 单位
         */
        private String dataUnit;

        /**
         * 业务负责人：以‘，’分割
         */
        private String businessLeaders;

        /**
         * 业务流向图在线文档地址
         */
        private String businessOnlineUri;

        /**
         * 业务规则
         */
        private String businessRole;

        /**
         * 数据来源页面地址
         */
        private String dataSourcePage;

        /**
         * 数据来源表名称
         */
        private String dataSourceTable;

        /**
         * 数据来源表字段名称
         */
        private String dataSourceTableField;

        /**
         * 统计维度：以“，”分割
         */
        private String dimIds;

        /**
         * 锚定值
         */
        private String anchorValue;

        /**
         * 应用系统
         */
        private String applicationSystem;

        /**
         * 基础指标：以“，”分割
         */
        private String basicDws;

        /**
         * 脑图口径
         */
        private String brainMap;

        /**
         * 指标文档
         */
        private String document;

        private String etlDesc;


        /**
         * 表达式
         */
        private String expressions;

        /**
         * 是否删除
         */
        private Boolean ifDeleted;

        /**
         * 是否发布
         */
        private Boolean ifPublished;

        /**
         * 排序值
         */
        private Long orderBy;

        /**
         * 统计周期维度，以“，”分割
         */
        private String periodValue;

        /**
         * 业务负责部门：以“，”分割
         */
        private String plateDeptIds;

        /**
         * 数据来源系统：以“，”分割
         */
        private String plateSourceIds;

        /**
         * 参考标准
         */
        private String referStandard;

        /**
         * 参考标准名称
         */
        private String referStandardName;

        /**
         * 相关调度
         */
        private String relatedDispatch;

        /**
         * 指标唯一标识
         */
        private String sno;

        /**
         * 统计频度
         */
        private String statistics;

        /**
         * 技术口径
         */
        private String technicalCaliber;

        /**
         * 技术负责部门，以‘，’分割
         */
        private String technicalDepts;

        /**
         * 技术负责人，以‘，’分割
         */
        private String technicalLeaders;

        /**
         * 创建人
         */
        private String createdBy;

        /**
         * 更新人
         */
        private String updatedBy;

        /**
         * 可用分析
         */
        private String usableAnalysis;

        /**
         * 版本号
         */
        private Long versionNum;

        private List<DimensionUnion> unionList;

        private List<LabelVo> labelList;
    }

    @Data
    public static class DimensionUnion {

        /**
         * 关联维度/事实表字段ID
         */
        private String dimAttrId;

        /**
         * 关联维度/事实表ID
         */
        private String dimId;

        /**
         * 关联的维度信息
         */
        private String dimension;

        /**
         * 关联指标ID
         */
        private String dwsId;

        /**
         * 排序
         */
        private Long orderBy;

        /**
         * 统计周期
         */
        private String periodValue;

        /**
         * 挂接类型
         */
        private String unionType;

        /**
         * 可用分析
         */
        private String analysis;

    }

    @Data
    public static class SearchResponse {

        private String id;

        private String enName;

        private String name;

        private String alias;
    }

    @Data
    public static class OperationResponse {
        private String id;

        private String name;

        private Integer type;

        private String typeName;

        private String username;

        private String actionUser;

        private Date createTime;
    }

    @Data
    public static class QueryResponse {
        private String name;
        private String enName;
    }

    @Data
    public static class DataFieldResponse {

        private String dataFieldId;

        private String dataFieldName;

        private List<DwsVo.SearchResponse> dwsList;

    }

    @Data
    public static class ReportDwsDetail {

        private String id;

        private String name;

        private String code;

        private String factId;

        private String factAttrId;

        /**
         * 统计周期表ID
         */
        private String dimId;

        /**
         * 统计周期ID
         */
        private String periodId;

        /**
         * 统计单位
         */
        private String periodValue;

        /**
         * 单位
         */
        private String dataUnit;

        /**
         * 数据格式
         */
        private String dataFormat;

    }

    /**
     * 指标查询参数
     */
    @Data
    public static class QueryDataRequest {

        /**
         * 指标订阅主题ID
         */
        private String topicId;

        /**
         * 指标订阅主题ID
         */
        private String reportDwsId;

        /**
         * 指标ID
         */
        private String dwsId;

        /**
         * 事实表ID
         */
        private String factId;

        /**
         * 事实表字段ID
         */
        private String factAttrId;

        /**
         * 统计周期表ID
         */
        private String dimId;

        /**
         * 统计周期ID
         */
        private String periodId;

        /**
         * 时间
         */
        private Date date;

        /**
         * 统计周期单位
         */
        private String periodUnit;

        /**
         * 筛选条件
         */
        private List<SqlBuilderVo.Filter> filters;
    }

    /**
     * 查询结果参数
     */
    @Data
    public static class QueryDataResponse {
        /**
         * 当前统计
         */
        private Object total;
        /**
         * 上一
         */
        private Object momTotal;
        /**
         * 上二
         */
        private Object before2Total;
        /**
         * 上三
         */
        private Object before3Total;
        /**
         * 上四
         */
        private Object before4Total;
        /**
         * 上五
         */
        private Object before5Total;
        /**
         * 同比 (今年的统计数据-去年的统计数据)/ 去年的统计数据 * 100%
         */
        private String yoyChange;
        /**
         * 环比 (本月的统计数据-上月的统计数据)/ 上月的统计数据 * 100%
         */
        private String momChange;
    }

    /**
     * 指标基础信息
     */
    @Data
    public static class BasicInfo {
        private String id;

        private String name;

        private String code;

        /**
         * 数据格式 json
         */
        private String dataFormat;

        /**
         * 数据单位
         */
        private String dataUnit;

    }

    /**
     * 开发API返回结果
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class OpenAPIResponseDetailVo extends Dws {

        private String createUserName;

        private String updateUserName;

        /**
         * 是否认证 0：未认证 1：已认证
         */
        private String ifCertified;

        /**
         * 认证状态 0: 未通过 1：已通过
         */
        private String authStatus;

        /**
         * 标签列表
         */
        private List<LabelVo.DwsDetail> labelList;

        /**
         * 认证信息
         */
        private Map<String, Object> authInfo;

        /**
         * 来源系统
         */
        private List<PlateSource> plateSourceList;

        /**
         * 板块
         */
        private Plate plate;

        /**
         * 数据域
         */
        private DataField dataField;

        /**
         * 业务过程
         */
        private BusinessProcess busProcess;

        /**
         * 维度列表
         */
        private List<Dimension> dimensionList;

        /**
         * 统计周期列表
         */
        private List<Dimension> periodList;

        /**
         * 基础指标列表
         */
        private List<Dws> basicDwsList;

        /**
         * 负责人列表
         */
        private List<UserListResponse> userList;

        /**
         * 负责部门列表
         */
        private List<PlateControlDept> deptList;

        /**
         * 数据挂接列表
         */
        private List<DimDwsVo.DetailVo> dimDwsList;

    }

    @Data
    public static class UserListResponse {
        private String id;
        private Boolean ifAdmin;
        private String name;
        private String thirdUserId;
        private String realName;
    }

    @Data
    public static class DwsPeriodList extends Dws {
        private List<Dimension> periodList;
    }
}