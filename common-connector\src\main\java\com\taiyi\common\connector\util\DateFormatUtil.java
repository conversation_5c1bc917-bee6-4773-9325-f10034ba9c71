package com.taiyi.common.connector.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Date;

/**
 * 日期工具类
 *
 * <AUTHOR>
 */
public class DateFormatUtil {

    public static String dateFormat(Date date) {
        String formatDate = "";
        String dateStr = date.toString();
        //去掉分隔符
        dateStr = dateStr
                // 去掉 "-"
                .replace(StrUtil.DASHED, StrUtil.EMPTY)
                // 去掉 "/"
                .replace(StrUtil.SLASH, StrUtil.EMPTY)
                // 去掉 " "
                .replace(StrUtil.SPACE, StrUtil.EMPTY)
                // 去掉 "."
                .replace(StrUtil.DOT, StrUtil.EMPTY)
                // 去掉 ":"
                .replace(StrUtil.COLON, StrUtil.EMPTY);
        int length = dateStr.length();
        if (length == 8) {
            //年月日
            formatDate = DateUtil.format(date, "yyyy-MM-dd");
        } else if (length == 12) {
            //年月日 时分
            formatDate = DateUtil.format(date, "yyyy-MM-dd HH:mm");
        } else if (length == 14) {
            //年月日 时分秒
            formatDate = DateUtil.format(date, "yyyy-MM-dd HH:mm:ss");
        } else if (length > 14) {
            //年月日 时分秒毫秒
            formatDate = DateUtil.format(date, "yyyy-MM-dd HH:mm:ss.SSS");
        } else {
            formatDate = date.toString();
        }
        return formatDate;
    }
}
