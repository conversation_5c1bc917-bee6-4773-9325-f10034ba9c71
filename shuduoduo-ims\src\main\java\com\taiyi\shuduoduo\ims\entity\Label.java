package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_label")
public class Label extends CommonMySqlEntity {

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 标签编码
     */
    private String code;

    /**
     * 标签名称
     */
    private String name;

    private Integer type;

    private String description;

    /**
     * 排序
     */
    private Long orderBy;

    private String createdBy;

    private String updatedBy;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

}