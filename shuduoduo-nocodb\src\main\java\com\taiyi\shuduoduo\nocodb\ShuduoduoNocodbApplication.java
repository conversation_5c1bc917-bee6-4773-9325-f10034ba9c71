package com.taiyi.shuduoduo.nocodb;

import com.taiyi.common.entity.MicroServer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableFeignClients(MicroServer.BASE_PACKAGES)
@MapperScan(basePackages = {"com.taiyi.shuduoduo.nocodb.dao"})
public class ShuduoduoNocodbApplication {
    public static void main(String[] args) {
        SpringApplication.run(ShuduoduoNocodbApplication.class, args);
    }
}
