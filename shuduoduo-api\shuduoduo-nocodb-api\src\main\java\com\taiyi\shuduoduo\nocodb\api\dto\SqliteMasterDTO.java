package com.taiyi.shuduoduo.nocodb.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class SqliteMasterDTO {

    private String name;

    @Data
    public static class QueryVo {

        @Data
        public static class QueryParam{

            /**
             * 表名
             */
            @NotBlank
            private String tableName;

            /**
             * 列名
             */
            @NotNull
            private List<String> columnName;

            private List<Filter> filters;
        }

        @Data
        public static class Filter{

            private String column;

            private String op;

            private Object value;

        }
    }
}
