package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import com.taiyi.shuduoduo.ims.entity.RawLicense;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 台账
 *
 * <AUTHOR>
 */
@ApiModel("台账实体类")
@Data
public class RawVo {

    /**
     * 台账新增参数
     */
    @ApiModel("台账新增参数")
    @Data
    public static class InsertParam {

        /**
         * 台账主键ID
         */
        @ApiModelProperty("台账主键ID")
        private String id;

        /**
         * 板块ID
         */
        @ApiModelProperty("板块ID")
        private String plateId;

        private Long orderBy;

        /**
         * 维度文件夹ID
         */
        @ApiModelProperty("维度文件夹ID")
        private String dimFolderId;

        /**
         * 数据域ID
         */
        @ApiModelProperty("数据域ID")
        private String dataFieldId;

        /**
         * 业务过程ID
         */
        @ApiModelProperty("业务过程ID")
        private String busProcessId;

        /**
         * 台账名称
         */
        @ApiModelProperty("台账名称")
        private String name;

        /**
         * 描述
         */
        @ApiModelProperty("描述")
        private String description;

        /**
         * 管理部门ID集合
         */
        @ApiModelProperty("管理部门ID集合")
        private List<String> controlDepts;

        /**
         * 来源系统ID集合
         */
        @ApiModelProperty("来源系统ID集合")
        private List<String> sources;

        /**
         * 台账表及表字段列表
         */
        @ApiModelProperty("台账表及表字段列表")
        private List<RawTableVo.InsertParam> rowTables;

        /**
         * 台账关联字段列表
         */
        @ApiModelProperty("台账关联字段列表")
        private List<RawForeignKeyVo.InsertParam> foreignKeys;

        /**
         * 台账关联对象列表
         */
        @ApiModelProperty("台账关联对象列表")
        private List<RawObjectVo.InsertParam> objects;

    }

    /**
     * 台账详情参数
     */
    @Data
    public static class DetailResponse {
        /**
         * 台账主键ID
         */
        @ApiModelProperty("台账主键ID")
        private String id;

        /**
         * 板块ID
         */
        @ApiModelProperty("板块ID")
        private String plateId;

        /**
         * 维度文件夹ID
         */
        @ApiModelProperty("维度文件夹ID")
        private String dimFolderId;

        /**
         * 数据域ID
         */
        @ApiModelProperty("数据域ID")
        private String dataFieldId;

        /**
         * 业务过程ID
         */
        @ApiModelProperty("业务过程ID")
        private String busProcessId;

        /**
         * 台账名称
         */
        @ApiModelProperty("台账名称")
        private String name;

        /**
         * 描述
         */
        @ApiModelProperty("描述")
        private String description;

        /**
         * 是否开启认证
         */
        @ApiModelProperty("是否开启认证")
        private Boolean ifAuth;

        /**
         * 管理部门ID集合
         */
        @ApiModelProperty("管理部门ID集合")
        private List<String> controlDepts;

        /**
         * 来源系统ID集合
         */
        @ApiModelProperty("来源系统ID集合")
        private List<String> sources;

        /**
         * 台账表及表字段列表
         */
        @ApiModelProperty("台账表及表字段列表")
        private List<RawTableVo.InsertParam> rowTables;

        /**
         * 台账关联字段列表
         */
        @ApiModelProperty("台账关联字段列表")
        private List<RawForeignKeyVo.InsertParam> foreignKeys;

        /**
         * 台账关联对象列表
         */
        @ApiModelProperty("台账关联对象列表")
        private List<RawObjectVo.InsertParam> objects;
    }

    /**
     * 台账分页参数
     */
    @Data
    public static class RawPageVo extends CommonMySqlPageVo {

        private String plateId;

        private String dimFolderId;

        private String dataFieldId;

        private String busProcessId;

        private String sourceId;

        private String controlDeptId;

        /**
         * 1、默认 2、最近使用 3、常用 4、最新
         */
        private Integer type = 1;

        private String id;
    }

    /**
     * 搜索列表参数
     */
    @Data
    public static class SearchParam {

        /**
         * 关键字搜索
         */
        @NotBlank
        public String keyWord;

    }

    /**
     * 搜索返回参数
     */
    @Data
    public static class SearchResponse {
        private String id;

        private String name;

        private String attrName;

    }

    /**
     * 分页返回参数
     */
    @Data
    public static class PageResponse {
        private String id;

        /**
         * 指标表ID
         */
        private String dwsId;

        /**
         * 通知信息
         */
        private String noticeMsg;

        /**
         * 板块ID
         */
        private String plateId;

        /**
         * 维度文件夹ID
         */
        private String dimFolderId;

        /**
         * 数据域ID
         */
        private String dataFieldId;

        /**
         * 业务过程ID
         */
        private String busProcessId;

        /**
         * 名称
         */
        private String name;

        /**
         * 描述
         */
        private String description;

        /**
         * 类型 1、新建 2、开通
         */
        private Integer type;

        /**
         * 排序字段
         */
        private Long orderBy;

        /**
         * 目录
         */
        private String target;

        /**
         * 常用统计
         */
        private Integer total;

        /**
         * 认证信息
         */
        private RawLicense rawLicense;
    }

    /**
     * 列表返回参数
     */
    @Data
    public static class ListResponse {

        private String id;

        private String name;

        private Boolean ifAuth;
    }

    /**
     * 浏览器台账详情参数
     */
    @Data
    public static class BrowserDetail {

        private String id;

        private String name;

        /**
         * 描述
         */
        private String description;

        private String createBy;

        private List<QueryTopicVo> createByMyself;

        private List<QueryTopicVo> createByOther;

        private List<RawObjectVo.DetailResponse> objects;

        private List<RawTableAttrVo.DetailResponse> rowTableAttrs;
    }

    @Data
    public static class QueryTopicVo {

        private String topicId;

        private String topicName;

        private Date updateAt;

        private String username;

        /**
         * 所选数据格式转换
         */
        private String dataParse;

    }

}
