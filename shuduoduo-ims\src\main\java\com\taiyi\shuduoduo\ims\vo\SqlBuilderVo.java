package com.taiyi.shuduoduo.ims.vo;

import cn.hutool.db.sql.Direction;
import lombok.Data;

import java.util.List;

/**
 * 构建SQL 实体
 *
 * <AUTHOR>
 */
@Data
public class SqlBuilderVo {

    /**
     * 求和
     */
    public static final String SUM = "SUM";

    /**
     * 最小值
     */
    public static final String MIN = "MIN";

    /**
     * 最大值
     */
    public static final String MAX = "MAX";

    /**
     * 平均值
     */
    public static final String AVG = "AVG";


    /**
     * 排序类
     */
    @Data
    public static class Order {

        private String code;

        private Direction sortType;
    }

    /**
     * 筛选类
     */
    @Data
    public static class Filter {
        /**
         * 字段
         */
        private String field;
        /**
         * 运算符（大于号，小于号，等于号 like 等）
         */
        private String operator;
        /**
         * 值
         */
        private Object value;
        /**
         * 是否使用条件值占位符
         */
        private boolean isPlaceHolder = true;
        /**
         * between firstValue and secondValue
         */
        private Object secondValue;
    }

    /**
     * 关联类
     */
    @Data
    public static class Join {
        /**
         * 主表
         */
        private String mainTable;

        /**
         * 关联类型
         */
        private String joinType;

        private List<On> joinList;
    }

    @Data
    public static class On {
        /**
         * 关联表
         */
        private String joinTable;
        /**
         * 关联条件
         */
        private String joinCondition;

        /**
         * and 条件
         */
        private String andCondition;
        /**
         * 主表字段
         */
        private String mainField;
        /**
         * 关联表字段
         */
        private String joinField;
    }
}
