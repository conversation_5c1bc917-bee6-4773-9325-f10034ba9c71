package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_dws_operation_log")
public class DwsOperationLog extends CommonMySqlEntity {
    /**
     * 执行人
     */
    private String actionUser;

    /**
     * 指标ID
     */
    private String dwsId;

    /**
     * 操作的类型，例如“新增”、“编辑”、“发布”或“下线”。
     */
    private Integer type;

}