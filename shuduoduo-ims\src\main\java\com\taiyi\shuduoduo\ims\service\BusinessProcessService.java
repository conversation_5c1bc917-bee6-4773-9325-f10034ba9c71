package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.BusinessProcessDao;
import com.taiyi.shuduoduo.ims.entity.BusinessProcess;
import com.taiyi.shuduoduo.ims.entity.DataField;
import com.taiyi.shuduoduo.ims.entity.Plate;
import com.taiyi.shuduoduo.ims.vo.BusinessProcessVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class BusinessProcessService extends CommonMysqlService<BusinessProcessDao, BusinessProcess> {

    @Autowired
    private PlateService plateService;

    @Autowired
    private DataFieldService dataFieldService;

    @Autowired
    private BusinessProcessDao businessProcessDao;

    @Autowired
    private DwsService dwsService;

    @Override
    public Class<BusinessProcess> getEntityClass() {
        return BusinessProcess.class;
    }

    /**
     * 保存或者更新业务过程
     *
     * @param businessProcess 实体
     * @return 布尔值
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addBusinessProcess(BusinessProcess businessProcess) {
        businessProcess.setCompanyId(CurrentUserUtil.get().getCompanyId());
        if (StringUtils.isNotBlank(businessProcess.getId())) {
            return super.updateById(businessProcess.getId(), businessProcess);
        }
        businessProcess.setType(1);
        businessProcess.setOrderBy(this.getMaxOrder());
        return super.save(businessProcess);
    }

    public Long getMaxOrder() {
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).orderByDesc("order_by").last("LIMIT 1");
        BusinessProcess businessProcess = super.getOne(wrapper);
        return null == businessProcess ? 1L : businessProcess.getOrderBy() + 1;
    }

    /**
     * 分页查询
     *
     * @param pageVo 分页
     * @return 分页
     */
    public PageResult<BusinessProcessVo.AddBusinessProcess> myPage(BusinessProcessVo.PageVo pageVo) {
        Page<BusinessProcess> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        page.setOptimizeCountSql(false);
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("bus.if_deleted", false);
        if (StringUtils.isBlank(pageVo.getDataFieldId())) {
            //根据公司ID查询板块列表
            List<Plate> plateList = plateService.getListByCompanyId(CurrentUserUtil.get().getCompanyId());
            if (ObjectUtil.isEmpty(plateList)) {
                return new PageResult<>();
            }
            List<String> plateIds = new ArrayList<>();
            plateList.forEach(plate -> plateIds.add(plate.getId()));
            // 根据板块ID查询数据域列表
            List<DataField> dataFieldList = dataFieldService.getListByPlatedIds(plateIds);
            if (ObjectUtil.isEmpty(dataFieldList)) {
                return new PageResult<>();
            }
            List<String> dataFieldIds = new ArrayList<>();
            dataFieldList.forEach(dataField -> dataFieldIds.add(dataField.getId()));
            wrapper.in("bus.data_field_id", dataFieldIds);
        } else {
            wrapper.eq("bus.data_field_id", pageVo.getDataFieldId());
        }
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.and(queryWrapper -> queryWrapper.like("bus.name", pageVo.getKeyWord())
                    .or()
                    .like("bus.code", pageVo.getKeyWord())
                    .or()
                    .like("d.name", pageVo.getKeyWord()));
        }
        if (StringUtils.isNotBlank(pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getSortType())) {
            wrapper.last("order by " + pageVo.getColumn() + " " + pageVo.getSortType());
        } else {
            wrapper.orderByAsc("bus.order_by");
        }
        IPage<BusinessProcessVo.AddBusinessProcess> iPage = businessProcessDao.selectBusPage(page, wrapper);
        PageResult<BusinessProcessVo.AddBusinessProcess> pageResult = new PageResult<>();
        pageResult.setPageNo(iPage.getCurrent());
        pageResult.setPageSize(iPage.getSize());
        pageResult.setTotal(iPage.getTotal());
        pageResult.setList(iPage.getRecords());
        return pageResult;
    }

    /**
     * 通过数据域ID查询业务过程
     *
     * @param dataFieldId 数据域ID
     * @return List
     */
    public List<Map<String, String>> queryProcessPlate(String dataFieldId, String companyId) {
        List<Map<String, String>> resList = new ArrayList<>();
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0);
        if (StringUtils.isBlank(dataFieldId)) {
            //根据公司ID查询板块列表
            List<Plate> plateList = plateService.getListByCompanyId(companyId);
            if (ObjectUtil.isEmpty(plateList)) {
                return new ArrayList<>();
            }
            List<String> plateIds = new ArrayList<>();
            plateList.forEach(plate -> plateIds.add(plate.getId()));
            // 根据板块ID查询数据域列表
            List<DataField> dataFieldList = dataFieldService.getListByPlatedIds(plateIds);
            if (ObjectUtil.isEmpty(dataFieldList)) {
                return new ArrayList<>();
            }
            List<String> dataFieldIds = new ArrayList<>();
            dataFieldList.forEach(dataField -> dataFieldIds.add(dataField.getId()));
            wrapper.in("data_field_id", dataFieldIds);
        } else {
            wrapper.eq("data_field_id", dataFieldId);
        }
        wrapper.orderByAsc("order_by");
        List<BusinessProcess> list = this.list(wrapper);
        for (BusinessProcess businessProcess : list) {
            Map<String, String> map = new HashMap<>();
            map.put("id", businessProcess.id);
            map.put("name", businessProcess.getName());
            map.put("dataFieldId", businessProcess.getDataFieldId());
            resList.add(map);
        }
        return resList;
    }

    public List<Map<String, Object>> queryProcessAndDwsCountByDataFieldId(String dataFieldId) {
        List<Map<String, Object>> resList = new ArrayList<>();
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0);
        if (StringUtils.isBlank(dataFieldId)) {
            //根据公司ID查询板块列表
            List<Plate> plateList = plateService.getListByCompanyId(CurrentUserUtil.get().getCompanyId());
            if (ObjectUtil.isEmpty(plateList)) {
                return new ArrayList<>();
            }
            List<String> plateIds = new ArrayList<>();
            plateList.forEach(plate -> plateIds.add(plate.getId()));
            // 根据板块ID查询数据域列表
            List<DataField> dataFieldList = dataFieldService.getListByPlatedIds(plateIds);
            if (ObjectUtil.isEmpty(dataFieldList)) {
                return new ArrayList<>();
            }
            List<String> dataFieldIds = new ArrayList<>();
            dataFieldList.forEach(dataField -> dataFieldIds.add(dataField.getId()));
            wrapper.in("data_field_id", dataFieldIds);
        } else {
            wrapper.eq("data_field_id", dataFieldId);
        }
        wrapper.orderByAsc("order_by");
        List<BusinessProcess> list = this.list(wrapper);
        for (BusinessProcess businessProcess : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", businessProcess.id);
            map.put("name", businessProcess.getName());
            map.put("dataFieldId", businessProcess.getDataFieldId());
            map.put("total", dwsService.getCountByRequiredId(businessProcess.getId(), 3));
            resList.add(map);
        }
        return resList;
    }

    /**
     * 数据域idList
     *
     * @return List
     */
    public List<String> getDataFieldIds(String plateId) {
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0);
        wrapper.eq("plate_id", plateId);
        wrapper.orderByAsc("order_by");
        List<BusinessProcess> list = this.list(wrapper);
        return list.stream().map(BusinessProcess::getDataFieldId).collect(Collectors.toList());
    }

    /**
     * 校验业务过程是否存在
     *
     * @param businessProcess 参数
     * @return boolean
     */
    public boolean isNameExists(BusinessProcess businessProcess) {
        QueryWrapper<BusinessProcess> dataFieldQueryWrapper = new QueryWrapper<>();
        dataFieldQueryWrapper.eq("name", businessProcess.getName())
                .eq("data_field_id", businessProcess.getDataFieldId())
                .eq("if_deleted", false)
                .ne(StringUtils.isNotBlank(businessProcess.getId()), "id", businessProcess.getId());
        return this.getOne(dataFieldQueryWrapper) != null;
    }

    /**
     * 校验业务过程是否存在
     *
     * @param businessProcess 参数
     * @return boolean
     */
    public boolean isCodeExists(BusinessProcess businessProcess) {
        QueryWrapper<BusinessProcess> dataFieldQueryWrapper = new QueryWrapper<>();
        dataFieldQueryWrapper.eq("code", businessProcess.getCode())
                .eq("data_field_id", businessProcess.getDataFieldId())
                .eq("if_deleted", false)
                .ne(StringUtils.isNotBlank(businessProcess.getId()), "id", businessProcess.getId());
        return this.getOne(dataFieldQueryWrapper) != null;
    }

    /**
     * 根据数据域ID查询业务过程列表
     *
     * @param dataFieldId 数据域ID
     * @return 业务过程列表
     */
    public List<BusinessProcess> getListByDataFieldId(String dataFieldId) {
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0);
        wrapper.eq("data_field_id", dataFieldId);
        wrapper.orderByAsc("order_by");
        return this.list(wrapper);
    }

    /**
     * 根据给定的信息判断BusinessProcess存不存在
     *
     * @param businessProcess 业务过程信息
     * @return 业务过程
     */
    public BusinessProcess isBusinessProcessExist(BusinessProcess businessProcess) {
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.setEntity(businessProcess)
                .last("LIMIT 1");
        return this.getOne(wrapper);
    }

    /**
     * 根据板块ID获取业务过程列表
     *
     * @param plateId 板块ID
     * @return 业务过程列表
     */
    public BusinessProcess getBusinessProcessListByPlateId(String plateId) {
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("plate_id", plateId)
                .last("LIMIT 1");
        return this.getOne(wrapper);
    }

    /**
     * 获取详情
     *
     * @param id id
     * @return 详情
     */
    public BusinessProcessVo.DetailResponse detail(String id) {
        BusinessProcess businessProcess = super.getById(id);
        BusinessProcessVo.DetailResponse res = BeanUtil.copy(businessProcess, BusinessProcessVo.DetailResponse.class);
        res.setPlateId(dataFieldService.getById(businessProcess.getDataFieldId()).getPlateId());
        return res;
    }

    /**
     * 根据数据域ID和业务过程编码查询业务过程信息
     *
     * @param dataFieldId 数据域ID
     * @param code        业务过程编码
     * @return 业务过程信息
     */
    public BusinessProcess getBusinessProcessByDataFieldAndCode(String dataFieldId, String code) {
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("data_field_id", dataFieldId).eq("if_deleted", 0).eq("code", code).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 查询数据总条数
     *
     * @return 总条数
     */
    public int selectTotal() {
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("company_id", CurrentUserUtil.get().getCompanyId());
        return super.count(wrapper);
    }

    public BusinessProcess getByNameAndDataFieldId(String busProcessName, String dataFieldId) {
        QueryWrapper<BusinessProcess> wrapper = new QueryWrapper<>();
        wrapper.eq("data_field_id", dataFieldId).eq("if_deleted", false).eq("name", busProcessName).last("LIMIT 1");
        return super.getOne(wrapper);
    }
}