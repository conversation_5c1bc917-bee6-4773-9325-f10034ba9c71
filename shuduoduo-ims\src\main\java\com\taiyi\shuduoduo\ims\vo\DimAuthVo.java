package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
public class DimAuthVo {

    @Data
    public static class PageVo extends CommonMySqlPageVo {

        /**
         * 维度ID
         */
        @NotBlank
        private String dimId;
    }

    @Data
    public static class PageResult {
        private String id;
        /**
         * 权限表ID
         */
        private String authTableId;
        private String authTableName;
        private boolean status;
        private String operator;
    }
}
