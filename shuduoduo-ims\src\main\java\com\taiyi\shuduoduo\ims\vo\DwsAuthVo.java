package com.taiyi.shuduoduo.ims.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class DwsAuthVo {

    /**
     * 指标ID
     */
    private String dwsId;

    /**
     * 认证人
     */
    private String authedBy;

    /**
     * 认证状态：1.未认证 2.认证成功 3. 认证失败
     */
    private Boolean status;

    /**
     * 认证意见
     */
    private String comment;

    /**
     * 类型 ：1、指标定义 2、指标数据
     */
    private Integer type;

    private Date createTime;

    @Data
    public static class AddRequestVo {
        private List<DwsAuthVo> authList;
    }

    @Data
    public static class Detail {

        private String id;

        /**
         * 认证人
         */
        private String authedBy;

        private String authName;

        /**
         * 认证状态
         */
        private Boolean status;

        /**
         * 类型 ：1、指标定义 2、指标数据
         */
        private Integer type;

        /**
         * 认证意见
         */
        private String comment;

        private Date createTime;
    }

    @Data
    public static class ListResponse {
        private String id;

        /**
         * 认证人
         */
        private String authedBy;

        private String authName;

        /**
         * 认证状态
         */
        private Boolean status;

        /**
         * 类型 ：1、指标定义 2、指标数据
         */
        private Integer type;

        /**
         * 认证意见
         */
        private String comment;

        private Date createTime;
    }

    @Data
    public static class InsertRequest {
        /**
         * 指标ID列表
         */
        @NotEmpty
        private List<String> dwsId;
        /**
         * 角色ID
         */
        @NotBlank
        private String roleId;
        /**
         * 是否开启权限
         */
        @NotNull
        private Boolean ifAuthed;
    }

}
