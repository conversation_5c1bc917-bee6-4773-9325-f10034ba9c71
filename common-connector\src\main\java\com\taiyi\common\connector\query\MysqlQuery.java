package com.taiyi.common.connector.query;

import cn.hutool.core.util.ObjectUtil;
import com.taiyi.common.connector.db.ConnectionPool;
import com.taiyi.common.connector.db.DbConnection;
import com.taiyi.common.connector.entity.DbEntity;
import com.taiyi.common.connector.entity.MysqlEntity;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Mysql数据库处理
 *
 * <AUTHOR>
 */
public class MysqlQuery implements DbQuery {

    private static Logger logger = LoggerFactory.getLogger(MysqlQuery.class);

    private MysqlEntity entity;

    DbConnection<DbEntity> dbConnection = new ConnectionPool<>();

    private ResultSet resultSet;


    public MysqlQuery(DbDto dbDto) {
        entity = BeanUtil.copy(dbDto, MysqlEntity.class);
    }


    /**
     * 获取数据库连接
     *
     * @return boolean
     */
    @Override
    public boolean getConnection() {
        return ObjectUtil.isNotEmpty(dbConnection.getConnection(entity));
    }

    /**
     * 关闭
     *
     * @throws SQLException e
     */
    @Override
    public void close() throws SQLException {
        if (ObjectUtil.isNotEmpty(resultSet)) {
            logger.info("关闭resultSet{}", resultSet);
            resultSet.close();
            logger.info("resultSet closed...");
        }
    }

    /**
     * 获取表
     *
     * @return list
     */
    @Override
    public List<String> getTables() throws SQLException {
        logger.info("获取数据库表,链接地址为{},数据库为{},数据库类型为{}", entity.getHost(), entity.getDatabase(), entity.getType());
        List<String> list = new ArrayList<>();
        resultSet = dbConnection.getConnection(entity).prepareStatement("SHOW TABLES").executeQuery();
        while (resultSet.next()) {
            list.add(resultSet.getString(1));
        }
        close();
        return list;
    }

    /**
     * 获取表字段
     *
     * @param tableName 表名
     * @return list
     */
    @Override
    public List<String> getColumns(String tableName) throws SQLException {
        logger.info("获取数据库表字段,链接地址为{},数据库为{},表名为{}", entity.getHost(), entity.getDatabase(), tableName);
        List<String> list = new ArrayList<>();
        resultSet = dbConnection.getConnection(entity).prepareStatement("SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name='" + tableName + "'").executeQuery();
        while (resultSet.next()) {
            list.add(resultSet.getString(4));
        }
        close();
        return list;
    }

    /**
     * 条件查询
     *
     * @param tableName   表名
     * @param queryColumn 查询列
     * @param queryParam  查询条件
     * @param sortParam   排序条件
     * @return list
     * @throws SQLException sqlError
     */
    @Override
    public List<Map<String, Object>> query(String tableName, List<String> queryColumn, List<Map<String, Object>> queryParam, List<Map<String, Object>> sortParam) throws SQLException {
        logger.info("数据库表查询,数据库地址为{},数据库为{},查询表为{}", entity.getHost(), entity.getDatabase(), tableName);
        StringBuilder builder = new StringBuilder();
        builder.append("select ");
        if (ObjectUtil.isEmpty(queryColumn)) {
            builder.append("* ");
        } else {
            for (String s : queryColumn) {
                builder.append(s).append(",");
            }
            builder.deleteCharAt(builder.length() - 1);
        }
        builder.append(" from ").append(tableName);
        if (ObjectUtil.isNotEmpty(queryParam)) {
            for (Map<String, Object> map : queryParam) {
                for (String key : map.keySet()) {
                    builder.append(" where ");
                    if (key.contains("like") || key.contains("LIKE")) {
                        builder.append(key).append(" '%").append(map.get(key)).append("%' ");
                    } else {
                        builder.append(key).append("'").append(map.get(key)).append("' ");
                    }
                }
            }
        }
        if (ObjectUtil.isNotEmpty(sortParam)) {
            for (Map<String, Object> map : sortParam) {
                builder.append("order by ");
                for (String key : map.keySet()) {
                    builder.append(key).append(" ").append(map.get(key));
                }
            }
        }
        logger.info("sql:{}", builder.toString());
        resultSet = dbConnection.getConnection(entity).prepareStatement(builder.toString()).executeQuery();
        return convertList(resultSet);
    }

    @Override
    public List<Map<String, Object>> querySql(String sql) throws SQLException {
        logger.info("数据库表查询,数据库地址为{},数据库为{},查询SQL为{}", entity.getHost(), entity.getDatabase(), sql);
        resultSet = dbConnection.getConnection(entity).createStatement().executeQuery(sql);
        return convertList(resultSet);
    }

    @Override
    public List<Map<String, Object>> getColumnsAndType(String tableName) {
        // TODO: 2022/7/1
        return new ArrayList<>();
    }

    /**
     * 数据集处理
     *
     * @param resultSet 结果集
     * @return List
     * @throws SQLException e
     */
    public List<Map<String, Object>> convertList(ResultSet resultSet) throws SQLException {
        List<Map<String, Object>> result = new ArrayList<>();
        ResultSetMetaData metaData = resultSet.getMetaData();
        int count = metaData.getColumnCount();
        while (resultSet.next()) {
            Map<String, Object> map = new HashMap<>();
            for (int i = 1; i <= count; i++) {
                map.put(metaData.getColumnName(i), resultSet.getObject(i));
            }
            result.add(map);
        }
        close();
        return result;
    }

    public static void main(String[] args) throws SQLException {
        DbDto dbDto = new DbDto();
        dbDto.setHost("127.0.0.1");
        dbDto.setPort(3306);
        dbDto.setDatabase("wisdom_ht");
        dbDto.setType("MYSQL");
        dbDto.setUsername("root");
        dbDto.setPassword("root");
        MysqlQuery mysqlQuery = new MysqlQuery(dbDto);
        List<String> tables = mysqlQuery.getTables();
        for (String table : tables) {
            List<String> columns = mysqlQuery.getColumns(table);
            System.out.println(table);
            columns.forEach(System.out::println);
        }
        List<String> queryColumn = new ArrayList<>();
        queryColumn.add("id");
        queryColumn.add("image");
        queryColumn.add("url");
        List<Map<String, Object>> queryParam = new ArrayList<>();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("url like", "https");
        queryParam.add(queryMap);
        List<Map<String, Object>> sortParam = new ArrayList<>();
        Map<String, Object> sortMap = new HashMap<>();
        sortMap.put("id", "desc");
        sortParam.add(sortMap);
        List<Map<String, Object>> mapList = mysqlQuery.query("ads", queryColumn, queryParam, sortParam);
        for (Map<String, Object> map : mapList) {
            map.values().forEach(System.out::println);
        }
    }
}
