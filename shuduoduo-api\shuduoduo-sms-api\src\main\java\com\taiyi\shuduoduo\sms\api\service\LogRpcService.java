package com.taiyi.shuduoduo.sms.api.service;

import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.sms.api.dto.LogDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 操作日志RPC接口
 *
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoSms.SERVER_NAME)
public interface LogRpcService {

    /**
     * 新增操作日志
     *
     * @param logDTO 日志参数
     * @return bool
     */
    @PostMapping(MicroServer.ShuduoduoSms.SERVER_PREFIX + "/rpc/log")
    boolean save(@RequestBody LogDTO logDTO);
}
