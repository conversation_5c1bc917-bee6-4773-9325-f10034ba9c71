package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.RawTableGroupAttrDao;
import com.taiyi.shuduoduo.ims.entity.RawTableGroupAttr;
import com.taiyi.shuduoduo.ims.vo.RawTableAttrVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class RawTableGroupAttrService extends CommonMysqlService<RawTableGroupAttrDao, RawTableGroupAttr> {
    @Override
    public Class<RawTableGroupAttr> getEntityClass() {
        return RawTableGroupAttr.class;
    }

    @Autowired
    private RawTableAttrService tableAttrService;

    public List<RawTableGroupAttr> listByRawTableGroupId(String rawTableGroupId) {
        QueryWrapper<RawTableGroupAttr> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(rawTableGroupId), "raw_table_group_id", rawTableGroupId).eq("if_deleted", false).orderByAsc("order_by");
        return super.list(wrapper);
    }

    public boolean logicDeleteByRawTableGroupId(String rawTableGroupId) {
        List<RawTableGroupAttr> tables = listByRawTableGroupId(rawTableGroupId);
        if (tables.isEmpty()) {
            return true;
        }
        UpdateWrapper<RawTableGroupAttr> wrapper = new UpdateWrapper<>();
        wrapper.set("if_deleted", 1)
                .eq("raw_table_group_id", rawTableGroupId);
        return this.update(wrapper);
    }

    /**
     * 根据台账分组ID和台账表字段ID 保存字段分组信息
     *
     * @param rawTableAttrId  台账表字段ID
     * @param rawTableGroupId 台账分组ID
     * @param param           字段分组信息
     * @return bool
     */
    public boolean saveBatchByRawTableId(String companyId, String rawTableAttrId, String rawTableGroupId, RawTableAttrVo.InsertParam param) {
        RawTableGroupAttr tableGroupAttr = new RawTableGroupAttr();
        tableGroupAttr.setCompanyId(companyId);
        tableGroupAttr.setRawTableGroupId(rawTableGroupId);
        tableGroupAttr.setRawTableAttrId(rawTableAttrId);
        return super.save(tableGroupAttr);
    }

    public String getGroupIdByRawTableAttrId(String rawTableAttrId) {
        QueryWrapper<RawTableGroupAttr> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false)
                .eq("raw_table_attr_id", rawTableAttrId)
                .orderByAsc("order_by").last("LIMIT 1");
        RawTableGroupAttr one = super.getOne(wrapper);
        return one == null ? null : one.getRawTableGroupId();
    }
}