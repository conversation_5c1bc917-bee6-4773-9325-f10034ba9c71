package com.taiyi.shuduoduo.ums.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.entity.MyQuery;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ums.dao.DepartmentDao;
import com.taiyi.shuduoduo.ums.entity.Department;
import com.taiyi.shuduoduo.ums.entity.User;
import com.taiyi.shuduoduo.ums.entity.UserCompany;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class DepartmentService extends CommonMysqlService<DepartmentDao, Department> {

    @Override
    public Class<Department> getEntityClass() {
        return Department.class;
    }

    @Autowired
    public UserCompanyService userCompanyService;
    @Autowired
    public UserService userService;

    /**
     * 组织架构(部门)-用户-部门下用户分页查询
     *
     * @param id 部门ID
     * @return T
     */
    public List<User> getDepartmentUserPage(String id) {
        QueryWrapper<UserCompany> wrapper = new QueryWrapper<>();
        wrapper.eq(true, "dep_id", id);
        List<UserCompany> list = userCompanyService.list(wrapper);
        List<String> idList = new ArrayList<>();
        for (UserCompany userCompany : list) {
            idList.add(userCompany.getUserId());
        }
        if (idList.isEmpty()) {
            return new ArrayList<>();
        }
        //去用户表查询用户
        return userService.listByIds(idList).stream()
                .filter(user -> user.getEmpStatus() == 1)
                .collect(Collectors.toList());
    }

    /**
     * 组织架构(部门)-用户-公司下用户分页查询
     *
     * @param id    公司id
     * @param query 分页参数
     * @return PageResult
     */
    public PageResult getCompanyUserPage(String id, MyQuery query) {
        QueryWrapper<UserCompany> wrapper = new QueryWrapper<>();
        wrapper.eq(true, "company_id", id);
        List<UserCompany> list = userCompanyService.list(wrapper);
        List<String> idList = new ArrayList<>();
        for (UserCompany userCompany : list) {
            idList.add(userCompany.getUserId());
        }
        //去用户表查询用户
        List<User> userList = userService.listByIds(idList);
        PageResult pageResult = BeanUtil.copy(query.getPage(), PageResult.class);
        pageResult.setList(userList);
        return pageResult;
    }

    /**
     * 查询部门列表 前两级部门
     *
     * @return 部门列表
     */
    public List<Node> getDeptListAsLevel2Node() {
        List<Node> node = new ArrayList<>();
        QueryWrapper<Department> wrapper = new QueryWrapper<>();
        wrapper.eq("ums_company_id", CurrentUserUtil.get().getCompanyId()).eq("pid", "0").eq("state", 0).last("limit 1");
        Department department = getOne(wrapper);
        if (null == department) {
            return node;
        }
        Node mainNode = new Node(department);
        /*if (StringUtils.isNotBlank(department.getId())) {
            //查询 二级部门
            QueryWrapper<Department> level2wrapper = new QueryWrapper<>();
            level2wrapper.eq("ums_company_id", CurrentUserUtil.get().getCompanyId()).eq("pid", department.getId()).eq("state", 0);
            List<Department> list = list(level2wrapper);
            List<Node> nodeList = new ArrayList<>();
            list.forEach(dept -> nodeList.add(new Node(dept)));
            mainNode.setChild(nodeList);
        }*/
        node.add(mainNode);
        return node;
    }

    /**
     * 组织架构(部门)-根据父部门ID查询子部门树列表
     *
     * @param pId 父部门id
     * @return 公司部门列表
     */
    public List<Node> getDepartmentListByPid(String pId) {
        QueryWrapper<Department> wrapper = new QueryWrapper<>();
        wrapper.eq("ums_company_id", CurrentUserUtil.get().getCompanyId()).eq("pid", pId).eq("state", 0);
        List<Department> list = super.list(wrapper);
        List<Node> nodeList = new ArrayList<>();
        list.forEach(dept -> nodeList.add(new Node(dept)));
        return nodeList;
    }

    /**
     * 根据部门ID 获取部门信息
     *
     * @param depId 部门ID
     * @return 部门信息
     */
    public String getDeptNameById(String depId, List<String> res) {
        Department dept = getById(depId);
        if (null != dept) {
            if ("0".equals(dept.getPid())) {
                res.add(("未知部门").equals(dept.getName()) ? dept.getDeptId() : dept.getName());
            } else {
                res.add(("未知部门").equals(dept.getName()) ? dept.getDeptId() : dept.getName());
                getDeptNameById(dept.getPid(), res);
            }
        }
        Collections.reverse(res);
        return String.join("-", res);
    }

    private static class Node {
        public String pid;
        public String id;
        public String name;
        public String deptId;
        public List<Node> child = new ArrayList<>();

        public Node(Department department) {
            this.pid = department.getPid();
            this.id = department.getId();
            this.name = department.getName();
            this.deptId = department.getDeptId();
        }

        public List<Node> getChild() {
            return child;
        }

        public void setChild(List<Node> child) {
            this.child = child;
        }

        @Override
        public String toString() {
            return "{" +
                    "pid:'" + pid + '\'' +
                    ", id:'" + id + '\'' +
                    ", name:'" + name + '\'' +
                    ", deptId:'" + deptId + '\'' +
                    ", child:" + child +
                    '}';
        }
    }
}