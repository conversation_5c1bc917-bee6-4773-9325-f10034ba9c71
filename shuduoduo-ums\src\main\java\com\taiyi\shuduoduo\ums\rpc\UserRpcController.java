package com.taiyi.shuduoduo.ums.rpc;


import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ums.api.dto.UserAuthDTO;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.entity.User;
import com.taiyi.shuduoduo.ums.service.UserAuthService;
import com.taiyi.shuduoduo.ums.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/rpc/user")
public class UserRpcController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private UserService userService;

    @Resource
    private UserAuthService userAuthService;

    /**
     * 根据 用户ID 获取用户信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @GetMapping("/getById")
    public UserDTO getUserInfoById(@RequestParam("id") String id) {
        return BeanUtil.copy(userService.getById(id), UserDTO.class);
    }

    /**
     * 根据 用户三方ID 获取用户信息
     *
     * @param companyId   企业ID
     * @param thirdUserId 三方ID
     * @return 用户信息
     */
    @GetMapping("/getByThirdId")
    public UserDTO getUserByThirdId(@RequestParam("companyId") String companyId, @RequestParam("thirdUserId") String thirdUserId) {
        return BeanUtil.copy(userService.getByThirdUserId(companyId, thirdUserId), UserDTO.class);
    }

    /**
     * 根据 用户ID 获取用户信息包含权限信息
     *
     * @param id 用户ID
     * @return 用户信息包含权限信息
     */
    @GetMapping("/auth/{id}")
    public UserAuthDTO getById(@PathVariable("id") String id) {
        return userAuthService.getUserAuthById(id);
    }

    /**
     * 获取sass用户信息
     *
     * @param adminId sass用户id
     * @return
     */
    @GetMapping("/getAdminUserById")
    public UserDTO getAdminUserById(@RequestParam("adminId") String adminId) {
        User user = userService.getById(adminId);
        UserDTO dto = BeanUtil.copy(user, UserDTO.class);
        //todo...查询用户信息
        return dto;
    }

    /**
     * 更新用户dremio信息
     *
     * @param dremioInfo dremio信息
     * @return bool
     */
    @PostMapping("/updateUserDremioInfoById")
    public boolean updateUserDremioInfoById(@RequestBody UserDTO.DremioDTO dremioInfo) {
        return userService.updateUserDremioInfoById(dremioInfo);
    }
}