package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_nocodb_table")
public class NocodbTable extends CommonMySqlEntity {
    private String companyId;

    private Boolean ifDeleted;

    private String nocodbTableId;

    private String tableDesc;

    private String tableName;

    private String projectId;

    /**
     * 创建人
     */
    private String createBy;

}