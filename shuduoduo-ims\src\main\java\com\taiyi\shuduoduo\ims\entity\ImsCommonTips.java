package com.taiyi.shuduoduo.ims.entity;

/**
 * <AUTHOR>
 * @since 2022/6/30
 */
public class ImsCommonTips {


    public static final String HAS_SCHEMA_IN_THIS_PLATE = "该板块下已存在数据源信息!";

    public static final String NAME_DUPLICATE = "名称重复，请重新输入";

    public static final String CODE_DUPLICATE = "编码已存在";

    public static final String SCHEMA_FIELD_DUPLICATE = "指标表及字段已被引用";

    public static final String DWS_DETAIL_NOT_EXIST = "指标百科不存在";

    public static final String TAG_NOT_EXIST = "标签不存在";

    public static final String DATA_ENGINE_FAIL = "数仓查询失败";

    public static final String TOKEN_ERROR = "TOKEN错误";

    public static final String HAS_SCHEMA_LIST_IN_THIS_PLATE = "该板块下存在数据源信息,不可直接删除!";

    public static final String HAS_DATA_FIELD_LIST_IN_THIS_PLATE = "该板块下存在数据域信息,不可直接删除!";

    public static final String HAS_BUSINESS_PROCESS_LIST_IN_THIS_PLATE = "该板块下存在业务过程信息,不可直接删除!";

    public static final String HAS_DIMENSION_LIST_IN_THIS_PLATE = "该板块下存在维度信息,不可直接删除!";

    public static final String HAS_METRIC_LIST_IN_THIS_PLATE = "该板块下存在指标信息,不可直接删除!";

    public static final String HAS_BUSINESS_PROCESS_LIST_IN_THIS_DATA_FIELD = "该数据域下存在业务过程信息,不可直接删除!";

    public static final String HAS_METRIC_LIST_IN_THIS_BUSINESS_PROCESS = "该业务过程下存在指标信息,不可直接删除!";

    public static final String HAS_METRIC_LIST_IN_THIS_DIMENSION = "该维度存在关联指标信息,不可直接删除!";

    public static final String HAS_METRIC_LIST_IN_THIS_PERIOD = "该统计周期存在关联指标信息,不可直接删除!";

    public static final String CODE_LEGAL = "数仓字段不规范!";

    public static final String SELECT_COLUMN_TOO_MUCH = "所选列超出范围";

    public static final String SELECT_COLUMN_NOT_NULL = "未选择对象";
}
