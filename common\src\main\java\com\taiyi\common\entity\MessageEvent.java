package com.taiyi.common.entity;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;

/**
 * 消息
 *
 * <AUTHOR>
 */
@Validated
public class MessageEvent extends MqMessage {

    protected static final Logger logger = LoggerFactory.getLogger(MessageEvent.class);

    /**
     * 校验消息参数
     *
     * @param t   对象
     * @param <T> 泛型
     * @return boolean
     */
    public static <T extends MessageEvent> boolean checkParam(T t) {
        try {
            logger.info("MQ请求参数{}", JSON.toJSONString(t));
            if (ObjectUtil.isEmpty(t)) {
                return false;
            }
        } catch (Exception e) {
            logger.warn("MQ参数错误,parameter:{},异常信息:{}", JSON.toJSONString(t), ExceptionUtil.stacktraceToString(e));
            return false;
        }
        return true;
    }


}
