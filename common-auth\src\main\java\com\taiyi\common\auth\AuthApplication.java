package com.taiyi.common.auth;

import com.taiyi.common.entity.MicroServer;
import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */
@SpringCloudApplication
@EnableFeignClients(MicroServer.BASE_PACKAGES)
@EnableEncryptableProperties
public class AuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuthApplication.class);
    }

}
