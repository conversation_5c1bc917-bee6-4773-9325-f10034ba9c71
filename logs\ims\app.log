2025-07-19 17:19:52.751 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 17:19:52.754 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:19:52.757 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:19:52.757 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:19:52.758 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:19:52.758 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:19:52.759 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:19:54.816 [main] INFO  c.t.s.ims.ShuduoduoImsApplication - The following profiles are active: local
2025-07-19 17:19:56.642 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-19 17:19:56.647 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 17:19:56.702 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38ms. Found 0 Redis repository interfaces.
2025-07-19 17:19:56.782 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 17:19:56.962 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=12a41be9-7273-39ce-b1c9-e44947bfcf4f
2025-07-19 17:19:57.028 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 17:19:57.029 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:19:57.029 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 17:19:57.029 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 17:19:57.029 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:19:57.029 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:19:57.030 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:19:57.030 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:19:57.030 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:19:57.031 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:19:57.031 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:19:57.031 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:19:57.031 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:19:57.596 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 17:19:57.606 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 17:19:57.607 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 17:19:57.622 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:19:57.646 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$dc52d8dc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:19:57.677 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:19:57.774 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:19:57.908 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 17:19:59.245 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8880 (http)
2025-07-19 17:19:59.257 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 17:19:59.257 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.37]
2025-07-19 17:19:59.449 [main] INFO  o.a.c.c.C.[.[.[/shuduoduo/ims] - Initializing Spring embedded WebApplicationContext
2025-07-19 17:19:59.450 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4609 ms
2025-07-19 17:19:59.696 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 17:20:00.292 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-07-19 17:20:00.469 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join properties config complete
2025-07-19 17:20:00.604 [main] WARN  com.zaxxer.hikari.HikariConfig - mysql - idleTimeout has been set but has no effect because the pool is operating as a fixed size pool.
2025-07-19 17:20:00.606 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Starting...
2025-07-19 17:20:01.028 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Start completed.
2025-07-19 17:20:01.031 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Starting...
2025-07-19 17:20:01.101 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Start completed.
2025-07-19 17:20:01.101 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sqlite] success
2025-07-19 17:20:01.102 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [mysql] success
2025-07-19 17:20:01.102 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [mysql]
2025-07-19 17:20:01.134 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join SqlInjector init
2025-07-19 17:20:07.037 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-19 17:20:08.522 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 19 endpoint(s) beneath base path '/actuator'
2025-07-19 17:20:08.660 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 17:20:08.661 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 17:20:08.669 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 17:20:08.669 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 17:20:08.970 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-07-19 17:20:10.574 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8880 (http) with context path '/shuduoduo/ims'
2025-07-19 17:20:10.576 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 17:20:10.577 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 17:20:10.577 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 17:20:10.578 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 17:20:10.578 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 17:20:10.578 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application-local.yml] refreshed
2025-07-19 17:20:10.578 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.properties] refreshed
2025-07-19 17:20:10.578 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.yml] refreshed
2025-07-19 17:20:10.579 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudDefaultProperties refreshed
2025-07-19 17:20:10.579 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source cachedrandom refreshed
2025-07-19 17:20:10.579 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:20:10.579 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 17:20:10.579 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-19 17:20:10.580 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
2025-07-19 17:20:10.625 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP shuduoduo-ims *************:8880 register finished
2025-07-19 17:20:11.287 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 17:20:11.287 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 17:20:11.287 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 17:20:11.287 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 17:20:11.287 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 17:20:11.288 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 17:20:11.288 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 17:20:11.288 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 17:20:11.288 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 17:20:12.578 [main] INFO  c.t.c.l.NacosConfigRefreshListener - [配置监听] Nacos配置动态监听器已加载
2025-07-19 17:20:12.596 [main] INFO  c.t.s.ims.ShuduoduoImsApplication - Started ShuduoduoImsApplication in 21.867 seconds (JVM running for 22.665)
2025-07-19 17:46:55.911 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-07-19 17:46:55.938 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 17:46:55.942 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-19 17:46:55.943 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-07-19 17:46:55.952 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-19 17:46:55.952 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown initiated...
2025-07-19 17:46:55.958 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown completed.
2025-07-19 17:46:55.959 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown initiated...
2025-07-19 17:46:55.960 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown completed.
2025-07-19 17:46:55.960 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-19 17:46:55.961 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-19 21:31:15.027 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:31:15.031 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:31:15.033 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:31:15.033 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:31:15.033 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:31:15.033 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:31:15.033 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:31:16.522 [main] INFO  c.t.s.ims.ShuduoduoImsApplication - The following profiles are active: local
2025-07-19 21:31:18.234 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-19 21:31:18.237 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 21:31:18.276 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25ms. Found 0 Redis repository interfaces.
2025-07-19 21:31:18.337 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 21:31:18.485 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=12a41be9-7273-39ce-b1c9-e44947bfcf4f
2025-07-19 21:31:18.548 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:31:18.548 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:31:18.549 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:31:18.549 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:31:18.549 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:31:18.549 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:31:18.550 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:31:18.550 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:31:18.550 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:31:18.550 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:31:18.550 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:31:18.551 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:31:18.551 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:31:19.008 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 21:31:19.016 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 21:31:19.020 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 21:31:19.039 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:31:19.059 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$41ed2e07] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:31:19.087 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:31:19.172 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:31:19.291 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:31:20.655 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8880 (http)
2025-07-19 21:31:20.666 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 21:31:20.666 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.37]
2025-07-19 21:31:20.883 [main] INFO  o.a.c.c.C.[.[.[/shuduoduo/ims] - Initializing Spring embedded WebApplicationContext
2025-07-19 21:31:20.883 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4337 ms
2025-07-19 21:31:21.213 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 21:31:21.963 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-07-19 21:31:22.187 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join properties config complete
2025-07-19 21:31:22.352 [main] WARN  com.zaxxer.hikari.HikariConfig - mysql - idleTimeout has been set but has no effect because the pool is operating as a fixed size pool.
2025-07-19 21:31:22.356 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Starting...
2025-07-19 21:31:22.887 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Start completed.
2025-07-19 21:31:22.891 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Starting...
2025-07-19 21:31:22.968 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Start completed.
2025-07-19 21:31:22.968 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sqlite] success
2025-07-19 21:31:22.969 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [mysql] success
2025-07-19 21:31:22.969 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [mysql]
2025-07-19 21:31:23.004 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join SqlInjector init
2025-07-19 21:31:28.945 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-19 21:31:30.455 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 19 endpoint(s) beneath base path '/actuator'
2025-07-19 21:31:30.601 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 21:31:30.602 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 21:31:30.607 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 21:31:30.608 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 21:31:30.926 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-07-19 21:31:33.029 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8880 (http) with context path '/shuduoduo/ims'
2025-07-19 21:31:33.031 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 21:31:33.031 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 21:31:33.032 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 21:31:33.032 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 21:31:33.032 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 21:31:33.033 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application-local.yml] refreshed
2025-07-19 21:31:33.033 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.properties] refreshed
2025-07-19 21:31:33.033 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.yml] refreshed
2025-07-19 21:31:33.033 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudDefaultProperties refreshed
2025-07-19 21:31:33.033 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source cachedrandom refreshed
2025-07-19 21:31:33.033 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:31:33.033 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:31:33.033 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-19 21:31:33.034 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
2025-07-19 21:31:33.084 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP shuduoduo-ims *************:8880 register finished
2025-07-19 21:31:33.851 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 21:31:33.851 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 21:31:33.851 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 21:31:33.851 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 21:31:33.852 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 21:31:33.852 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:31:33.852 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:31:33.852 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:31:33.852 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:31:34.953 [main] INFO  c.t.c.l.NacosConfigRefreshListener - [配置监听] Nacos配置动态监听器已加载
2025-07-19 21:31:34.975 [main] INFO  c.t.s.ims.ShuduoduoImsApplication - Started ShuduoduoImsApplication in 21.855 seconds (JVM running for 22.568)
2025-07-19 21:35:05.008 [http-nio-8880-exec-1] INFO  o.a.c.c.C.[.[.[/shuduoduo/ims] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 21:35:05.008 [http-nio-8880-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 21:35:05.020 [http-nio-8880-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 12 ms
2025-07-19 21:35:05.129 [http-nio-8880-exec-1] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$9fc16359 - request path: /shuduoduo/ims/nocodb/table/info/7172070a1c264c2bb6bb201e17bf8ee7
2025-07-19 21:35:05.142 [http-nio-8880-exec-1] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$9fc16359 - request path: /shuduoduo/ims/error
2025-07-19 21:36:55.537 [http-nio-8880-exec-3] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$9fc16359 - request path: /shuduoduo/ims/nocodb/table/info/7172070a1c264c2bb6bb201e17bf8ee7
2025-07-19 21:36:55.541 [http-nio-8880-exec-3] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$9fc16359 - request path: /shuduoduo/ims/error
2025-07-19 21:41:19.705 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-07-19 21:41:19.758 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 21:41:19.765 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-19 21:41:19.766 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-07-19 21:41:19.776 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-19 21:41:19.777 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown initiated...
2025-07-19 21:41:19.783 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown completed.
2025-07-19 21:41:19.783 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown initiated...
2025-07-19 21:41:19.879 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown completed.
2025-07-19 21:41:19.880 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-19 21:41:19.880 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-19 21:41:28.870 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:41:28.874 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:41:28.877 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:41:28.878 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:41:28.878 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:41:28.878 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:41:28.878 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:41:30.467 [main] INFO  c.t.s.ims.ShuduoduoImsApplication - The following profiles are active: local
2025-07-19 21:41:32.591 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-19 21:41:32.595 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 21:41:32.637 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29ms. Found 0 Redis repository interfaces.
2025-07-19 21:41:32.711 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 21:41:32.864 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=cb4ca344-6261-34bf-af4e-be98a541ee82
2025-07-19 21:41:32.934 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:41:32.935 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:41:32.935 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:41:32.935 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:41:32.935 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:41:32.936 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:41:32.936 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:41:32.936 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:41:32.936 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:41:32.936 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:41:32.936 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:41:32.937 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:41:32.937 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:41:33.408 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 21:41:33.418 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 21:41:33.420 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 21:41:33.437 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:41:33.459 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$cbabf51d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:41:33.481 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:41:33.573 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:41:33.701 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:41:35.031 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8880 (http)
2025-07-19 21:41:35.043 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 21:41:35.043 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.37]
2025-07-19 21:41:35.250 [main] INFO  o.a.c.c.C.[.[.[/shuduoduo/ims] - Initializing Spring embedded WebApplicationContext
2025-07-19 21:41:35.250 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4758 ms
2025-07-19 21:41:35.518 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 21:41:36.120 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-07-19 21:41:36.253 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join properties config complete
2025-07-19 21:41:36.357 [main] WARN  com.zaxxer.hikari.HikariConfig - mysql - idleTimeout has been set but has no effect because the pool is operating as a fixed size pool.
2025-07-19 21:41:36.360 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Starting...
2025-07-19 21:41:36.721 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Start completed.
2025-07-19 21:41:36.724 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Starting...
2025-07-19 21:41:36.779 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Start completed.
2025-07-19 21:41:36.779 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sqlite] success
2025-07-19 21:41:36.780 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [mysql] success
2025-07-19 21:41:36.780 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [mysql]
2025-07-19 21:41:36.806 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join SqlInjector init
2025-07-19 21:41:42.585 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-19 21:41:42.615 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'documentationPluginsBootstrapper' defined in URL [jar:file:/C:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsBootstrapper.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcRequestHandlerProvider' defined in URL [jar:file:/C:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'authNocodbController' method 
com.taiyi.shuduoduo.ims.controller.AuthNocodbController#getUserNocoInfo(String)
to {GET /auth/nocodb/info/{comId}}: There is already 'authNocodbController' bean method
com.taiyi.shuduoduo.ims.controller.AuthNocodbController#getById(String) mapped.
2025-07-19 21:41:42.623 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-19 21:41:42.624 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown initiated...
2025-07-19 21:41:42.628 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown completed.
2025-07-19 21:41:42.628 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown initiated...
2025-07-19 21:41:42.640 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown completed.
2025-07-19 21:41:42.640 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-19 21:41:42.640 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-19 21:41:42.643 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-19 21:41:42.660 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-19 21:41:42.667 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'documentationPluginsBootstrapper' defined in URL [jar:file:/C:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/3.0.0/springfox-spring-web-3.0.0.jar!/springfox/documentation/spring/web/plugins/DocumentationPluginsBootstrapper.class]: Unsatisfied dependency expressed through constructor parameter 1; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcRequestHandlerProvider' defined in URL [jar:file:/C:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'authNocodbController' method 
com.taiyi.shuduoduo.ims.controller.AuthNocodbController#getUserNocoInfo(String)
to {GET /auth/nocodb/info/{comId}}: There is already 'authNocodbController' bean method
com.taiyi.shuduoduo.ims.controller.AuthNocodbController#getById(String) mapped.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:797)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:227)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1356)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1203)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1237)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at com.taiyi.shuduoduo.ims.ShuduoduoImsApplication.main(ShuduoduoImsApplication.java:28)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webMvcRequestHandlerProvider' defined in URL [jar:file:/C:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-webmvc/3.0.0/springfox-spring-webmvc-3.0.0.jar!/springfox/documentation/spring/web/plugins/WebMvcRequestHandlerProvider.class]: Unsatisfied dependency expressed through constructor parameter 2; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'authNocodbController' method 
com.taiyi.shuduoduo.ims.controller.AuthNocodbController#getUserNocoInfo(String)
to {GET /auth/nocodb/info/{comId}}: There is already 'authNocodbController' bean method
com.taiyi.shuduoduo.ims.controller.AuthNocodbController#getById(String) mapped.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:797)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:227)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1356)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1203)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1525)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1489)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1378)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1265)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:884)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'requestMappingHandlerMapping' defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]: Invocation of init method failed; nested exception is java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'authNocodbController' method 
com.taiyi.shuduoduo.ims.controller.AuthNocodbController#getUserNocoInfo(String)
to {GET /auth/nocodb/info/{comId}}: There is already 'authNocodbController' bean method
com.taiyi.shuduoduo.ims.controller.AuthNocodbController#getById(String) mapped.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1794)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:594)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1525)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1489)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1378)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1265)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:884)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	... 37 common frames omitted
Caused by: java.lang.IllegalStateException: Ambiguous mapping. Cannot map 'authNocodbController' method 
com.taiyi.shuduoduo.ims.controller.AuthNocodbController#getUserNocoInfo(String)
to {GET /auth/nocodb/info/{comId}}: There is already 'authNocodbController' bean method
com.taiyi.shuduoduo.ims.controller.AuthNocodbController#getById(String) mapped.
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.validateMethodMapping(AbstractHandlerMethodMapping.java:636)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping$MappingRegistry.register(AbstractHandlerMethodMapping.java:603)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.registerHandlerMethod(AbstractHandlerMethodMapping.java:318)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:378)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.registerHandlerMethod(RequestMappingHandlerMapping.java:75)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lambda$detectHandlerMethods$1(AbstractHandlerMethodMapping.java:288)
	at java.util.LinkedHashMap.forEach(LinkedHashMap.java:684)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.detectHandlerMethods(AbstractHandlerMethodMapping.java:286)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.processCandidateBean(AbstractHandlerMethodMapping.java:258)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.initHandlerMethods(AbstractHandlerMethodMapping.java:217)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.afterPropertiesSet(AbstractHandlerMethodMapping.java:205)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping.afterPropertiesSet(RequestMappingHandlerMapping.java:189)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1853)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1790)
	... 51 common frames omitted
2025-07-19 21:43:16.301 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:43:16.305 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:43:16.307 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:43:16.308 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:43:16.308 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:43:16.308 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:43:16.308 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:43:17.887 [main] INFO  c.t.s.ims.ShuduoduoImsApplication - The following profiles are active: local
2025-07-19 21:43:20.053 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-19 21:43:20.057 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-19 21:43:20.097 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26ms. Found 0 Redis repository interfaces.
2025-07-19 21:43:20.166 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-19 21:43:20.321 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=cb4ca344-6261-34bf-af4e-be98a541ee82
2025-07-19 21:43:20.387 [main] INFO  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-07-19 21:43:20.388 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:43:20.388 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:43:20.388 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:43:20.388 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:43:20.388 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:43:20.389 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:43:20.389 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:43:20.389 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application-local.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:43:20.389 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:43:20.389 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource applicationConfig: [classpath:/application.yml] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:43:20.389 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:43:20.390 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:43:20.960 [main] INFO  c.u.j.f.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-19 21:43:20.968 [main] INFO  c.u.j.r.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-19 21:43:20.970 [main] INFO  c.u.j.d.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-19 21:43:20.985 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:43:21.009 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$511e0f2c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:43:21.041 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:43:21.153 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:43:21.295 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-19 21:43:22.682 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8880 (http)
2025-07-19 21:43:22.694 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-19 21:43:22.695 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.37]
2025-07-19 21:43:22.886 [main] INFO  o.a.c.c.C.[.[.[/shuduoduo/ims] - Initializing Spring embedded WebApplicationContext
2025-07-19 21:43:22.887 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4970 ms
2025-07-19 21:43:23.169 [main] WARN  o.s.boot.actuate.endpoint.EndpointId - Endpoint ID 'nacos-discovery' contains invalid characters, please migrate to a valid format.
2025-07-19 21:43:23.785 [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-07-19 21:43:23.948 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join properties config complete
2025-07-19 21:43:24.121 [main] WARN  com.zaxxer.hikari.HikariConfig - mysql - idleTimeout has been set but has no effect because the pool is operating as a fixed size pool.
2025-07-19 21:43:24.124 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Starting...
2025-07-19 21:43:24.553 [main] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Start completed.
2025-07-19 21:43:24.556 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Starting...
2025-07-19 21:43:24.623 [main] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Start completed.
2025-07-19 21:43:24.623 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [sqlite] success
2025-07-19 21:43:24.623 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [mysql] success
2025-07-19 21:43:24.624 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [mysql]
2025-07-19 21:43:24.656 [main] INFO  c.g.y.a.MybatisPlusJoinAutoConfiguration - mybatis plus join SqlInjector init
2025-07-19 21:43:30.723 [main] INFO  c.a.c.s.SentinelWebAutoConfiguration - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-19 21:43:32.194 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 19 endpoint(s) beneath base path '/actuator'
2025-07-19 21:43:32.363 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 21:43:32.363 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 21:43:32.370 [main] WARN  c.n.c.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-19 21:43:32.371 [main] INFO  c.n.c.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-19 21:43:32.725 [main] INFO  o.s.s.c.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-07-19 21:43:34.383 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8880 (http) with context path '/shuduoduo/ims'
2025-07-19 21:43:34.384 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 21:43:34.384 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 21:43:34.384 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 21:43:34.385 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 21:43:34.385 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 21:43:34.385 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application-local.yml] refreshed
2025-07-19 21:43:34.385 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.properties] refreshed
2025-07-19 21:43:34.385 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source applicationConfig: [classpath:/application.yml] refreshed
2025-07-19 21:43:34.385 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudDefaultProperties refreshed
2025-07-19 21:43:34.385 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source cachedrandom refreshed
2025-07-19 21:43:34.385 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:43:34.385 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-19 21:43:34.385 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-19 21:43:34.386 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource Management Server [org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration$SameManagementContextConfiguration$1] to EncryptablePropertySourceWrapper
2025-07-19 21:43:34.424 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP shuduoduo-ims *************:8880 register finished
2025-07-19 21:43:35.140 [main] INFO  c.u.j.c.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-07-19 21:43:35.140 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-07-19 21:43:35.140 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-07-19 21:43:35.140 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-07-19 21:43:35.140 [main] INFO  c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source springCloudClientHostInfo refreshed
2025-07-19 21:43:35.140 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-19 21:43:35.140 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-19 21:43:35.140 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-07-19 21:43:35.141 [main] INFO  c.u.j.EncryptablePropertySourceConverter - Converting PropertySource springCloudDefaultProperties [org.springframework.cloud.bootstrap.BootstrapApplicationListener$ExtendedDefaultPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-19 21:43:36.478 [main] INFO  c.t.c.l.NacosConfigRefreshListener - [配置监听] Nacos配置动态监听器已加载
2025-07-19 21:43:36.502 [main] INFO  c.t.s.ims.ShuduoduoImsApplication - Started ShuduoduoImsApplication in 22.073 seconds (JVM running for 23.854)
2025-07-19 21:46:03.450 [http-nio-8880-exec-1] INFO  o.a.c.c.C.[.[.[/shuduoduo/ims] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-19 21:46:03.451 [http-nio-8880-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-19 21:46:03.461 [http-nio-8880-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 10 ms
2025-07-19 21:46:03.575 [http-nio-8880-exec-1] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$aef2447e - request path: /shuduoduo/ims/nocodb/table/info/7172070a1c264c2bb6bb201e17bf8ee7
2025-07-19 21:46:03.602 [http-nio-8880-exec-1] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :66c3d064-cf7e-4619-9355-844ee398e3a6, device: null, path: /shuduoduo/ims/nocodb/table/info/7172070a1c264c2bb6bb201e17bf8ee7, method: GET, request params :[7172070a1c264c2bb6bb201e17bf8ee7], request json: ["7172070a1c264c2bb6bb201e17bf8ee7"]
2025-07-19 21:46:03.783 [http-nio-8880-exec-1] DEBUG c.t.common.aspect.WebApiLogAspect - api request :66c3d064-cf7e-4619-9355-844ee398e3a6, device: null, response data: {"body":{"code":"A000","data":{"id":"p_zlv85gvudvgjh7","baseId":"ds_x2vmydag0fpco0"},"message":"返回正常","tips":"返回正常"},"headers":{},"statusCode":"OK","statusCodeValue":200}
2025-07-19 21:46:05.433 [http-nio-8880-exec-2] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$aef2447e - request path: /shuduoduo/ims/auth/nocodb/info/7172070a1c264c2bb6bb201e17bf8ee7
2025-07-19 21:46:05.435 [http-nio-8880-exec-2] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :a67033f1-9a40-467d-ac6f-b810c60c0d68, device: null, path: /shuduoduo/ims/auth/nocodb/info/7172070a1c264c2bb6bb201e17bf8ee7, method: GET, request params :[7172070a1c264c2bb6bb201e17bf8ee7], request json: ["7172070a1c264c2bb6bb201e17bf8ee7"]
2025-07-19 21:46:05.484 [http-nio-8880-exec-2] DEBUG c.t.common.aspect.WebApiLogAspect - api request :a67033f1-9a40-467d-ac6f-b810c60c0d68, device: null, response data: {"body":{"code":"A000","data":{"baseId":"ds_nzp00hx4vns20u","companyId":"7172070a1c264c2bb6bb201e17bf8ee7","createTime":1684425600000,"id":"quanxiandenocodbxinxi","ifDeleted":false,"projectId":"p_begh
2025-07-19 21:46:51.957 [http-nio-8880-exec-3] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$aef2447e - request path: /shuduoduo/ims/nocodb/table/info/7172070a1c264c2bb6bb201e17bf8ee7
2025-07-19 21:46:51.958 [http-nio-8880-exec-3] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :6d2cbb9a-b68c-4fc5-9b89-a8e327534e46, device: null, path: /shuduoduo/ims/nocodb/table/info/7172070a1c264c2bb6bb201e17bf8ee7, method: GET, request params :[7172070a1c264c2bb6bb201e17bf8ee7], request json: ["7172070a1c264c2bb6bb201e17bf8ee7"]
2025-07-19 21:46:51.995 [http-nio-8880-exec-3] DEBUG c.t.common.aspect.WebApiLogAspect - api request :6d2cbb9a-b68c-4fc5-9b89-a8e327534e46, device: null, response data: {"body":{"code":"A000","data":{"id":"p_zlv85gvudvgjh7","baseId":"ds_x2vmydag0fpco0"},"message":"返回正常","tips":"返回正常"},"headers":{},"statusCode":"OK","statusCodeValue":200}
2025-07-19 21:46:52.014 [http-nio-8880-exec-4] INFO  c.t.c.c.WebSecurityConfig$$EnhancerBySpringCGLIB$$aef2447e - request path: /shuduoduo/ims/auth/nocodb/info/7172070a1c264c2bb6bb201e17bf8ee7
2025-07-19 21:46:52.015 [http-nio-8880-exec-4] DEBUG c.t.common.aspect.WebApiLogAspect - api user: null, request :3b0ca6ff-677e-428d-8933-0b2e834c2093, device: null, path: /shuduoduo/ims/auth/nocodb/info/7172070a1c264c2bb6bb201e17bf8ee7, method: GET, request params :[7172070a1c264c2bb6bb201e17bf8ee7], request json: ["7172070a1c264c2bb6bb201e17bf8ee7"]
2025-07-19 21:46:52.035 [http-nio-8880-exec-4] DEBUG c.t.common.aspect.WebApiLogAspect - api request :3b0ca6ff-677e-428d-8933-0b2e834c2093, device: null, response data: {"body":{"code":"A000","data":{"baseId":"ds_nzp00hx4vns20u","companyId":"7172070a1c264c2bb6bb201e17bf8ee7","createTime":1684425600000,"id":"quanxiandenocodbxinxi","ifDeleted":false,"projectId":"p_begh
2025-07-19 21:50:14.626 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-07-19 21:50:14.673 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registering from Nacos Server now...
2025-07-19 21:50:14.676 [SpringContextShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - De-registration finished.
2025-07-19 21:50:14.677 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-07-19 21:50:14.684 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-19 21:50:14.685 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown initiated...
2025-07-19 21:50:14.689 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - sqlite - Shutdown completed.
2025-07-19 21:50:14.689 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown initiated...
2025-07-19 21:50:14.693 [SpringContextShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - mysql - Shutdown completed.
2025-07-19 21:50:14.693 [SpringContextShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-19 21:50:14.693 [SpringContextShutdownHook] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
