package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.entity.DbType;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import com.taiyi.shuduoduo.common.connector.api.service.DbRpcService;
import com.taiyi.shuduoduo.ims.dao.DimensionDao;
import com.taiyi.shuduoduo.ims.dremio.service.DremioApiService;
import com.taiyi.shuduoduo.ims.entity.*;
import com.taiyi.shuduoduo.ims.exceptions.DremioException;
import com.taiyi.shuduoduo.ims.exceptions.QueryFailedException;
import com.taiyi.shuduoduo.ims.util.SqlBuilderUtil;
import com.taiyi.shuduoduo.ims.vo.*;
import com.taiyi.shuduoduo.sms.api.dto.DictDTO;
import com.taiyi.shuduoduo.sms.api.service.DictRpcService;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyDremioRpcService;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.validation.constraints.NotNull;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.taiyi.shuduoduo.ims.util.CheckQueryDataResponse.getErrorValues;

/**
 * 维度/事实表
 *
 * <AUTHOR>
 */
@Service
public class DimensionService extends CommonMysqlService<DimensionDao, Dimension> {

    public static final String SUCCEED_MSG = "检查通过";

    public static final String FAILED_MSG = "表设计字段英文名与虚拟表不匹配";

    public static final String DATA_LAKE_VIEW_NOT_FOUND = "找不到虚拟表";

    public static final String API_EXECUTE_ERROR = "API执行错误";

    public static final String DIMENSION_FIELD_IS_EMPTY = "表设计字段为空";

    @Autowired
    private DimAttributeService dimAttributeService;

    @Autowired
    private PlateService plateService;

    @Autowired
    private DataFieldService dataFieldService;

    @Autowired
    private BusinessProcessService businessProcessService;

    @Autowired
    private CompanyDremioRpcService dremioRpcService;

    @Autowired
    private DremioApiService dremioApiService;

    @Autowired
    private PlateLayerService plateLayerService;

    @Autowired
    private RawTableService rawTableService;

    @Autowired
    private DimDwsService dimDwsService;

    @Autowired
    private DictRpcService dictRpcService;

    @Autowired
    private DimensionDao dimensionDao;

    @Autowired
    private DbRpcService dbRpcService;

    @Override
    public Class<Dimension> getEntityClass() {
        return Dimension.class;
    }

    /**
     * 新增维度/事实表信息
     *
     * @param addDimension 对象
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public String addDimension(DimensionVo.AddDimension addDimension) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Dimension dimension = BeanUtil.copy(addDimension, Dimension.class);
        dimension.setCompanyId(CurrentUserUtil.get().getCompanyId());
        dimension.setType(1);
        dimension.setOrderBy(getMaxOrder());
        // 新增维度
        boolean f = this.save(dimension);
        if (f && !addDimension.getAttrList().isEmpty()) {
            //新增维度属性
            f = addDimAttrList(addDimension.getAttrList(), dimension.getId(), addDimension.getDataType());
            if (f) {
                // 异步处理指标关联数据
                afterExecuteUnionInfo(companyId, dimension.getId());
            }
        }
        return dimension.getId();
    }

    /**
     * 编辑维度/事实表信息
     *
     * @param addDimension 对象
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean editDimension(DimensionVo.AddDimension addDimension) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Dimension dimension = BeanUtil.copy(addDimension, Dimension.class);
        dimension.setCompanyId(companyId);

        // 查询旧的维度信息
        Dimension oldDimension = super.getById(addDimension.getId());
        if (oldDimension == null) {
            logger.warn("未找到维度信息，ID: {}", addDimension.getId());
            return false;
        }

        // 1. 修改字段信息
        if (!updateDimensionAttributes(addDimension)) {
            return false;
        }

        // 2. 更新维度表基本信息
        if (!super.updateById(dimension.getId(), dimension)) {
            logger.warn("更新维度表基本信息失败，ID: {}", dimension.getId());
            return false;
        }

        // 3. 同步字段信息到数据湖
        if (!syncDimensionToDremio(dimension, oldDimension, companyId)) {
            return false;
        }

        // 4. 异步处理指标关联数据
        afterExecuteUnionInfo(companyId, dimension.getId());

        return true;
    }

    /**
     * 修改字段信息
     */
    private boolean updateDimensionAttributes(DimensionVo.AddDimension addDimension) {
        // 修改字段信息
        if (!editDimAttrList(addDimension.getEditAttrList())) {
            logger.warn("修改字段信息失败");
            return false;
        }

        // 删除字段信息
        if (!dimAttributeService.logicDeleteByIds(addDimension.getDelAttrList())) {
            logger.warn("删除字段信息失败");
            return false;
        }

        // 新增字段信息
        if (!addDimAttrList(addDimension.getAttrList(), addDimension.getId(), addDimension.getDataType())) {
            logger.warn("新增字段信息失败");
            return false;
        }

        return true;
    }

    /**
     * 同步修改数据湖中的表名
     *
     * @param dimension    维度对象
     * @param oldDimension 旧的维度对象
     * @param companyId    公司ID
     * @return boolean
     */
    private boolean syncDimensionToDremio(Dimension dimension, Dimension oldDimension, String companyId) {
        if (!oldDimension.getCode().equals(dimension.getCode())) {
            CompanyDremioDTO dremioDTO = dremioRpcService.getCompanyById(companyId);
            if (dremioDTO == null) {
                logger.warn("未找到Dremio配置，companyId: {}", companyId);
                return false;
            }

            Plate plate = plateService.getById(oldDimension.getPlateId());
            PlateLayer plateLayer = plateLayerService.getById(oldDimension.getPlateLayerId());
            if (plate == null || plateLayer == null) {
                logger.warn("板块或分层信息缺失，plateId: {}, layerId: {}", oldDimension.getPlateId(), oldDimension.getPlateLayerId());
                return false;
            }
            try {
                dremioApiService.renameDataSet(
                        dremioDTO.getDremioUsername(),
                        dremioDTO.getDremioPasswd(),
                        dremioDTO.getDremioApiUri(),
                        plate.getPlateCode(),
                        plateLayer.getCode(),
                        oldDimension.getCode(),
                        dimension.getCode()
                );
            } catch (Exception e) {
                logger.error(e.getMessage());
                return false;
            }
        }
        return true;
    }

    /**
     * 异步处理指标关联数据
     *
     * @param companyId 公司ID
     * @param dimId     维度ID
     */
    public void afterExecuteUnionInfo(String companyId, String dimId) {
        // 处理指标关联数据
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                //异步操作 通知维度表关联的台账--有事实表字段关联指标
                List<DimAttribute> attributeList = dimAttributeService.getHasDwsList(dimId);
                List<DwsVo.DimensionUnion> unionList = new ArrayList<>();
                // 记录指标ID
                String dwsId = null;
                for (DimAttribute attribute : attributeList) {
                    if (StringUtils.isBlank(dwsId)) {
                        dwsId = attribute.getDwsId();
                    }
                    DwsVo.DimensionUnion union = new DwsVo.DimensionUnion();
                    union.setDimId(attribute.getDimId());
                    union.setDimAttrId(attribute.getId());
                    union.setDwsId(attribute.getDwsId());
                    union.setUnionType(attribute.getUnionType());
                    union.setAnalysis(attribute.getAnalysis());
                    union.setPeriodValue(attribute.getPeriodValue());
                    union.setDimension(attribute.getDimension());
                    unionList.add(union);
                }
                // 更新维度指标关联关系
                dimDwsService.saveBatchByDimIdWithUnionList(unionList);
                // 通知维度表关联的台账--有事实表字段关联指标
                callRawUpdate(companyId, dwsId, dimId, null);
            }
        });
    }

    /**
     * 批量添加字段属性
     *
     * @param attributeList 字段信息列表
     * @param dimId         维度/事实表ID
     * @param dataType      数据类型：1、维度 2、事实
     * @return 布尔
     */
    private boolean addDimAttrList(List<DimensionVo.AddDimension.DimAttr> attributeList, String dimId, Integer dataType) {
        if (null == attributeList || attributeList.isEmpty()) {
            return true;
        }
//        if (dataType == 1) {
        List<DimAttribute> dimAttributeList = BeanUtil.copyList(attributeList, DimAttribute.class);
        for (DimAttribute dimAttribute : dimAttributeList) {
            dimAttribute.setId(null);
            dimAttribute.setCompanyId(CurrentUserUtil.get().getCompanyId());
            dimAttribute.setDimId(dimId);
            dimAttribute.setType(1);
            dimAttribute.setAliasName(dimAttribute.getAliasName());
        }
        return dimAttributeService.saveBatch(dimAttributeList);
    }

    /**
     * 批量修改字段属性
     *
     * @param attributeList 字段信息列表
     * @return 布尔
     */
    private boolean editDimAttrList(List<DimensionVo.AddDimension.DimAttr> attributeList) {
        if (null == attributeList || attributeList.isEmpty()) {
            return true;
        }
        // 1.新的维度属性数据列表
        List<DimAttribute> dimAttributeList = BeanUtil.copyList(attributeList, DimAttribute.class);

        List<String> attrIds = dimAttributeList.stream()
                .map(DimAttribute::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 2.查找修改前的维度属性数据
        List<DimAttribute> beforeAttrList = dimAttributeService.listByIds(attrIds);

        // 3. 构建 Map 用于快速比对
        Map<String, DimAttribute> beforeAttrMap = beforeAttrList.stream()
                .collect(Collectors.toMap(DimAttribute::getId, a -> a));

        // 4.比较两个维度属性列表，如果删除了挂接指标，删除挂接表
        for (DimAttribute attribute : dimAttributeList) {
            DimAttribute beforeAttr = beforeAttrMap.get(attribute.getId());
            if (beforeAttr.getId().equals(attribute.getId())) {
                // 如果删除了关联指标，删除关联表
                if (null == attribute.getDwsId() && null != beforeAttr.getDwsId()) {
                    // 删除关联表
                    dimDwsService.deletedByDimIdAndAttrIdAndDwsId(beforeAttr.getDimId(), beforeAttr.getId(), beforeAttr.getDwsId());
                }
            }
        }

        return dimAttributeService.updateBatchById(dimAttributeList);
    }

    /**
     * 获取维度最大排序
     *
     * @return long
     */
    public Long getMaxOrder() {
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).orderByDesc("order_by").last("LIMIT 1");
        Dimension t = super.getOne(wrapper);
        return null == t ? 1L : t.getOrderBy() + 1;
    }

    /**
     * 根据公司id查询维度列表
     *
     * @param companyId 公司id
     * @return 维度列表
     */
    public List<Dimension> listByCompanyId(String companyId) {
        //从公司id查询板块再查询维度
        //查询所有板块
        List<Plate> plateByCompanyId = plateService.getListByCompanyId(companyId);
        List<String> pIds = plateByCompanyId.stream().map(Plate::getId).collect(Collectors.toList());
        if (!pIds.isEmpty()) {
            //查询所有维度
            QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
            wrapper.in("plate_id", pIds);
            wrapper.eq("if_deleted", 0);
            return this.list(wrapper);
        } else {
            //如果板块为空返回空集合
            return new ArrayList<>();
        }
    }

    /**
     * 初始化数据
     */
    public void initData() {
        //TODO... 把数据写入到数据库  注意，写入前要判断数据是否存在
        List<Dimension> dimensionList = this.list();
        String fileName = "src/main/java/com/taiyi/shujianjian/ims/init/dimensionInit.sql";
        if (dimensionList.size() == 0) {
            File file = new File(fileName);
            BufferedReader reader = null;
            try {
//                System.out.println("以行为单位读取文件内容，一次读一整行：");
                reader = new BufferedReader(new FileReader(file));
                String tempString = null;
                int line = 1;
                // 一次读入一行，直到读入null为文件结束
                while ((tempString = reader.readLine()) != null) {
                    // 显示行号
                    System.out.println("line " + line + ": " + tempString);
                    line++;
                }
                reader.close();
            } catch (IOException e) {
                logger.error("数据库初始化文件读取失败", e);
            } finally {
                if (reader != null) {
                    try {
                        reader.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 根据目录ID查询维度列表
     *
     * @param unionId 目录ID
     * @param type    目录类型
     * @param keyWord 关键字搜索
     * @return 维度列表
     */
    public List<Dimension> getDimListByUnionId(String unionId, @NotNull Integer type, String keyWord) {
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0);
        if (type == 1) {
            wrapper.eq("plate_id", unionId);
        } else if (type == 2) {
            wrapper.eq("dim_folder_id", unionId);
        } else if (type == 3) {
            wrapper.eq("data_field_id", unionId);
        } else if (type == 4) {
            wrapper.eq("bus_process_id", unionId);
        }
        if (StringUtils.isNotBlank(keyWord)) {
            wrapper.like("name", keyWord);
        }
        return this.list(wrapper);
    }

    /**
     * 根据给定的信息判断维度/事实表存不存在
     *
     * @param dimension 维度/事实表信息
     * @return 数据域
     */
    public Dimension isDimensionExist(Dimension dimension) {
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.setEntity(dimension)
                .last("LIMIT 1");
        return this.getOne(wrapper);
    }

    /**
     * 删除维度/事实表
     *
     * @param id 维度/事实表id
     * @return 布尔
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String id) {
        Dimension dimension = super.getById(id);
        CompanyDremioDTO dremioDTO = dremioRpcService.getCompanyById(dimension.getCompanyId());
        // 删除数据湖中的数据集
        if (dimension.getDatasetId() != null) {
            dremioApiService.deleteDataSet(dremioDTO.getDremioUsername(),
                    dremioDTO.getDremioPasswd(),
                    dremioDTO.getDremioApiUri(),
                    dimension.getDatasetId());
        }
        return dimAttributeService.deletedByDimId(id) && this.logicDeleteById(id);
    }

    /**
     * 维度分页查询
     *
     * @param pageVo 分页参数
     * @return PageResult
     */
    public DimensionVo.DimensionPageResult<DimensionVo.PageResponse> myPage(DimensionVo.PageVo pageVo) {
        Page<Dimension> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        page.setOptimizeCountSql(false);
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false)
                .eq("if_insight", false)
                .eq("data_type", pageVo.getDataType())
                .eq(StrUtil.isNotBlank(pageVo.getId()), "id", pageVo.getId());
        if (StringUtils.isBlank(pageVo.getPlateId()) && StringUtils.isBlank(pageVo.getDataFieldId()) && StringUtils.isBlank(pageVo.getBusProcessId())) {
            //添加板块控制
            List<Plate> plateList = plateService.getListByCompanyId(CurrentUserUtil.get().getCompanyId());
            if (ObjectUtil.isEmpty(plateList)) {
                return new DimensionVo.DimensionPageResult<>();
            }
            List<String> plateIds = new ArrayList<>();
            plateList.forEach(plate -> plateIds.add(plate.getId()));
            wrapper.in("plate_id", plateIds);
        } else if (StringUtils.isNotBlank(pageVo.getPlateId())) {
            wrapper.eq("plate_id", pageVo.getPlateId());
        } else if (StringUtils.isNotBlank(pageVo.getDataFieldId())) {
            wrapper.eq("data_field_id", pageVo.getDataFieldId());
        } else if (StringUtils.isNotBlank(pageVo.getBusProcessId())) {
            wrapper.eq("bus_process_id", pageVo.getBusProcessId());
        }
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.and(queryWrapper -> queryWrapper.like("name", pageVo.getKeyWord())
                    .or()
                    .like("code", pageVo.getKeyWord())
                    .or()
                    .like("description", pageVo.getKeyWord()));
        }
        if (StringUtils.isNotBlank(pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getSortType())) {
            wrapper.last("order by " + StrUtil.toSymbolCase(pageVo.getColumn(), '_') + " " + pageVo.getSortType());
        } else {
            wrapper.orderByAsc("order_by");
        }
        IPage<Dimension> iPage = dimensionDao.selectPage(page, wrapper);
        List<DimensionVo.PageResponse> responseList = BeanUtil.copyList(iPage.getRecords(), DimensionVo.PageResponse.class);
        for (DimensionVo.PageResponse response : responseList) {
            // 设置目录
            if (response.getDataType() == 1) {
                response.setTarget(plateService.getById(response.getPlateId()).getName());
            } else {
                response.setTarget(plateService.getById(response.getPlateId()).getName() +
                        (StringUtils.isNotBlank(response.getDataFieldId()) ? ("-" + dataFieldService.getById(response.getDataFieldId()).getName()) : "") +
                        (StringUtils.isNotBlank(response.getBusProcessId()) ? ("-" + businessProcessService.getById(response.getBusProcessId()).getName()) : "")
                );
            }
        }
        DimensionVo.DimensionPageResult<DimensionVo.PageResponse> result = new DimensionVo.DimensionPageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setList(responseList);
        result.setTotal(iPage.getTotal());
        result.setCheckingTotal(getCheckingStatusList(pageVo));
        return result;
    }

    public int getCheckingStatusList(DimensionVo.PageVo pageVo) {
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId());
        wrapper.eq("if_deleted", false);
        wrapper.eq("data_type", pageVo.getDataType());
        wrapper.eq("check_status", 3);
        if (StringUtils.isBlank(pageVo.getPlateId()) && StringUtils.isBlank(pageVo.getDataFieldId()) && StringUtils.isBlank(pageVo.getBusProcessId())) {
            //添加板块控制
            List<Plate> plateList = plateService.getListByCompanyId(CurrentUserUtil.get().getCompanyId());
            if (ObjectUtil.isEmpty(plateList)) {
                return 0;
            }
            List<String> plateIds = new ArrayList<>();
            plateList.forEach(plate -> plateIds.add(plate.getId()));
            wrapper.in("plate_id", plateIds);
        } else if (StringUtils.isNotBlank(pageVo.getPlateId())) {
            wrapper.eq("plate_id", pageVo.getPlateId());
        } else if (StringUtils.isNotBlank(pageVo.getDataFieldId())) {
            wrapper.eq("data_field_id", pageVo.getDataFieldId());
        } else if (StringUtils.isNotBlank(pageVo.getBusProcessId())) {
            wrapper.eq("bus_process_id", pageVo.getBusProcessId());
        }
        return super.count(wrapper);
    }

    /**
     * 检查字段名是否合法
     *
     * @param schemaCodeField 字段名
     * @return boolean
     */
    public boolean isCodeLegal(String schemaCodeField) {
        return StrUtil.containsBlank(schemaCodeField);
    }

    /**
     * 检查维度属性字段名是否合法
     *
     * @param dimAttrList 维度属性字段名
     * @return boolean
     */
    public boolean isCodeLegal1(List<DimensionVo.AddDimension.DimAttr> dimAttrList) {
        for (DimensionVo.AddDimension.DimAttr dimAttr : dimAttrList) {
            if (StringUtils.isNotBlank(dimAttr.getName())) {
                return StrUtil.containsBlank(dimAttr.getCode());
            }
        }
        return false;
    }

    /**
     * 校验重名
     *
     * @param dimension 对象
     * @param name      名称
     * @return bool
     */
    @Override
    public boolean isNameDuplicate(Dimension dimension, String name) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0).eq("data_type", dimension.getDataType()).eq("company_id", companyId);

        if (dimension.getId() != null) {
            wrapper.ne("id", dimension.getId());
        }

        wrapper.eq("name", name);
        wrapper.eq(StringUtils.isNotBlank(dimension.getPlateId()), "plate_id", dimension.getPlateId());
        wrapper.eq(StringUtils.isNotBlank(dimension.getDataFieldId()), "data_field_id", dimension.getDataFieldId());
        wrapper.eq(StringUtils.isNotBlank(dimension.getBusProcessId()), "bus_process_id", dimension.getBusProcessId());

        wrapper.last("LIMIT 1");

        Dimension one = super.getOne(wrapper);
        return one != null;
    }

    /**
     * 校验重复编码
     *
     * @param dimension 对象
     * @param code      编码
     * @return bool
     */
    public boolean isCodeDuplicate(Dimension dimension, String code) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", 0).eq("data_type", dimension.getDataType()).eq("company_id", companyId);

        if (dimension.getId() != null) {
            wrapper.ne("id", dimension.getId());
        }

        wrapper.eq("code", code);
        wrapper.eq(StringUtils.isNotBlank(dimension.getPlateId()), "plate_id", dimension.getPlateId());
        wrapper.eq(StringUtils.isNotBlank(dimension.getDataFieldId()), "data_field_id", dimension.getDataFieldId());
        wrapper.eq(StringUtils.isNotBlank(dimension.getBusProcessId()), "bus_process_id", dimension.getBusProcessId());

        wrapper.last("LIMIT 1");

        Dimension one = super.getOne(wrapper);
        return one != null;
    }

    /**
     * 详情
     *
     * @param id ID
     * @return 详情
     */
    public DimensionVo.DetailResponse detail(String id) {
        Dimension dimension = super.getById(id);
        DimensionVo.DetailResponse response = BeanUtil.copy(dimension, DimensionVo.DetailResponse.class);
        List<DimensionVo.AddDimension.DimAttr> attrList = BeanUtil.copyList(dimAttributeService.getList(id), DimensionVo.AddDimension.DimAttr.class);
        for (DimensionVo.AddDimension.DimAttr attr : attrList) {
            if (StringUtils.isNotBlank(attr.getUnionDimId()) && StringUtils.isNotBlank(attr.getUnionDimAttrId())) {
                attr.setDimName(super.getById(attr.getUnionDimId()).getName());
                attr.setDimEnName(super.getById(attr.getUnionDimId()).getCode());
                attr.setDimAttrName(dimAttributeService.getById(attr.getUnionDimAttrId()).getName());
                attr.setDimAttrEnName(dimAttributeService.getById(attr.getUnionDimAttrId()).getCode());
            }
        }
        response.setAttrList(attrList);
        return response;
    }

    /**
     * 查询列表
     *
     * @param type    类型
     * @param keyWord 模糊搜索
     * @return List
     */
    public List<Dimension> getListByType(Integer type, String keyWord, String companyId) {
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId)
                .eq("if_deleted", false)
                .eq("data_type", type);
        if (StringUtils.isNotBlank(keyWord)) {
            wrapper.and(queryWrapper -> queryWrapper.like("name", keyWord)
                    .or()
                    .like("code", keyWord));
        }
        wrapper.orderByDesc("order_by", "create_time");
        return super.list(wrapper);
    }

    /**
     * 根据板块ID和维度编码查询维度/事实表信息
     *
     * @param plateId 板块ID
     * @param code    维度编码
     * @return 维度/事实表信息
     */
    public Dimension getDimensionByPlateIdAndCode(String plateId, String code) {
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("plate_id", plateId).eq("if_deleted", 0).eq("code", code).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 同步维度/事实表信息
     *
     * @param dimId 维度/事实表ID
     * @return boolean
     */
    public boolean syncDimension(String dimId, String companyId) {
        boolean f = false;
        Dimension dimension = super.getById(dimId);
        if (null != dimension) {
            CompanyDremioDTO dremioDTO = dremioRpcService.getCompanyById(companyId);
            if (null != dremioDTO) {
                Plate plate = plateService.getById(dimension.getPlateId());
                if (null != plate) {
                    PlateLayer plateLayer = plateLayerService.getById(dimension.getPlateLayerId());
                    if (null != plateLayer) {
                        String dataSetId = dremioApiService.createDataSet(dremioDTO.getDremioUsername(), dremioDTO.getDremioPasswd(), dremioDTO.getDremioApiUri(), "dataset",
                                dimension.getCode(), plate.getPlateCode(), plateLayer.getCode(), createDataSetSql(dimension));
                        if (StrUtil.isNotBlank(dataSetId)) {
                            // 修改维度状态
                            dimension.setStatus(2);
                            dimension.setDatasetId(dataSetId);
                            return super.updateById(dimId, dimension);
                        }
                    }
                }
            }
        }
        return f;
    }

    /**
     * 根据板块同步维度数据
     *
     * @param plateId   板块ID
     * @param companyId 公司ID
     */
    public void syncByPlate(String plateId, String companyId) {
        List<Dimension> list = this.getDimListByUnionId(plateId, 1, null);
        for (Dimension dimension : list) {
            syncDimension(dimension.getId(), companyId);
        }
    }

    /**
     * 创建SQL
     *
     * @param dimension 维度/事实表信息
     * @return SQL
     */
    private String createDataSetSql(Dimension dimension) {
        String prefix = String.format(
                "-- 内容说明: %s   %s\n" +
                        "-- 创建人: %s(%s)\n" +
                        "-- 创建时间: %s   创建途径:数据规划智能创建\n" +
                        "-- 修改时间:              修改说明:\n" +
                        "-- 特殊说明:\n" +
                        "-- =============================================\n",
                dimension.getName(),
                dimension.getCode(),
                CurrentUserUtil.get().getRealName(),
                CurrentUserUtil.get().getThirdUserId(),
                DateUtil.format(new Date(), "yyyy-MM-dd"));
        StringBuilder builder = new StringBuilder(prefix);
        builder.append("select \n\t");
        List<DimAttribute> dimAttributes = dimAttributeService.listByDimId(dimension.getId());
        if (ObjectUtil.isEmpty(dimAttributes)) {
            builder.append("\n\t,");
        }
        for (DimAttribute dimAttribute : dimAttributes) {
            builder.append("'' as ").append(dimAttribute.getCode()).append(" --").append(dimAttribute.getName());
            builder.append("\n\t,");
        }
        return builder.deleteCharAt(builder.lastIndexOf(",")).toString();
    }

    /**
     * 查询数据总条数
     *
     * @return 总条数
     */
    public int selectTotal(Integer type) {
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("data_type", type).eq("if_deleted", false).eq("company_id", CurrentUserUtil.get().getCompanyId());
        return super.count(wrapper);
    }

    /**
     * 获取统计周期列表
     *
     * @return 统计周期列表
     */
    public List<DictDTO> getPeriodList() {
        return dictRpcService.list("Period");
    }

    /**
     * 根据统计周期字典值获取统计周期信息
     *
     * @param dictValue 计周期字典值
     * @return 统计周期信息
     */
    public DictDTO getPeriodByValue(String dictValue) {
        return dictRpcService.getByTypeAndValue("Period", dictValue);
    }

    /**
     * 根据目录ID和维度/事实表类型获取 维度/事实列表
     *
     * @param foreignId 目录ID
     * @param type      维度/事实表类型 1:维度 2:事实表
     * @return 维度/事实列表
     */
    public List<Dimension> getListByForeignIdAndType(String foreignId, Integer type) {
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("data_type", type)
                .eq("if_deleted", false);
        if (type == 1) {
            wrapper.eq("plate_id", foreignId);
        } else {
            wrapper.and(queryWrapper -> queryWrapper.eq("plate_id", foreignId)
                    .or()
                    .eq("data_field_id", foreignId)
                    .or()
                    .eq("bus_process_id", foreignId));
        }
        wrapper.orderByDesc("order_by", "create_time");
        return super.list(wrapper);
    }

    /**
     * 根据维度ID获取类型
     *
     * @param dimId 维度ID
     * @return 类型
     */
    public Integer getTypeByDimId(String dimId) {
        Dimension dimension = super.getById(dimId);
        return dimension == null ? null : dimension.getDataType();
    }

    /**
     * 校验表字段
     *
     * @param field     参数
     * @param dremioDto 数据源信息
     */
    public void checkFields(DimensionVo.CheckField field, CompanyDremioDTO dremioDto, String companyId) {
        // 获取维度表集合
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId);
        wrapper.eq("if_deleted", false);
        wrapper.eq("data_type", field.getDataType());
        if (StringUtils.isBlank(field.getPlateId()) && StringUtils.isBlank(field.getDataFieldId()) && StringUtils.isBlank(field.getBusProcessId())) {
            return;
        }
        if (StringUtils.isNotBlank(field.getPlateId())) {
            wrapper.eq("plate_id", field.getPlateId());
        }
        if (StringUtils.isNotBlank(field.getDataFieldId())) {
            wrapper.eq("data_field_id", field.getDataFieldId());
        }
        if (StringUtils.isNotBlank(field.getBusProcessId())) {
            wrapper.eq("bus_process_id", field.getBusProcessId());
        }
        List<Dimension> list = super.list(wrapper);
        if (list.isEmpty()) {
            return;
        }
        for (Dimension dim : list) {
            Date date = new Date();
            dim.setCheckTime(date);
            List<String> attrs = new ArrayList<>();
            List<String> fields = new ArrayList<>();
            List<DimAttribute> attributeList = dimAttributeService.listByDimId(dim.getId());
            if (attributeList.isEmpty()) {
                // 更新检查状态和时间
                dim.setCheckStatus(5);
                dim.setCheckMessage(DIMENSION_FIELD_IS_EMPTY);
                super.updateById(dim.getId(), dim);
                continue;
            }
            for (DimAttribute attribute : attributeList) {
                attrs.add(attribute.getCode());
            }
            //有datasetID时
//            if (StringUtils.isNotBlank(dim.getDatasetId())) {
//                List<Map> viewFields = dremioApiService.getViewFields(dremioDto.getDremioUsername(), dremioDto.getDremioPasswd(), dremioDto.getDremioApiUri(), dim.getDatasetId());
//                if (viewFields.isEmpty()) {
//                    // 更新检查状态和时间
//                    dim.setCheckStatus(2);
//                    dim.setCheckMessage(DATA_LAKE_VIEW_NOT_FOUND);
//                    super.updateById(dim.getId(), dim);
//                    continue;
//                }
//                for (Map map : viewFields) {
//                    fields.add(map.get("name").toString());
//                }
//                if (ListUtils.isEqualList(attrs, fields)) {
//                    // 更新检查状态和时间
//                    dim.setCheckStatus(1);
//                    dim.setCheckMessage(SUCCEED_MSG);
//                } else {
//                    // 更新检查状态和时间
//                    dim.setCheckStatus(2);
//                    dim.setCheckMessage(FAILED_MSG);
//                }
//            } else {
            Plate plate = plateService.getById(dim.getPlateId());
            if (plate == null || StringUtils.isBlank(plate.getPlateCode())) {
                // 更新检查状态和时间
                dim.setCheckStatus(2);
                dim.setCheckMessage(DATA_LAKE_VIEW_NOT_FOUND);
                super.updateById(dim.getId(), dim);
                continue;
            }
            PlateLayer plateLayer = plateLayerService.getById(dim.getPlateLayerId());
            if (plateLayer == null || StringUtils.isBlank(plateLayer.getCode())) {
                // 更新检查状态和时间
                dim.setCheckStatus(2);
                dim.setCheckMessage(DATA_LAKE_VIEW_NOT_FOUND);
                super.updateById(dim.getId(), dim);
                continue;
            }
            List<Map> dataSets;
            try {
                dataSets = dremioApiService.getDataSetByPath(dremioDto.getDremioUsername(), dremioDto.getDremioPasswd(), dremioDto.getDremioApiUri(), plate.getPlateCode() + "/" + plateLayer.getCode());
            } catch (Exception e) {
                // 更新检查状态和时间
                dim.setCheckStatus(2);
                dim.setCheckMessage(API_EXECUTE_ERROR + e.getCause());
                super.updateById(dim.getId(), dim);
                continue;
            }
            if (dataSets.isEmpty()) {
                // 更新检查状态和时间
                dim.setCheckStatus(2);
                dim.setCheckMessage(DATA_LAKE_VIEW_NOT_FOUND);
                super.updateById(dim.getId(), dim);
                continue;
            }

            // 判断dremio中是否有该表
            Map map2 = dataSets.stream().filter(map -> ((List) map.get("path")).contains(dim.getCode())).findAny().orElse(null);
            if (null == map2) {
                // 更新检查状态和时间
                dim.setCheckStatus(2);
                dim.setCheckMessage(DATA_LAKE_VIEW_NOT_FOUND);
                super.updateById(dim.getId(), dim);
                continue;
            }

            for (Map map : dataSets) {
                List path = (List) map.get("path");
                if (path.contains(dim.getCode())) {
                    List<Map> viewFields = dremioApiService.getViewFields(dremioDto.getDremioUsername(), dremioDto.getDremioPasswd(), dremioDto.getDremioApiUri(), map.get("id").toString());
                    if (viewFields.isEmpty()) {
                        // 更新检查状态和时间
                        dim.setCheckStatus(2);
                        dim.setCheckMessage(DATA_LAKE_VIEW_NOT_FOUND);
                        super.updateById(dim.getId(), dim);
                        continue;
                    }
                    for (Map map1 : viewFields) {
                        fields.add(map1.get("name").toString());
                    }
                    if (ListUtils.isEqualList(attrs, fields)) {
                        // 更新检查状态和时间
                        dim.setCheckStatus(1);
                        dim.setCheckMessage(SUCCEED_MSG);
                    } else {
                        // 更新检查状态和时间
                        dim.setCheckStatus(4);
                        dim.setCheckMessage(FAILED_MSG);
                    }
                }
            }
//            }
            super.updateById(dim.getId(), dim);
        }

    }

    /**
     * 修改模型检查状态
     *
     * @param field 参数
     * @return bool
     */
    public boolean updateCheckStatus(DimensionVo.CheckField field) {
        // 获取维度表集合
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId());
        wrapper.eq("if_deleted", false);
        wrapper.eq("data_type", field.getDataType());
        if (StringUtils.isBlank(field.getPlateId()) && StringUtils.isBlank(field.getDataFieldId()) && StringUtils.isBlank(field.getBusProcessId())) {
            return true;
        }
        if (StringUtils.isNotBlank(field.getPlateId())) {
            wrapper.eq("plate_id", field.getPlateId());
        }
        if (StringUtils.isNotBlank(field.getDataFieldId())) {
            wrapper.eq("data_field_id", field.getDataFieldId());
        }
        if (StringUtils.isNotBlank(field.getBusProcessId())) {
            wrapper.eq("bus_process_id", field.getBusProcessId());
        }
        List<Dimension> list = super.list(wrapper);
        if (list.isEmpty()) {
            return true;
        }
        for (Dimension dimension : list) {
            dimension.setCheckTime(new Date());
            dimension.setCheckStatus(3);
        }
        return super.saveOrUpdateBatch(list);
    }

    /**
     * 根据板块ID和维度/事实表名称获取信息
     *
     * @param plateId 板块ID
     * @param name    维度/事实表名称
     * @return 维度/事实表信息
     */
    public Dimension getByPlateIdAndName(String plateId, String name) {
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false);
        wrapper.eq("data_type", 1);
        wrapper.eq("plate_id", plateId).eq("name", name).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 通知台账表有事实表字段关联指标
     *
     * @param dwsId     指标ID
     * @param dimId     维度表ID
     * @param unionList 关联列表
     */
    public void callRawUpdate(String companyId, String dwsId, String dimId, List<DwsVo.DimensionUnion> unionList) {
        if (StringUtils.isNotBlank(dimId)) {
            // 查询关联的台账表
            List<RawTable> rawTables = rawTableService.getListByDimId(companyId, dimId);
            for (RawTable rawTable : rawTables) {
                UpdateWrapper<RawTable> wrapper = new UpdateWrapper<>();
                wrapper.eq("id", rawTable.getId()).set("dws_id", dwsId);
                rawTableService.update(wrapper);
            }
        } else {
            for (DwsVo.DimensionUnion union : unionList) {
                // 查询关联的台账表
                List<RawTable> rawTables = rawTableService.getListByDimId(companyId, union.getDimId());
                for (RawTable rawTable : rawTables) {
                    UpdateWrapper<RawTable> wrapper = new UpdateWrapper<>();
                    wrapper.eq("id", rawTable.getId()).set("dws_id", dwsId);
                    rawTableService.update(wrapper);
                }
            }
        }
    }

    /**
     * 数据查询
     *
     * @param param     查询参数
     * @param dimension 维度/事实表
     * @param plate     板块
     * @param dremioDTO 数据源
     * @return PageResult
     */
    public PageResult<Map<String, Object>> queryData(QueryDataVo.QueryRequest param, Dimension dimension, Plate plate, CompanyDremioDTO dremioDTO) {
        PageResult<Map<String, Object>> result = new PageResult<>();
        result.setPageNo(param.getPageNo());
        result.setPageSize(param.getPageSize());

        // 查询列
        List<String> queryColumns = new ArrayList<>();
        // 查询表
        List<String> queryTables = new ArrayList<>();
        queryTables.add(getQueryTable(dimension));
        // 查询表及表字段
        List<DimAttribute> attrs = dimAttributeService.listByIds(param.getTableAttrIds());

        for (DimAttribute attr : attrs) {
            // 获取所在表
            queryColumns.add(dimension.getCode() + StrUtil.DOT + attr.getCode() + " AS " + CharUtil.DOUBLE_QUOTES + dimension.getCode() + SqlBuilderUtil.DOLLAR + attr.getCode() + CharUtil.DOUBLE_QUOTES);
        }

        // 组装SQL
        String sql = SqlBuilderUtil.buildSql(false, queryColumns, queryTables, null, param.getFilters(), null, param.getOrders(), null);
        // 分页SQL
        String pageSql = SqlBuilderUtil.buildPageSql(sql, new Page(param.getPageNo(), param.getPageSize()));
        // 总数SQL
        String countSql = SqlBuilderUtil.buildCountSql(sql);

        // 查询数据
        List<Map<String, Object>> data = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, pageSql));
        if (null != getErrorValues(data)) {
            throw new QueryFailedException(getErrorValues(data));
        }
        result.setList(data);
        List<Map<String, Object>> total = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, countSql));
        if (null != getErrorValues(total)) {
            throw new QueryFailedException(getErrorValues(total));
        }
        result.setTotal(Optional.of(total)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(map -> map.get("total"))
                .map(Object::toString)
                .map(Long::valueOf)
                .orElse(0L));
        return result;
    }

    /**
     * 查询IN列表
     *
     * @param param 查询参数
     * @return T
     */
    public PageResult<Map<String, Object>> queryInData(QueryDataVo.InQueryRequest param, Plate plate, CompanyDremioDTO dremioDTO) {
        PageResult<Map<String, Object>> result = new PageResult<>();
        result.setPageNo(param.getPageNo());
        result.setPageSize(param.getPageSize());
        DimAttribute attr = dimAttributeService.getById(param.getTableAttrId());
        // 查询列
        List<String> queryColumns = new ArrayList<>();
        // 查询表
        List<String> queryTables = new ArrayList<>();
        // 筛选列
        List<SqlBuilderVo.Filter> filters = new ArrayList<>();

        // 获取所在表
        Dimension dimension = super.getById(attr.getDimId());
        queryColumns.add(dimension.getCode() + StrUtil.DOT + attr.getCode() + " AS " + CharUtil.DOUBLE_QUOTES + dimension.getCode() + SqlBuilderUtil.DOLLAR + attr.getCode() + CharUtil.DOUBLE_QUOTES);
        queryTables.add(getQueryTable(dimension));

        SqlBuilderVo.Filter filter = new SqlBuilderVo.Filter();
        filter.setField(dimension.getCode() + StrUtil.DOT + attr.getCode());
        filter.setOperator(SqlBuilderUtil.OPERATOR_IS_NOT);
        filters.add(filter);

        if (StringUtils.isNoneBlank(param.getKeyWord())) {
            filter = new SqlBuilderVo.Filter();
            filter.setField(dimension.getCode() + StrUtil.DOT + attr.getCode());
            filter.setOperator(SqlBuilderUtil.OPERATOR_LIKE);
            filter.setValue(param.getKeyWord());
            filters.add(filter);
        }

        // 组装SQL
        String sql = SqlBuilderUtil.buildSql(true, queryColumns, queryTables, null, filters, null, param.getOrders(), null);
        // 分页SQL
        String pageSql = SqlBuilderUtil.buildPageSql(sql, new Page(param.getPageNo(), param.getPageSize()));
        // 总数SQL
        String countSql = SqlBuilderUtil.buildCountSql(sql);

        // 查询数据
        List<Map<String, Object>> data = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, pageSql));
        if (null != getErrorValues(data)) {
            throw new QueryFailedException(getErrorValues(data));
        }
        List<Map<String, Object>> total = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, countSql));
        List<String> res = new ArrayList<>();
        for (Map<String, Object> map : data) {
            Object o = map.get(dimension.getCode() + SqlBuilderUtil.DOLLAR + attr.getCode());
            res.add(null == o ? null : o.toString());
        }
        if (null != getErrorValues(total)) {
            throw new QueryFailedException(getErrorValues(total));
        }
        result.setList(data);
        result.setTotal(Optional.of(total)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .map(map -> map.get("total"))
                .map(Object::toString)
                .map(Long::valueOf)
                .orElse(0L));
        return result;
    }

    /**
     * 封装目录+表名
     *
     * @param dimension 维度信息
     * @return s
     */
    private String getQueryTable(Dimension dimension) {
        // 查询维度表所在的分层信息
        PlateLayer plateLayer = plateLayerService.getById(dimension.getPlateLayerId());
        if (plateLayer == null) {
            return null;
        }
        return plateLayer.getCode() + StrUtil.DOT + dimension.getCode();
    }


    /**
     * dremio 信息 转换为通用连接参数
     *
     * @param dremioDTO dremio 信息
     * @param sql       查询SQL
     * @return DbDto.DbQueryBySql
     */
    public DbDto.DbQueryBySql convertByDremioInfo(CompanyDremioDTO dremioDTO, Plate plate, String sql) {
        DbDto.DbQueryBySql dbQueryBySql = new DbDto.DbQueryBySql();
        dbQueryBySql.setHost(dremioDTO.getDremioHost());
        dbQueryBySql.setPort(dremioDTO.getDremioPort());
        dbQueryBySql.setUsername(dremioDTO.getDremioUsername());
        dbQueryBySql.setPassword(dremioDTO.getDremioPasswd());
        dbQueryBySql.setType(DbType.DREMIO.toString());
        dbQueryBySql.setDatabase(plate.getPlateCode());
        dbQueryBySql.setSql(sql);
        return dbQueryBySql;
    }

    /**
     * 查询可选字段列表
     *
     * @param dimension 维度/事实表
     * @return list
     */
    public List<RawTableVo.OptionParam> optionListByDim(Dimension dimension) {
        List<RawTableVo.OptionParam> res = new ArrayList<>();
        RawTableVo.OptionParam param = BeanUtil.copy(dimension, RawTableVo.OptionParam.class);
        // 获取所有字段
        List<DimAttribute> dimAttributes = dimAttributeService.listByDimId(dimension.getId());
        param.setName(dimension.getName());
        param.setTableAttrs(BeanUtil.copyList(dimAttributes, RawTableAttrVo.DetailResponse.class));
        param.setTotal(dimAttributes.size());
        res.add(param);
        return res;
    }

    /**
     * 获取导出数据
     *
     * @return
     */
    public List<Map<String, Object>> getExportData(QueryDataVo.QueryRequest param, Dimension dimension, Plate plate, CompanyDremioDTO dremioDTO) {
        // 查询列
        List<String> queryColumns = new ArrayList<>();
        // 查询表
        List<String> queryTables = new ArrayList<>();
        // 查询表及表字段
        queryTables.add(getQueryTable(dimension));
        // 查询表及表字段
        List<DimAttribute> attrs = dimAttributeService.listByIds(param.getTableAttrIds());
        for (DimAttribute attr : attrs) {
            // 获取所在表
            queryColumns.add(dimension.getCode() + StrUtil.DOT + attr.getCode() + " AS " + CharUtil.DOUBLE_QUOTES + dimension.getCode() + SqlBuilderUtil.DOLLAR + attr.getCode() + CharUtil.DOUBLE_QUOTES);
        }
        // 组装SQL
        String exportSql = SqlBuilderUtil.buildSql(false, queryColumns, queryTables, null, param.getFilters(), null, param.getOrders(), null);
        // 分页SQL
        String pageSql = SqlBuilderUtil.buildPageSql(exportSql, new Page(param.getPageNo(), param.getPageSize()));
        List<Map<String, Object>> data = dbRpcService.queryBySql(convertByDremioInfo(dremioDTO, plate, pageSql));
        return data.stream()
                .map(row -> {
                    Map<String, Object> resMap = new LinkedHashMap<>();
                    for (Map.Entry<String, Object> entry : row.entrySet()) {
                        String[] values = entry.getKey().split("\\$");
                        for (DimAttribute attr : attrs) {
                            if (values[1].equals(attr.getCode())) {
                                String columnName = attr.getAliasName();
                                if (columnName == null || columnName.isEmpty()) {
                                    columnName = attr.getName();
                                }
                                if (columnName == null || columnName.isEmpty()) {
                                    columnName = values[1];
                                }
                                resMap.put(columnName, entry.getValue());
                                break;
                            }
                        }
                    }
                    return resMap;
                })
                .collect(Collectors.toList());
    }

    /**
     * 同步SQL到数据湖中
     *
     * @param dimension 维度/事实表
     * @param companyId 企业ID
     * @param sql       SQL
     * @return bool
     */
    public boolean syncSqlToDremio(Dimension dimension, String companyId, String sql) {
        try {
            CompanyDremioDTO dremioDTO = dremioRpcService.getCompanyById(companyId);
            if (null != dremioDTO) {
                Plate plate = plateService.getById(dimension.getPlateId());
                if (null != plate) {
                    PlateLayer plateLayer = plateLayerService.getById(dimension.getPlateLayerId());
                    if (null != plateLayer) {
                        return dremioApiService.updateDataSet(
                                dremioDTO.getDremioUsername(),
                                dremioDTO.getDremioPasswd(),
                                dremioDTO.getDremioApiUri(),
                                dimension.getDatasetId(),
                                "dataset",
                                dimension.getCode(), plate.getPlateCode(), plateLayer.getCode(), sql);
                    }
                }
            }
        } catch (Exception e) {
            // 记录日志并抛出自定义异常，以便Controller层处理
            logger.error("同步SQL到数据湖时发生异常: {}", ExceptionUtil.stacktraceToString(e));
            throw new RuntimeException("同步SQL到数据湖失败: " + e.getMessage(), e);
        }
        return false;
    }

    /**
     * 根据维度/事实表信息获取数据湖中的SQL
     *
     * @param dimension 维度/事实表信息
     * @param companyId 企业ID
     * @return SQL
     */
    public String getDremioSqlByDimId(Dimension dimension, String companyId) {
        if (StringUtils.isBlank(dimension.getDatasetId())) {
            return null;
        }
        CompanyDremioDTO dremioDTO = dremioRpcService.getCompanyById(companyId);
        if (null == dremioDTO) {
            return null;
        }
        try {
            return dremioApiService.getViewSql(dremioDTO.getDremioUsername(),
                    dremioDTO.getDremioPasswd(),
                    dremioDTO.getDremioApiUri(),
                    dimension.getDatasetId());
        } catch (Exception e) {
            logger.error("获取数据湖中的SQL失败，{}", ExceptionUtil.stacktraceToString(e));
            throw new RuntimeException("获取数据湖中的SQL失败：" + e.getMessage(), e);
        }
    }

    /**
     * 校验维度/事实表是否存在于数据湖中
     *
     * @param dimension  维度/事实表西悉尼
     * @param plate      板块信息
     * @param plateLayer 数仓分层信息
     * @param dremioDto  数据湖连接信息
     * @return bool
     */
    public boolean checkDremioHasThisDimension(Dimension dimension, Plate plate, PlateLayer plateLayer, CompanyDremioDTO dremioDto) {
        List<String> path = Arrays.asList(plate.getPlateCode(), plateLayer.getCode(), dimension.getCode());
        List<Map> dataSets;
        try {
            dataSets = dremioApiService.getDataSetByPath(dremioDto.getDremioUsername(),
                    dremioDto.getDremioPasswd(),
                    dremioDto.getDremioApiUri(),
                    plate.getPlateCode() + "/" + plateLayer.getCode());
        } catch (Exception e) {
            logger.error(e.getMessage());
            throw new DremioException(e.getMessage());
        }
        for (Map map : dataSets) {
            List<String> paths = (List<String>) map.get("path");
            if (path.equals(paths)) {
                // 修改维度状态
                dimension.setStatus(2);
                dimension.setDatasetId(map.get("id").toString());
                return super.updateById(dimension.getId(), dimension);
            }
        }
        return false;
    }

    /**
     * 搜索
     *
     * @param param 参数
     * @return List
     */
    public List<Map<String, Object>> getSearchDimensionList(DimensionVo.SearchParam param, String companyId) {
        return dimensionDao.selectSearchDimensionList(companyId, param.getDataType(), param.getKeyWord());
    }

    /**
     * 获取虚拟表字段
     *
     * @param dimension 维度/事实表信息
     * @param dremioDto 数据湖连接信息
     * @return List
     */
    public List<Map> getViewFields(Dimension dimension, CompanyDremioDTO dremioDto) {
        try {
            return dremioApiService.getViewFields(dremioDto.getDremioUsername(), dremioDto.getDremioPasswd(), dremioDto.getDremioApiUri(), dimension.getDatasetId());
        } catch (Exception e) {
            logger.error("获取数据湖中的字段失败，{}", ExceptionUtil.stacktraceToString(e));
            throw new RuntimeException("获取数据湖中的字段失败：" + e.getMessage(), e);
        }
    }

    /**
     * 更新维度文档
     *
     * @param id  维度ID
     * @param doc 文档
     * @return bool
     */
    public boolean updateDoc(String id, String doc) {
        UpdateWrapper<Dimension> wrapper = new UpdateWrapper<>();
        wrapper.set("doc", doc).eq("id", id);
        return super.update(wrapper);
    }

    /**
     * 新增维度/事实表信息
     *
     * @param addDimension 对象
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public String insertDimensionByInsight(InsightVo.AddDimension addDimension) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Dimension dimension = BeanUtil.copy(addDimension, Dimension.class);
        dimension.setCompanyId(companyId);
        dimension.setType(1);
        dimension.setOrderBy(getMaxOrder());
        // 新增维度
        boolean f = this.save(dimension);
        if (f) {
            //新增维度属性
            f = addDimAttrList(BeanUtil.copyList(addDimension.getAttrList(), DimensionVo.AddDimension.DimAttr.class), dimension.getId(), addDimension.getDataType());
            if (f) {
                // 创建dataset
                syncDimension(dimension.getId(), companyId);
            }
        }
        return dimension.getId();
    }

    /**
     * 编辑维度/事实表信息
     *
     * @param addDimension 对象
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean editDimensionByInsight(InsightVo.AddDimension addDimension) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Dimension dimension = BeanUtil.copy(addDimension, Dimension.class);
        dimension.setCompanyId(companyId);
        // 删除关联字段信息
        boolean f = dimAttributeService.deletedByDimId(addDimension.getId());
        if (f) {
            // 设置为未同步状态
            dimension.setStatus(1);
            //修改维度/事实表信息
            f = super.updateById(dimension.getId(), dimension);
            if (f) {
                //新增关联的字段信息
                f = addDimAttrList(BeanUtil.copyList(addDimension.getAttrList(), DimensionVo.AddDimension.DimAttr.class), dimension.getId(), addDimension.getDataType());
            }
        }
        return f;
    }

    /**
     * 逻辑删除维度/事实表信息
     *
     * @param id
     * @return
     */
    public boolean logicDeleteDimensionByInsight(String id) {
        return this.delete(id);
    }

    /**
     * 根据企业ID获取维度/事实表列表
     *
     * @param companyId 企业ID
     * @return List
     */
    public List<Dimension> getListByCompanyId(String companyId) {
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId).eq("if_deleted", false);
        return super.list(wrapper);
    }

    /**
     * 根据ID列表获取维度/事实表列表
     *
     * @param dimIds    ID列表
     * @param companyId 企业ID
     * @return List
     */
    public List<Dimension> getListByIds(String dimIds, String companyId) {
        // 检查输入参数
        if (dimIds == null || dimIds.trim().isEmpty()) {
            logger.warn("dimIds is null or empty");
            return super.list(new QueryWrapper<Dimension>().eq("company_id", companyId).eq("if_deleted", false));
        }
        // 处理 dimIds 并去除空字符串
        List<String> idList = Arrays.stream(dimIds.split(","))
                .map(String::trim)
                .filter(id -> !id.isEmpty())
                .collect(Collectors.toList());

        if (idList.isEmpty()) {
            logger.warn("No valid ids found in dimIds");
            return super.list(new QueryWrapper<Dimension>().eq("company_id", companyId).eq("if_deleted", false));
        }

        // 构建查询条件
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.in("id", idList).eq("company_id", companyId).eq("if_deleted", false);

        return super.list(wrapper);
    }

    /**
     * 数据模型查询分页
     *
     * @param pageVo 分页参数
     * @return PageResult
     */
    public PageResult getPageByDataModel(DimensionVo.DataModelPageVo pageVo) {
        Page<Dimension> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        page.setOptimizeCountSql(false);
        QueryWrapper<Dimension> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false)
                .eq("if_insight", false)
                .eq(null != pageVo.getDataType(), "data_type", pageVo.getDataType())
                .eq(StringUtils.isNotBlank(pageVo.getOrgId()), "org_id", pageVo.getOrgId())
                .eq(StringUtils.isNotBlank(pageVo.getLineId()), "line_id", pageVo.getLineId())
                .eq(StringUtils.isNotBlank(pageVo.getBvId()), "bv_id", pageVo.getBvId())
                .eq(StringUtils.isNotBlank(pageVo.getSourceId()), "source_id", pageVo.getSourceId())
                .like(StringUtils.isNotBlank(pageVo.getKeyWord()), "name", pageVo.getKeyWord())
                .eq(StrUtil.isNotBlank(pageVo.getId()), "id", pageVo.getId());
        if (StringUtils.isNotBlank(pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getSortType())) {
            wrapper.last("order by " + StrUtil.toSymbolCase(pageVo.getColumn(), '_') + " " + pageVo.getSortType());
        } else {
            wrapper.orderByAsc("order_by").orderByDesc("id");
        }
        IPage<Dimension> iPage = dimensionDao.selectPage(page, wrapper);
        return getPageResult(page, iPage);
    }
}