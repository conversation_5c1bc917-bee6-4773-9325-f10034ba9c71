package com.taiyi.shuduoduo.ims.kestra.api;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.taiyi.shuduoduo.ims.kestra.KestraResponse;
import com.taiyi.shuduoduo.ims.kestra.vo.KestraVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Component
public class KestraApi {

    protected final Logger logger = LoggerFactory.getLogger(KestraApi.class);

    private static final String BASE_URI = "http://linker.aheada.cn:18090";

    /**
     * execute flow
     */
    private static final String EXECUTE_FLOW_URI = "/api/v1/executions/trigger/company";

    /**
     * 插件列表
     */
    private static final String PLUGINS_URI = "/api/v1/plugins";

    /**
     * 执行日志
     */
    private static final String GET_LOG__URI = "/api/v1/logs/";

    /**
     * 创建流程
     */
    private static final String SAVE_FLOW_URI = "/api/v1/flows";

    /**
     * 校验流程
     */
    private static final String VALID_FLOW_URI = "/api/v1/flows/validate";

    /**
     * 执行流程
     */
    private static final String EXECUTE_EXECUTION_URI = "/api/v1/executions/";

    private static final String EXECUTE_WEBHOOK_URI = "/api/v1/executions/webhook/";

    private static String token = "Basic ********************************";

    /**
     * 设置请求头
     *
     * @return header
     */
    public Map<String, String> getTokenHeader(String contentType) {
        Map<String, String> heardMap = new HashMap<>();
        heardMap.put("Authorization", token);
        heardMap.put("Content-Type", contentType);
        return heardMap;
    }

    /**
     * 执行流程
     *
     * @param webHookUri 用户名
     */
    public KestraResponse executeFlow(String webHookUri, String body) {
        KestraResponse res = new KestraResponse();
        try {
            HttpResponse response;
            if (StringUtils.isBlank(body)) {
                response = HttpRequest.get(webHookUri).contentType(ContentType.JSON.getValue()).execute();
            } else {
                response = HttpRequest.post(webHookUri).body(body).execute();
            }
            logger.warn("流程执行返回结果:{}", response.body());
            res.setOk(response.isOk());
            res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        } catch (Exception e) {
            // 捕获所有异常，防止崩溃
            logger.error("执行流程异常: {}", ExceptionUtil.stacktraceToString(e));
            res.setOk(false);
            res.setMsg("流程执行异常：" + e.getMessage());

        }
        return res;
    }


    public static void main(String[] args) {
        KestraApi api = new KestraApi();
//        System.out.println(api.executeFlow("http://linker.aheada.cn:18090/api/v1/executions/webhook/company/wechat/test", "{\n" +
//                "    \"name\": \"Settimoqq\"\n" +
//                "}"));
        String flow = "id: myflow\n" +
                "namespace: company.myteam\n" +
                "description: Save and Execute the flow\n" +
                "\n" +
                "\n" +
                "tasks:\n" +
                "  - id: print_s\n" +
                "    type: io.kestra.plugin.core.log.Log\n" +
                "    message: hello aaa\n" +
                "\n";
        System.out.println(convertToYaml(flow));
    }

    public static String convertToYaml(String flow) {
        // 使用SnakeYAML将字符串转换为YAML对象
        Yaml yaml = new Yaml();
        Map<String, Object> yamlObject = yaml.load(flow);
        return yaml.dump(yamlObject.get("flow"));
    }

    public KestraResponse createFlow(String flow) {
        HttpResponse response = HttpRequest.post(BASE_URI + SAVE_FLOW_URI).addHeaders(getTokenHeader("application/x-yaml")).body(flow).execute();
        logger.warn("流程创建参数:{},返回结果:{}", flow, response.body());
        KestraResponse res = new KestraResponse();
        res.setOk(response.isOk());
        res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        res.setData(response.isOk() ? JSONObject.parse(response.body()) : null);
        return res;
    }

    public KestraResponse searchFlow(String namespace, String flowId) {
        HttpResponse response = HttpRequest.get(BASE_URI + SAVE_FLOW_URI + "/search?sort=id:asc&size=1&page=1&namespace=" + namespace + "&q=" + flowId).addHeaders(getTokenHeader("application/json")).execute();
        logger.warn("流程创建参数:{},{},返回结果:{}", namespace, flowId, response.body());
        KestraResponse res = new KestraResponse();
        res.setOk(response.isOk());
        res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        res.setData(response.isOk() ? JSONObject.parse(response.body()) : null);
        return res;
    }

    public KestraResponse flowList(String namespace) {
        HttpResponse response = HttpRequest.get(BASE_URI + SAVE_FLOW_URI + "/" + namespace).addHeaders(getTokenHeader("application/json")).execute();
        logger.warn("流程创建参数:{},返回结果:{}", namespace, response.body());
        KestraResponse res = new KestraResponse();
        res.setOk(response.isOk());
        res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        res.setData(response.isOk() ? JSONObject.parseArray(response.body()) : null);
        return res;
    }

    public KestraResponse updateFlow(String namespace, String flowId, String flow) {
        HttpResponse response = HttpRequest.put(BASE_URI + SAVE_FLOW_URI + "/" + namespace + "/" + flowId).addHeaders(getTokenHeader("application/x-yaml")).body(flow).execute();
        logger.warn("流程修改参数:{},{},{},返回结果:{}", namespace, flowId, flow, response.body());
        KestraResponse res = new KestraResponse();
        res.setOk(response.isOk());
        res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        res.setData(response.isOk() ? JSONObject.parse(response.body()) : null);
        return res;
    }

    public KestraResponse validFlow(String flow) {
        KestraResponse res = new KestraResponse();
        HttpResponse response;
        try {
            response = HttpRequest.post(BASE_URI + VALID_FLOW_URI).addHeaders(getTokenHeader("application/x-yaml")).body(flow).execute();
            logger.warn("流程校验参数:{},返回结果:{}", flow, response.body());
            res.setOk(response.isOk());
            res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
            res.setData(response.isOk() ? JSONObject.parseArray(response.body()) : null);
        } catch (Exception e) {
            logger.error("流程校验异常：{}", e.getMessage());
            res.setOk(false);
            res.setMsg(e.getMessage());
        }
        return res;
    }

    public KestraResponse executeFlowByApi(KestraVo vo) {
        HttpResponse response = HttpRequest.post(BASE_URI + EXECUTE_EXECUTION_URI + vo.getNamespace() + "/" + vo.getFlowId()).addHeaders(getTokenHeader("multipart/form-data")).execute();
        logger.warn("流程执行参数:{},返回结果:{}", vo, response.body());
        KestraResponse res = new KestraResponse();
        res.setOk(response.isOk());
        res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        res.setData(response.isOk() ? JSONObject.parse(response.body()) : null);
        return res;
    }

    public KestraResponse getExecutionStatus(String executionId) {
        HttpResponse response = HttpRequest.post(BASE_URI + EXECUTE_EXECUTION_URI + executionId + "/state").addHeaders(getTokenHeader("multipart/form-data")).execute();
        logger.warn("流程执行参数:{},返回结果:{}", executionId, response.body());
        KestraResponse res = new KestraResponse();
        res.setOk(response.isOk());
        res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        res.setData(response.isOk() ? JSONObject.parse(response.body()) : null);
        return res;
    }

    public KestraResponse getPluginList() {
        HttpResponse response = HttpRequest.get(BASE_URI + PLUGINS_URI).addHeaders(getTokenHeader("application/json")).execute();

        logger.warn("插件列表返回结果:{}", response.body());
        KestraResponse res = new KestraResponse();
        res.setOk(response.isOk());
        res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        res.setData(response.isOk() ? JSONObject.parseArray(response.body()) : null);
        return res;
    }

    public KestraResponse getExecuteLog(String executionId) {
        HttpResponse response = HttpRequest.get(BASE_URI + GET_LOG__URI + executionId + "?minLevel=INFO").addHeaders(getTokenHeader("application/json")).execute();
        logger.warn("流程执行日志参数:{}返回结果:{}", executionId, response.body());
        KestraResponse res = new KestraResponse();
        res.setOk(response.isOk());
        res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        res.setData(response.isOk() ? JSONObject.parseArray(response.body()) : null);
        return res;
    }

    /**
     * 获取流程执行详情
     *
     * @param executionId 执行ID
     * @return T
     */
    public KestraResponse getExecuteInfo(String executionId) {
        HttpResponse response = HttpRequest.get(BASE_URI + EXECUTE_EXECUTION_URI + executionId).addHeaders(getTokenHeader("application/json")).execute();
        logger.warn("流程执行详情参数:{}返回结果:{}", executionId, response.body());
        KestraResponse res = new KestraResponse();
        res.setOk(response.isOk());
        res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        res.setData(response.isOk() ? JSONObject.parseObject(response.body()) : null);
        return res;
    }

    /**
     * 获取输出日志
     *
     * @param executionId 执行ID
     * @param path        文件地址  + "&maxRows=100&encoding=UTF-8"
     * @return T
     */
    public KestraResponse getOutputData(String executionId, String path) {
        HttpResponse response = HttpRequest.get(BASE_URI + EXECUTE_EXECUTION_URI + executionId + "/file/preview?executionId=" + executionId + "&path=" + path)
                .addHeaders(getTokenHeader("application/json")).execute();
        logger.warn("流程执行输出参数:{}返回结果:{}", executionId, response.body());
        KestraResponse res = new KestraResponse();
        res.setOk(response.isOk());
        res.setMsg(response.isOk() ? TipsConstant.EXECUTE_FLOW_SUCCEED : TipsConstant.EXECUTE_FLOW_FAILED + JSONObject.parseObject(response.body()).getString("message"));
        res.setData(response.isOk() ? JSON.parseObject(response.body(), Feature.OrderedField) : null);
        return res;
    }
}
