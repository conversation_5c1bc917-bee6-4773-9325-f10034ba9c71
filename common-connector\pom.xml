<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>shuduoduo-v2</artifactId>
        <groupId>com.taiyi</groupId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>common-connector</artifactId>

    <properties>
        <protostuff.version>1.7.2</protostuff.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-common-connector-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>shuduoduo-ums-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.2.14</version>
        </dependency>
        <dependency>
            <groupId>com.oracle</groupId>
            <artifactId>ojdbc6</artifactId>
            <version>11.2.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.dremio</groupId>
            <artifactId>dremiojdbc</artifactId>
            <version>24.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.0</version>
            <scope>compile</scope>
        </dependency>
        <!-- 阿里开源EXCEL -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>1.1.2-beta5</version>
        </dependency>
        <dependency>
            <groupId>com.taiyi</groupId>
            <artifactId>common-data-redis</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <!--  Netty相关      -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${netty.version}</version>
        </dependency>
        <!--   protostuff 字解码     -->
        <!--<dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-api</artifactId>
            <version>${protostuff.version}</version>
        </dependency>-->
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-core</artifactId>
            <version>${protostuff.version}</version>
        </dependency>
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-runtime</artifactId>
            <version>${protostuff.version}</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>com.spotify</groupId>-->
<!--                <artifactId>docker-maven-plugin</artifactId>-->
<!--                <version>${docker-maven-plugin.version}</version>-->
<!--                <executions>-->
<!--                    <execution>-->
<!--                        <id>build-images</id>-->
<!--                        <phase>install</phase>-->
<!--                        <goals>-->
<!--                            <goal>build</goal>-->
<!--                        </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
<!--                <configuration>-->
<!--                    <imageName>shuduoduo-v2/${project.artifactId}:${project.version}</imageName>-->
<!--                    <dockerHost>http://127.0.0.1:2375</dockerHost>-->
<!--                    <dockerDirectory>${basedir}</dockerDirectory>-->
<!--                    <resources>-->
<!--                        <resource>-->
<!--                            <targetPath>/</targetPath>-->
<!--                            <directory>${project.build.directory}</directory>-->
<!--                            <include>${project.build.finalName}.jar</include>-->
<!--                        </resource>-->
<!--                    </resources>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>
    </build>

</project>