package com.taiyi.shuduoduo.ims.task;

import cn.hutool.core.date.DateUtil;
import com.taiyi.shuduoduo.ims.entity.*;
import com.taiyi.shuduoduo.ims.service.*;
import com.taiyi.shuduoduo.nocodb.api.dto.SqliteMasterDTO;
import com.taiyi.shuduoduo.nocodb.api.servie.NcProjectRpcService;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyRpcService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.BiPredicate;

/**
 * 同步组织架构
 *
 * <AUTHOR>
 */
@Component
@Async
public class AuthTableTask {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private NcProjectRpcService ncProjectRpcService;

    @Autowired
    private CompanyRpcService companyRpcService;

    @Autowired
    private AuthNocodbService authNocodbService;

    @Autowired
    private AuthTableService authTableService;

    @Autowired
    private RawAuthService rawAuthService;

    @Autowired
    private DimensionAuthService dimensionAuthService;

    @Autowired
    private RawService rawService;

    @Autowired
    private DimensionService dimensionService;

    @Value("${spring.profiles.active}")
    private String active;

    /**
     * 定时任务  同步台账权限表
     * cron 5分钟一次
     */
    @Scheduled(cron = "0 0/3 * * * ?")
    public void syncRawAuth() {
        //判断当前环境，本地环境不初始化
        if ("local".equals(active)) {
            return;
        }
        long start = System.currentTimeMillis();
        logger.info("同步台账权限表开始:{}", DateUtil.now());
        //同步所有公司的权限表
        //1获取所有公司
        List<CompanyDTO> companyList = companyRpcService.getCompanyList();
        for (CompanyDTO companyDTO : companyList) {
            //2获取公司权限项目配置
            AuthNocodb authNocodb = authNocodbService.getAuthInfoByCompanyId(companyDTO.getId());
            // 未配置权限表信息时，跳过
            if (null == authNocodb) {
                continue;
            }
            //3获取所有权限表
            List<SqliteMasterDTO> tables = ncProjectRpcService.showTablesWithCompanyPrefix(authNocodb.getProjectPrefix());
            // 未配置权限表内容时，跳过
            if (tables.isEmpty()) {
                continue;
            }
            //4 获取所有台账
            List<Raw> rawList = rawService.getListByCompanyId(companyDTO.getId());
            for (SqliteMasterDTO table : tables) {
                AuthTable authTable = authTableService.getByName(table.getName(), companyDTO.getId());
                //5 添加到权限表中
                if (authTable == null) {
                    authTable = new AuthTable();
                    authTable.setCompanyId(companyDTO.getId());
                    authTable.setAuthTable(table.getName());
                    boolean f = authTableService.save(authTable);
                    if (f) {
                        for (Raw raw : rawList) {
                            //6 添加到所有台账中
                            if (rawAuthService.isExist(raw.getId(), authTable.getId())) {
                                RawAuth rawAuth = new RawAuth();
                                rawAuth.setRawId(raw.getId());
                                rawAuth.setAuthTableId(authTable.getId());
                                rawAuthService.save(rawAuth);
                            }
                        }
                    }
                } else {
                    //6 添加到所有台账中
                    for (Raw raw : rawList) {
                        if (rawAuthService.isExist(raw.getId(), authTable.getId())) {
                            RawAuth rawAuth = new RawAuth();
                            rawAuth.setRawId(raw.getId());
                            rawAuth.setAuthTableId(authTable.getId());
                            rawAuthService.save(rawAuth);
                        }
                    }
                }
            }
        }

        logger.info("同步台账权限表结束:{},总耗时:{}ms", DateUtil.now(), System.currentTimeMillis() - start);
    }

    /**
     * 定时任务  同步维度权限表
     * cron 5分钟一次
     */
    @Scheduled(cron = "0 0/3 * * * ?")
    public void syncDimensionAuth() {
        //判断当前环境，本地环境不初始化
        if ("local".equals(active)) {
            return;
        }
        long start = System.currentTimeMillis();
        logger.info("同步维度权限表开始:{}", DateUtil.now());
        try {
            //同步所有公司的权限表
            //1获取所有公司
            List<CompanyDTO> companyList = companyRpcService.getCompanyList();
            for (CompanyDTO companyDTO : companyList) {
                //2获取公司权限项目配置
                AuthNocodb authNocodb = authNocodbService.getAuthInfoByCompanyId(companyDTO.getId());
                // 未配置权限表信息时，跳过
                if (null == authNocodb) {
                    continue;
                }
                //3获取所有权限表
                List<SqliteMasterDTO> tables = ncProjectRpcService.showTablesWithCompanyPrefix(authNocodb.getProjectPrefix());
                // 未配置权限表内容时，跳过
                if (tables.isEmpty()) {
                    continue;
                }
                //4 获取所有维度事实表
                List<Dimension> dimensionList = dimensionService.getListByCompanyId(companyDTO.getId());
                for (SqliteMasterDTO table : tables) {
                    AuthTable authTable = authTableService.getByName(table.getName(), companyDTO.getId());
                    //5 添加到权限表中
                    if (authTable == null) {
                        authTable = new AuthTable();
                        authTable.setCompanyId(companyDTO.getId());
                        authTable.setAuthTable(table.getName());
                        boolean f = authTableService.save(authTable);
                        if (f) {
                            for (Dimension dimension : dimensionList) {
                                //6 添加到所有的维度权限表中
                                if (dimensionAuthService.isExist(dimension.getId(), authTable.getId())) {
                                    DimensionAuth dimensionAuth = new DimensionAuth();
                                    dimensionAuth.setDimId(dimension.getId());
                                    dimensionAuth.setAuthTableId(authTable.getId());
                                    dimensionAuthService.save(dimensionAuth);
                                }
                            }
                        }
                    } else {
                        //6 添加到所有的维度权限表中
                        for (Dimension dimension : dimensionList) {
                            if (dimensionAuthService.isExist(dimension.getId(), authTable.getId())) {
                                DimensionAuth dimensionAuth = new DimensionAuth();
                                dimensionAuth.setDimId(dimension.getId());
                                dimensionAuth.setAuthTableId(authTable.getId());
                                dimensionAuthService.save(dimensionAuth);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("同步维度权限表任务出错: {}", e.getMessage(), e);
        } finally {
            logger.info("同步维度权限表结束:{},总耗时:{}ms", DateUtil.now(), System.currentTimeMillis() - start);
        }

    }

    private <T> void syncTablesWithEntities(CompanyDTO companyDTO, List<SqliteMasterDTO> tables, List<T> entities,
                                            String entityType,
                                            BiPredicate<AuthTable, T> existenceChecker,
                                            BiConsumer<AuthTable, T> entitySaver) {
        if (entities == null || entities.isEmpty()) {
            logger.debug("公司 {} 的{}列表为空，跳过", companyDTO.getId(), entityType);
            return;
        }

        for (SqliteMasterDTO table : tables) {
            AuthTable authTable = authTableService.getByName(table.getName(), companyDTO.getId());
            if (authTable == null) {
                authTable = new AuthTable();
                authTable.setCompanyId(companyDTO.getId());
                authTable.setAuthTable(table.getName());
                boolean saved = authTableService.save(authTable);
                if (!saved) {
                    logger.warn("保存权限表 {} 失败，跳过", table.getName());
                    continue;
                }
            }

            for (T entity : entities) {
                if (!existenceChecker.test(authTable, entity)) {
                    entitySaver.accept(authTable, entity);
                }
            }
        }
    }

}
