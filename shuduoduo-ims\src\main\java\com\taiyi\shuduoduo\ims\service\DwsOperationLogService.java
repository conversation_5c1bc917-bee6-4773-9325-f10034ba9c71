package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.DwsOperationLogDao;
import com.taiyi.shuduoduo.ims.entity.DwsOperationLog;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DwsOperationLogService extends CommonMysqlService<DwsOperationLogDao, DwsOperationLog> {
    @Override
    public Class<DwsOperationLog> getEntityClass() {
        return DwsOperationLog.class;
    }

    public boolean saveLog(String dwsId, Integer type, String userId) {
        DwsOperationLog operationLog = new DwsOperationLog();
        operationLog.setDwsId(dwsId);
        operationLog.setType(type);
        operationLog.setActionUser(userId);
        return super.save(operationLog);
    }

    public List<DwsOperationLog> getLogByDwsId(String dwsId) {
        QueryWrapper<DwsOperationLog> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId).orderByDesc("create_time");
        return super.list(wrapper);
    }
}