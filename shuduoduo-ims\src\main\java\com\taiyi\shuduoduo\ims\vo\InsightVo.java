package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

public class InsightVo {

    @Data
    public static class DetailVo {
        /**
         * 名称
         */
        private String name;

        /**
         * 文件夹id
         */
        private String folderId;

        /**
         * 数据湖文件夹ID
         */
        private String dremioFolderId;

        /**
         * 流程ID
         */
        private String flowId;

        /**
         * 流程目录
         */
        private String namespace;

        /**
         * 描述
         */
        private String description;

        /**
         * 流程内容
         */
        private String content;

        /**
         * 其他额外参数
         */
        private String param;

        private String createdBy;

        private String plateId;
    }

    @Data
    public static class InsertRequest {
        private String companyId;

        private String id;

        /**
         * 名称
         */
        private String name;

        /**
         * 文件夹id
         */
        private String folderId;

        /**
         * 数据湖文件夹ID
         */
        private String dremioFolderId;

        /**
         * 流程ID
         */
        private String flowId;

        /**
         * 流程目录
         */
        private String namespace;

        /**
         * 描述
         */
        private String description;

        /**
         * 流程内容
         */
        private String content;

        /**
         * 其他额外参数
         */
        private String param;

        private String createdBy;

        /**
         * 数据湖文件夹路径
         */
        private List<String> path;
    }

    @Data
    public static class PageVo extends CommonMySqlPageVo {
        /**
         * 文件夹ID
         */
        private String folderId;

        /**
         * 板块ID
         */
        private String plateId;
    }

    @Data
    public static class PageResponseVo {
        private String companyId;

        private String id;

        private String name;

        private String folderId;

        private String flowId;

        private String namespace;

        private String description;

        private String content;

        private String param;

        private String createUserName;

        private String createdBy;

        private Date createTime;

        private Date updateTime;

        private String plateId;
    }

    @Data
    public static class InsertInsightExecuteLogVo {
        /**
         * 流程ID
         */
        @NotBlank
        private String flowId;

        /**
         * 执行ID
         */
        private String executionId;

        /**
         * 洞察ID
         */
        @NotBlank
        private String insightId;

        /**
         * 执行输出 json字符串
         */
        @NotBlank
        private String outputs;
    }

    /**
     * 新增/编辑参数
     */
    @Data
    public static class AddDimension {

        private String id;

        /**
         * 板块ID
         */
        @NotBlank
        private String plateId;

        /**
         * 数据域ID
         */
        private String dataFieldId;

        /**
         * 业务过程ID
         */
        private String busProcessId;

        /**
         * 数仓分层ID
         */
        private String plateLayerId;

        /**
         * 数据类型：1、维度 2、事实表
         */
        @NotNull
        private Integer dataType;

        /**
         * 表名称
         */
        @NotBlank
        private String name;

        /**
         * 表编码
         */
        @NotBlank
        private String code;

        /**
         * 数据集ID(数据湖ID)
         */
        private String dataSetId;

        /**
         * 智能洞察标记
         */
        private boolean  ifInsight = false;

        /**
         * 智能洞察ID
         */
        @NotBlank
        private String insightId;

        /**
         * 新增的表字段集合
         */
        private List<InsightVo.AddDimension.DimAttr> attrList;


        @Data
        public static class DimAttr {

            /**
             * 字段名称
             */
            private String name;
            /**
             * 字段编码
             */
            private String code;
            /**
             * 别名
             */
            private String aliasName;

            /**
             * 类型 1、新建 2、开通
             */
            private Integer type = 1;

            /**
             * 状态 1、未同步 2、已同步
             */
            private Integer status = 2;

            /**
             * 字段类型 json
             */
            private String columnType;

            /**
             * 数据格式 json
             */
            private String dataFormat;

            /**
             * 是否主键
             */
            private Boolean ifPrimaryKey;

            /**
             * 排序字段
             */
            private Long orderBy;

        }
    }

    @Data
    public static class SqlParam {

        @NotBlank
        private String sql;

        @NotBlank
        private String plateCode;
    }
}
