package com.taiyi.shuduoduo.nocodb.rpc;

import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.nocodb.api.dto.NcProjectDTO;
import com.taiyi.shuduoduo.nocodb.api.dto.SqliteMasterDTO;
import com.taiyi.shuduoduo.nocodb.service.NcProjectService;
import com.taiyi.shuduoduo.nocodb.service.SqliteMasterService;
import com.taiyi.shuduoduo.nocodb.vo.QueryVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Validated
@RestController
@RequestMapping("/rpc/nc/project")
public class NcProjectRpcController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private NcProjectService projectService;

    @Autowired
    private SqliteMasterService sqliteMasterService;

    @GetMapping("/{title}")
    public NcProjectDTO getNcProjectInfo(@PathVariable("title") String title) {
        return BeanUtil.copy(projectService.getNcProjectInfo(title), NcProjectDTO.class);
    }

    @GetMapping("/showTables/{prefix}")
    public List<SqliteMasterDTO> showCompanyTableWithPrefix(@PathVariable("prefix") String prefix) {
        return BeanUtil.copyList(sqliteMasterService.showCompanyTableWithPrefix(prefix), SqliteMasterDTO.class);
    }

    @GetMapping("/showColumns/{table}")
    public List<String> showColumns(@PathVariable("table") String table) {
        return sqliteMasterService.showColumns(table);
    }

    @PostMapping("/showDataMap")
    public List<Map<String,Object>> showDataMap(@RequestBody QueryVo.QueryParam queryParam){
        return sqliteMasterService.showDataMap(queryParam);
    }

    @PostMapping("/showData")
    public List<String> showData(@RequestBody QueryVo.QueryParam queryParam){
        return sqliteMasterService.showData(queryParam);
    }
}
