package com.taiyi.shuduoduo.ims.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.QueryTopic;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface QueryTopicDao extends CommonMysqlMapper<QueryTopic> {

    /**
     * 连表查询
     *
     * @param topicQueryWrapper 请求参数
     * @param page              分页参数
     * @return 分页
     */
    @Select("SELECT a.id,a.user_id,a.com_id,a.`subject`,a.raw_id,a.selected_metric,a.selected_query_param,a.create_time " +
            "from ims_query_topic a " +
            "LEFT JOIN ims_raw b on a.raw_id=b.id " +
            "${ew.customSqlSegment}")
    IPage<QueryTopic> getPageVo(@Param(Constants.WRAPPER) QueryWrapper<QueryTopic> topicQueryWrapper, Page<QueryTopic> page);

}