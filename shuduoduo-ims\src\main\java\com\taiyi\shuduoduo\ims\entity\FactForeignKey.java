package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_fact_foreign_key")
public class FactForeignKey extends CommonMySqlEntity {
    private String companyId;

    /**
     * 事实表与维度表关联关系ID
     */
    private String factDimId;

    /**
     * 事实表ID
     */
    private String factId;

    /**
     * 维度表ID
     */
    private String dimId;

    /**
     * 事实表外键ID
     */
    private String factAttrId;

    /**
     * 维度表主键ID
     */
    private String dimAttrId;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

}