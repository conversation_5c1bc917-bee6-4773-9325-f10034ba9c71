package com.taiyi.common.auth.rpc;

import com.taiyi.common.auth.service.TokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * TokenRpcController
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/rpc/token")
public class TokenRpcController {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private TokenService tokenService;

    /**
     * 验证Token
     *
     * @param oauthToken token
     * @return 验证结果
     */
    @GetMapping("/verify")
    public boolean verify(@RequestParam String oauthToken) {
        logger.info("verify token: {}", oauthToken);
        return tokenService.verify(oauthToken);
    }

}
