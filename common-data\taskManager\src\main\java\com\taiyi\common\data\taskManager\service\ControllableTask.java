package com.taiyi.common.data.taskManager.service;

import com.taiyi.common.data.taskManager.entity.TaskStatus;

/**
 * 任务控制
 *
 * <AUTHOR>
 */
public interface ControllableTask {

    /**
     * 获取任务名称
     *
     * @return 任务名称
     */
    String getTaskName();

    /**
     * 执行任务
     */
    void execute();

    /**
     * 是否允许执行
     *
     * @return 允许执行
     */
    boolean isEnabled();

    /**
     * 暂停任务
     */
    void stop();

    /**
     * 启动任务
     */
    void start();

    /**
     * 获取任务状态
     *
     * @return 任务状态
     */
    TaskStatus getStatus();
}

