package com.taiyi.common.connector.entity;


import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class DbVo implements Serializable {

    private String engineHost;

    /**
     * 地址
     */
    private String host;

    /**
     * 端口
     */
    private Integer port;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 数据库类型
     */
    private String type;

    /**
     * 数据库
     */
    private String database;

    /**
     * schema
     */
    private String schema;

    /**
     * sql 语句
     */
    private String sql;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 查询列
     */
    private List<String> columns;

    /**
     * 查询条件  eg:[{"":""},{"":""}]
     */
    private List<Map<String, Object>> queryParam;

    /**
     * 排序条件
     */
    private List<Map<String, Object>> sortParam;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 表头
     */
    private Map<String, Object> header;

}

