###########common dev config##########
#sleuth config
#spring.zipkin.base-url=http://*********:44119/
#spring.zipkin.locator.discovery.enabled=false
#spring.sleuth.sampler.probability=1.0


# nacos confg
spring.cloud.nacos.discovery.server-addr=*************:18848,*************:18849
spring.cloud.nacos.config.server-addr=*************:18848,*************:18849
spring.cloud.nacos.username=nacos
spring.cloud.nacos.password=shuduoduo2023!
#spring.cloud.sentinel.transport.dashboard=*********:44408
#spring.cloud.sentinel.transport.port=44409

# datasource config

spring.datasource.dynamic.primary=mysql
spring.datasource.dynamic.strict=false

spring.datasource.dynamic.hikari.max-pool-size=5
spring.datasource.dynamic.hikari.min-idle=5
spring.datasource.dynamic.hikari.connection-test-query=SELECT 1
spring.datasource.dynamic.hikari.max-lifetime=60000
spring.datasource.dynamic.hikari.idle-timeout=60000

spring.datasource.dynamic.datasource.mysql.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.mysql.username=root
spring.datasource.dynamic.datasource.mysql.password="!QW@3er4"
spring.datasource.dynamic.datasource.mysql.url=***************************************************************************************************************************************

spring.datasource.dynamic.datasource.sqlite.driver-class-name=org.sqlite.JDBC
spring.datasource.dynamic.datasource.sqlite.username=
spring.datasource.dynamic.datasource.sqlite.password=
spring.datasource.dynamic.datasource.sqlite.url=jdbc:sqlite:/home/<USER>/docker/nocodb/noco.db?date_string_format=yyyy-MM-dd HH:mm:ss

# redis
spring.redis.host=*************
spring.redis.jedis.pool.max-active=2000
spring.redis.jedis.pool.max-idle=500
spring.redis.jedis.pool.max-wait=1000ms
spring.redis.jedis.pool.min-idle=50
spring.redis.password=LsGuoChao2023!
spring.redis.port=16379
spring.redis.timeout=30000ms

# seata
#seata.tx-service-group=test_tx_service_group
#seata.config.type=nacos
#seata.config.nacos.serverAddr=*********:18848
#seata.config.nacos.group=SEATA_GROUP
#seata.config.nacos.username=nacos
#seata.config.nacos.password=nacos
#seata.registry.type=nacos
#seata.registry.nacos.server-addr=*********:18848
#seata.registry.nacos.group=SEATA_GROUP
#seata.registry.nacos.username=nacos
#seata.registry.nacos.password=nacos

# feign config
feign.hystrix.enabled=false

# logger config
logging.config=classpath:logback-spring.xml

# actuator config
management.server.port=-1
management.endpoints.web.base-path=/shuduoduov2_monitor
management.endpoints.web.exposure.include=*
management.endpoints.web.exposure.exclude=env,beans,nacos-discovery,heapdump,threaddump

# aliyun oss
#aliyun.oss.bucketName=shuduoduo-test