package com.taiyi.shuduoduo.ims.util;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数组转SQL条件
 *
 * <AUTHOR>
 */
public class ConvertToSqlUtil {

    public static String listToString(List<String> list) {
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) {
                buffer.append(",");
            }
            buffer.append("'").append(list.get(i)).append("'");
        }
        return buffer.toString();
    }

    public static String listToJavaString(List<String> list) {
        StringBuffer buffer = new StringBuffer();
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) {
                buffer.append(",");
            }
            buffer.append(list.get(i));
        }
        return buffer.toString();
    }

    public Map<String,Object> mapStringToMap(String stringMap){
        Map<String,Object> map = new HashMap<>();
        String[] strings = stringMap.split(",");
        for (String str : strings) {
            String[] s = str.split("=");
            map.put(s[0],s[1]);
        }
        return map;
    }

}
