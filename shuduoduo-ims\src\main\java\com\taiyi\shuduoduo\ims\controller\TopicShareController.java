package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.TopicShare;
import com.taiyi.shuduoduo.ims.service.TopicShareService;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import com.taiyi.shuduoduo.ims.vo.TopicShareVo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/topic/share")
@Validated
public class TopicShareController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private TopicShareService topicShareService;

    /**
     * @param t
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated TopicShare t) {
        boolean f;
        try {
            f = topicShareService.save(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 分享详情
     *
     * @param id id
     * @return T
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            TopicShareVo.ShareDetailResponse t = topicShareService.getShareDetailById(id);
            topicShareService.updateLastTime(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 分享用户列表
     *
     * @param topicId id
     * @return T
     */
    @GetMapping("/users/{topicId}")
    public ResponseEntity<ResponseVo.ResponseBean> getUserListById(@PathVariable("topicId") String topicId) {
        try {
            List<String> t = topicShareService.getUserListById(topicId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 分享给我查询
     *
     * @param pageVo 查询参数
     * @return 主题列表
     */
    @PostMapping("/page")
    @RequestException
    public ResponseEntity page(@RequestBody @Validated TopicShareVo.TopicSharePageVo pageVo) {
        //从token获取用户id
        String userId = CurrentUserUtil.get().getId();
        if (StringUtils.isEmpty(userId)) {
            return ResponseVo.response(MessageCode.TOKEN_EXPIRE, TipsConstant.USER_NOT_FOUND);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, topicShareService.pageList(pageVo, userId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 删除分享
     *
     * @param id 分享ID
     * @return T
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = topicShareService.logicDeleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 批量删除分享
     *
     * @param t 分享ID集合
     * @return T
     */
    @DeleteMapping("/deleteBatch")
    public ResponseEntity<ResponseVo.ResponseBean> deleteBatch(@RequestBody TopicShareVo.DeleteParam t) {
        boolean f;
        try {
            f = topicShareService.logicDeleteBatchByIds(t.getIds());
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

}
