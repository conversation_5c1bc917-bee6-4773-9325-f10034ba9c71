package com.taiyi.common.gateway.filter;

import com.taiyi.common.gateway.common.GatewayConstant;
import com.taiyi.common.gateway.rpc.PathRpcService;
import com.taiyi.common.gateway.rpc.TokenRpcService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * token 拦截过滤器
 *
 * <AUTHOR>
 */
@Configuration
public class GatewayTokenFilterConfig {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private PathRpcService pathRpcService;

    @Autowired
    private TokenRpcService tokenRpcService;

    private Set<String> allowPathSetCache;

    public static final String HEADER_TOKEN = "Lsy-Token";

    public static final String REQUEST_ID = "Request-Id";

    @Bean
    public GatewayTokenFilter gatewayTokenFilter() {
        return new GatewayTokenFilter();
    }

    public class GatewayTokenFilter implements Ordered, GlobalFilter {
        @Override
        public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
            String requestId = UUID.randomUUID().toString();
            String path = exchange.getRequest().getURI().getPath();
            String method = exchange.getRequest().getMethodValue();
            // 获取权限令牌
            String token = exchange.getRequest().getHeaders().getFirst(HEADER_TOKEN);
            logger.debug("http request: {},  method: {} , path : {}, header: {},query params: {}",
                    requestId, method, path, exchange.getRequest().getHeaders(),
                    exchange.getRequest().getQueryParams());

            // 1. 判断白名单免认证接口
            if (allowPath(path)) {
                logger.info("白名单免认证接口");
                ServerHttpRequest serverHttpRequest = exchange.getRequest().mutate()
                        .header(REQUEST_ID, requestId)
                        .header(HEADER_TOKEN, token)
                        .build();
                return chain.filter(exchange.mutate().request(serverHttpRequest).build());
            }

            // 验证token
            if (!StringUtils.isEmpty(token)) {
                try {
                    logger.info("验证token: {}", token);
                    return validateTokenAndProceed(exchange, chain, token, requestId);
                } catch (Exception e) {
                    logger.warn("Token validation error for request: {}, method: {} , path : {}, exception: {}",
                            requestId, method, path, ExceptionUtils.getRootCauseStackTrace(e));
                    return handleTokenError(exchange);
                }
            }
            // 没有token - 返回403，前端将自行处理重定向到CAS
            logger.info("没有token - 返回403，前端将自行处理重定向到CAS");
            return handleNoPermission(exchange);

        }

        @Override
        public int getOrder() {
            return 0;
        }

        /**
         * 配置免鉴权的url
         *
         * @param path url
         * @return boolean
         */
        private boolean allowPath(String path) {
            Set<String> allowPaths = new HashSet<>();
            if (allowPathSetCache == null || allowPathSetCache.isEmpty()) {
                try {
                    allowPaths.add("/cas/**");
                    allowPathSetCache = pathRpcService.authAllowPaths().getBody();
                    logger.debug("Gateway 获取允许访问的path配置成功，获取结果：{}", allowPathSetCache);
                } catch (Exception e) {
                    logger.warn("Gateway 获取允许访问的path配置发生异常，异常类型：{}，异常信息：{}", e, e.getMessage());
                }
            }
            if (allowPathSetCache != null) {
                allowPaths.addAll(allowPathSetCache);
            }
            AntPathMatcher matcher = new AntPathMatcher();

            for (String allowPath : allowPaths) {
                boolean flag = matcher.match(allowPath, path);
                if (flag) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 验证token
         *
         * @param exchange  请求
         * @param chain     过滤器链
         * @param token     token
         * @param requestId 请求id
         * @return 响应
         */
        private Mono<Void> validateTokenAndProceed(ServerWebExchange exchange, GatewayFilterChain chain,
                                                   String token, String requestId) {
            try {
                // 1. 校验token
                ResponseEntity<Boolean> response = tokenRpcService.verify(token);
                boolean verify = Boolean.TRUE.equals(response.getBody());
                if (!verify) {
                    logger.warn("Token verification failed for request: {}, method: {} , path : {}",
                            requestId, exchange.getRequest().getMethodValue(), exchange.getRequest().getURI().getPath());
                    return handleTokenError(exchange);
                }
                // 2. 验证成功 返回用户信息 转换 jwt令牌
                ServerHttpRequest serverHttpRequest = exchange.getRequest().mutate()
                        .header(HEADER_TOKEN, token)
                        .header(REQUEST_ID, requestId)
                        .build();
                return chain.filter(exchange.mutate().request(serverHttpRequest).build());
            } catch (Exception e) {
                logger.error("Token 校验失败: {}", e.getMessage());
                return handleTokenError(exchange);
            }
        }

        /**
         * 处理没有token
         *
         * @param exchange exchange
         * @return
         */
        private Mono<Void> handleNoPermission(ServerWebExchange exchange) {
            ServerHttpResponse response = exchange.getResponse();
            response.setStatusCode(HttpStatus.FORBIDDEN);
            DataBuffer buffer = response.bufferFactory()
                    .wrap(GatewayConstant.RESPONSE_NO_TOKEN_JSON.getBytes(StandardCharsets.UTF_8));
            response.getHeaders().add("Content-Type", "application/json");
            return response.writeAndFlushWith(Flux.just(Mono.just(buffer)));
        }

        /**
         * 处理 token 错误
         *
         * @param exchange exchange
         * @return Mono
         */
        private Mono<Void> handleTokenError(ServerWebExchange exchange) {
            ServerHttpResponse response = exchange.getResponse();
            response.setStatusCode(HttpStatus.FORBIDDEN);
            DataBuffer buffer = response.bufferFactory().wrap(GatewayConstant.RESPONSE_INVALID_TOKEN_JSON.getBytes(StandardCharsets.UTF_8));
            response.getHeaders().add("Content-Type", "application/json");
            return response.writeAndFlushWith(Flux.just(Mono.just(buffer)));
        }

    }

    @Bean
    @ConditionalOnMissingBean
    public HttpMessageConverters messageConverters(ObjectProvider<HttpMessageConverter<?>> converters) {
        return new HttpMessageConverters(converters.orderedStream().collect(Collectors.toList()));
    }

}
