package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.vo.HttpUtilVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/exec/http")
@Validated
public class HttpUtilController {
    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> executeHttp(@RequestBody @Validated HttpUtilVo t) {
        try {
            HttpRequest request = HttpUtil.createRequest(t.getMethod(), t.getUrl()).contentType(ContentType.JSON.getValue());
            HttpResponse response = request.body(t.getBody()).execute();
            if (response.isOk()) {
                logger.info("执行成功{}", response);
                return ResponseVo.response(MessageCode.SUCCESS, TipsConstant.EXECUTE_FLOW_SUCCEED, response);
            } else {
                logger.warn("执行失败{}", response);
                return ResponseVo.response(MessageCode.SUCCESS, TipsConstant.EXECUTE_FLOW_FAILED);
            }
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.SUCCESS, ExceptionUtil.stacktraceToString(e));
        }
    }


}
