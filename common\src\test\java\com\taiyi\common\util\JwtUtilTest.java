package com.taiyi.common.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class JwtUtilTest {

    @Test
    void jwt() {

        String id = "1";
        String name = "test";

        CurrentUserUtil.CurrentUser user = new CurrentUserUtil.CurrentUser();
        user.setOpenId(id);

        String token = JwtUtil.getToken(user);
        System.out.println(token);
        Assertions.assertTrue(JwtUtil.verifyToken(token));

        CurrentUserUtil.CurrentUser currentUser = JwtUtil.decodeToken(token);

        Assertions.assertTrue(user.getOpenId().equals(currentUser.getOpenId()));

    }
}