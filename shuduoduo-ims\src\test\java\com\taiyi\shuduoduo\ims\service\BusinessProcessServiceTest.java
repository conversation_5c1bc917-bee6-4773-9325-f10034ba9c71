package com.taiyi.shuduoduo.ims.service;

import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.shuduoduo.ims.vo.BusinessProcessVo;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class BusinessProcessServiceTest {

    @Autowired
    BusinessProcessService businessProcessService;

    @Test
    void myPage() {
        BusinessProcessVo.PageVo pageVo = new BusinessProcessVo.PageVo();
        pageVo.setPageNo(1);
        pageVo.setPageSize(20);
        pageVo.setColumn("dataFieldName");
        pageVo.setSortType("desc");
        PageResult<BusinessProcessVo.AddBusinessProcess> addBusinessProcessPageResult = businessProcessService.myPage(pageVo);
        addBusinessProcessPageResult.getList().forEach(System.out::println);
    }
}