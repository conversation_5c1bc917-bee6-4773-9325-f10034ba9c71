package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.entity.InsightFolder;
import com.taiyi.shuduoduo.ims.service.InsightFolderService;
import com.taiyi.shuduoduo.ims.service.InsightService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 智能洞察文件夹
 *
 * <AUTHOR>
 * @Description: 提供基础的 restful风格的WEB服务
 * 包含简单的单表的  增 删 改 查 以及分页查询
 */
@RestController
@RequestMapping("/insight/folder")
@Validated
public class InsightFolderController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private InsightFolderService insightFolderService;

    @Autowired
    private InsightService insightService;

    /**
     * 新增数据
     *
     * @param t
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated InsightFolder t) {
        boolean f;
        t.setCompanyId(CurrentUserUtil.get().getCompanyId());
        try {
            f = insightFolderService.save(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 根据板块查询根文件夹列表
     *
     * @param plateId 板块ID
     * @return List
     */
    @GetMapping("/plate/{plateId}")
    public ResponseEntity<ResponseVo.ResponseBean> getByPlateId(@PathVariable("plateId") String plateId) {
        try {
            List<InsightFolder> t = insightFolderService.getListByPlateId(plateId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 根据PID获取文件夹列表
     *
     * @param pId pid
     * @return List
     */
    @GetMapping("/{pId}")
    public ResponseEntity<ResponseVo.ResponseBean> getByPId(@PathVariable("pId") String pId) {
        try {
            List<InsightFolder> t = insightFolderService.getListByPId(pId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 搜索文件夹列表
     *
     * @param keyword keyword
     * @return List
     */
    @GetMapping("/search")
    public ResponseEntity<ResponseVo.ResponseBean> getByKeyword(@RequestParam("keyword") String keyword) {
        try {
            List<InsightFolder> t = insightFolderService.getListByKeyword(keyword);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 根据文件夹ID获取所有父级文件夹ID
     *
     * @param folderId 文件夹ID
     * @return 父级文件夹ID列表
     */
    @GetMapping("/parentIds/{folderId}")
    public ResponseEntity<ResponseVo.ResponseBean> getParentFolderIds(@PathVariable String folderId) {
        try {
            List<String> t = insightFolderService.getParentFolderIds(folderId);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }

    }


    /**
     * 删除文件夹
     *
     * @param id 文件夹ID
     * @return bool
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            // 检查该文件夹及其子文件夹下是否有洞察数据
            boolean hasInsights = insightService.hasInsightsInFolderOrSubFolders(id);
            if (hasInsights) {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, "文件夹或其子文件夹中有洞察数据，无法删除");
            }
            f = insightFolderService.logicDeleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 更新部分字段信息
     *
     * @param t
     * @return
     */
    @PatchMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> patch(@PathVariable String id, @RequestBody @Validated InsightFolder t) {
        boolean f;
        try {
            f = insightFolderService.updateById(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
