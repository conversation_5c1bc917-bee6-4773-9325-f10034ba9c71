package com.taiyi.shuduoduo.ims.api.service;

import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.ims.api.dto.AuthNocodbDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 数据权限RPC
 *
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoIms.SERVER_NAME)
public interface AuthNocodbRpcService {

    /**
     * 根据企业ID获取数据权限表信息
     *
     * @param companyId 企业ID
     * @return 数据权限表信息
     */
    @GetMapping(value = MicroServer.ShuduoduoIms.SERVER_PREFIX + "/rpc/auth/nocodb/getInfo")
    AuthNocodbDTO getAuthNocodbInfoByCompanyId(@RequestParam("companyId") String companyId);

    /**
     * 添加数据
     *
     * @return Boolean
     */
    @GetMapping(value = MicroServer.ShuduoduoIms.SERVER_PREFIX + "/rpc/auth/nocodb/insert/data")
    Boolean insertData(@RequestParam("projectName") String projectName, @RequestParam("tableName") String tableName, @RequestParam("body") String body);
}
