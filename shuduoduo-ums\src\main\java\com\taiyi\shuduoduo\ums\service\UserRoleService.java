package com.taiyi.shuduoduo.ums.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ums.dao.UserRoleDao;
import com.taiyi.shuduoduo.ums.entity.Company;
import com.taiyi.shuduoduo.ums.entity.User;
import com.taiyi.shuduoduo.ums.entity.UserRole;
import com.taiyi.shuduoduo.ums.vo.RoleVo;
import com.taiyi.shuduoduo.ums.vo.UserRoleVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户角色
 *
 * <AUTHOR>
 */
@Service
public class UserRoleService extends CommonMysqlService<UserRoleDao, UserRole> {

    @Override
    public Class<UserRole> getEntityClass() {
        return UserRole.class;
    }

    @Autowired
    private UserRoleDao userRoleDao;

    @Autowired
    private UserCompanyService userCompanyService;

    @Autowired
    private UserService userService;

    @Autowired
    private CompanyService companyService;

    /**
     * 根据用户id分页查询角色列表
     *
     * @param userid 用户id
     * @param pageVo 分页参数
     * @return ResponseEntity
     */
    public PageResult<UserRoleVo.PageResponse> selectRolePage(String userid, RoleVo.PageParam pageVo) {
        Page<UserRole> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        QueryWrapper<UserRoleVo.PageResponse> wrapper = new QueryWrapper<>();
        wrapper.eq("ur.if_deleted", false).eq("uur.user_id", userid).orderByDesc("uur.create_time");
        IPage<UserRoleVo.PageResponse> rolePage = userRoleDao.selectRolePage(page, wrapper);
        PageResult<UserRoleVo.PageResponse> pageResult = new PageResult<>();
        pageResult.setPageNo(page.getCurrent());
        pageResult.setPageSize(page.getSize());
        pageResult.setTotal(rolePage.getTotal());
        pageResult.setList(rolePage.getRecords());
        return pageResult;
    }


    /**
     * 根据角色id分页查询用户列表
     *
     * @param roleId 角色id
     * @param pageVo 分页参数
     * @return ResponseEntity
     */
    public PageResult<UserRoleVo.PageResponse> selectUserPage(String roleId, RoleVo.PageParam pageVo) {
        Company company = companyService.getById(CurrentUserUtil.get().getCompanyId());
        if (null == company) {
            return null;
        }
        Page<UserRole> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        QueryWrapper<UserRoleVo.PageResponse> wrapper = new QueryWrapper<>();
        wrapper.eq("uu.if_lock", false).eq("uur.role_id", roleId);
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.and(queryWrapper -> queryWrapper.like("uu.real_name", pageVo.getKeyWord())
                    .or()
                    .like("uu.third_user_id", pageVo.getKeyWord()));
        }
        wrapper.orderByDesc("uur.create_time");
        IPage<UserRoleVo.PageResponse> rolePage = userRoleDao.selectUserPage(page, wrapper);
        for (UserRoleVo.PageResponse response : rolePage.getRecords()) {
            //查询用户部门
            response.setDept(userCompanyService.getDeptName(CurrentUserUtil.get().getCompanyId(), response.getUserId()));
        }
        PageResult<UserRoleVo.PageResponse> pageResult = new PageResult<>();
        pageResult.setPageNo(page.getCurrent());
        pageResult.setPageSize(page.getSize());
        pageResult.setTotal(rolePage.getTotal());
        pageResult.setList(rolePage.getRecords());
        return pageResult;
    }

    /**
     * 批量删除
     *
     * @param ids ids
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatchByIds(List<String> ids) {
        boolean f = false;
        for (String id : ids) {
            f = super.deleteById(id);
        }
        return f;
    }


    /**
     * 批量添加用户
     *
     * @param t t
     * @return Map
     */
    public Map<String, Object> saveUserBatch(UserRoleVo.InsertUserBatchRequest t) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        Map<String, Object> res = new HashMap<>(3);
        int repeat = 0;
        int succeed = 0;
        List<String> failed = new ArrayList<>();
        for (String key : t.getUserIds()) {
            User user = userService.getUserInfo(companyId, key);
            //用户不存在
            if (user == null) {
                failed.add(key);
                continue;
            }

            UserRole userRole = new UserRole();
            userRole.setCompanyId(companyId);
            userRole.setUserId(user.getId());
            userRole.setRoleId(t.getRoleId());
            // 是否存在
            if (exists(userRole)) {
                repeat++;
                continue;
            }
            //添加
            if (save(userRole)) {
                succeed++;
            }
        }
        res.put("repeat", repeat);
        res.put("succeed", succeed);
        res.put("failed", failed);
        return res;
    }

    public List<UserRole> getRoleListByUserId(String userId) {
        QueryWrapper<UserRole> wrapper = new QueryWrapper<>();
        wrapper.eq("if_lock", false).eq("user_id", userId);
        return super.list(wrapper);
    }

    public List<UserRole> getUserListByRoleId(String roleId, String companyId) {
        QueryWrapper<UserRole> wrapper = new QueryWrapper<>();
        wrapper.eq("if_lock", false).eq("role_id", roleId).eq("company_id", companyId);
        return super.list(wrapper);
    }
}