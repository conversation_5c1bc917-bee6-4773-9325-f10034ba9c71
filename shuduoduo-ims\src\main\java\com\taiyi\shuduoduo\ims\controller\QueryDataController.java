package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.taiyi.common.aspect.ApiLogPointCut;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.aspect.TopicApiLogPointCut;
import com.taiyi.shuduoduo.ims.entity.*;
import com.taiyi.shuduoduo.ims.exceptions.QueryFailedException;
import com.taiyi.shuduoduo.ims.service.*;
import com.taiyi.shuduoduo.ims.util.SqlBuilderUtil;
import com.taiyi.shuduoduo.ims.vo.QueryDataVo;
import com.taiyi.shuduoduo.ims.vo.SqlBuilderVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import com.taiyi.shuduoduo.ums.api.service.CompanyDremioRpcService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据查询
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/query/data")
@Validated
@RefreshScope
public class QueryDataController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Value(value = "${file.path}")
    private String filePath;

    @Autowired
    private RawService rawService;

    @Autowired
    private CompanyDremioRpcService companyDremioRpcService;

    @Autowired
    private PlateService plateService;

    @Autowired
    private QueryTopicService queryTopicService;

    @Autowired
    private RawTableService rawTableService;

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private DimAttributeService dimAttributeService;


    /**
     * 数据-导出
     *
     * @return ResponseEntity
     */
    @ApiLogPointCut(name = "数据导出", description = "数据导出")
    @PostMapping("/export")
    public ResponseEntity exportData(@RequestBody @Validated QueryDataVo.QueryRequest param) {
        CurrentUserUtil.CurrentUser user = CurrentUserUtil.get();
        Raw raw = rawService.getById(param.getRawId());
        if (null == raw && param.getType() == 1) {
            logger.error("导出异常：{}", TipsConstant.RAW_DETAIL_NOT_EXIST);
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        Dimension dimension = dimensionService.getById(param.getRawId());
        if (null == dimension && param.getType() == 2) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DIMENSION_DETAIL_NOT_EXIST);
        }
        // 查询板块信息
        Plate plate = plateService.getById(param.getType() == 1 ? raw.getPlateId() : dimension.getPlateId());
        if (null == plate) {
            logger.error("导出异常：{}", TipsConstant.RAW_BELONGS_PLATE_NOT_FOUND);
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_BELONGS_PLATE_NOT_FOUND);
        }
        // 获取数据源信息
        CompanyDremioDTO companyDremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == companyDremioDTO) {
            logger.error("导出异常：{}", TipsConstant.DATA_SOURCE_NOT_FOUND);
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        if (param.getPageNo() == null || param.getPageSize() == null) {
            param.setPageNo(1);
            param.setPageSize(50000);
        }
        int total = param.getPageSize();
        if (param.getPageSize() > 1000000) {
            total = 1000000;
        }
        String fileName = new String(param.getFileName().getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8) + ".xlsx";
        String filePath_ = this.filePath + fileName;
        File file = new File(filePath_);
        try {
            // 计算分页次数
            param.setPageSize(param.getPageSize() > 50000 ? 50000 : param.getPageSize());
            int pageCount = total / param.getPageSize() + (total % param.getPageSize() == 0 ? 0 : 1);
            // 创建一个excel文件
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
            SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(xssfWorkbook, 100, true);
            SXSSFSheet sheet = sxssfWorkbook.createSheet(param.getFileName());
            sheet.trackAllColumnsForAutoSizing();
            for (int i = 1; i <= pageCount; i++) {
                param.setPageNo(i);
                // 获取数据
                List<Map<String, Object>> exportData = param.getType() == 1 ? rawService.getExportData(user, param, raw, plate, companyDremioDTO)
                        : dimensionService.getExportData(param, dimension, plate, companyDremioDTO);
                //构建Excel
                rawService.createExcel(sheet, exportData, param.getIfShow(), param.getHeaderContext());
            }
            //返回导出文件
            //response为HttpServletResponse对象
            //response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            //response.setHeader("Content-Disposition", "attachment;filename=" + new String((StringUtils.isBlank(param.getFileName()) ? "未命名查询" : param.getFileName()).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1) + ".xlsx");
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();
            try (FileOutputStream out = new FileOutputStream(file)) {
                sxssfWorkbook.write(out);
            } finally {
                sxssfWorkbook.close();
            }
            return ResponseVo.response(MessageCode.SUCCESS, null, "/download/" + fileName);
        } catch (Exception e) {
            logger.error("导出异常：所选参数异常:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, "所选参数异常");
        }
    }

    /**
     * 下载文件
     *
     * @param fileName 文件名
     * @return
     * @throws IOException
     */
    @GetMapping("/export/download/{fileName:.+}")
    public void downloadFile(@PathVariable String fileName, HttpServletResponse response) throws IOException {
        File file = new File(this.filePath + fileName);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
        response.setHeader("Content-Disposition", "attachment;filename=" + new String((fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf(".")) : fileName).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1) + ".xlsx");
        try (
                InputStream inputStream = Files.newInputStream(file.toPath());
                ServletOutputStream out = response.getOutputStream()
        ) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            logger.error("下载文件异常：{}", ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 数据-导出
     */
    @GetMapping("/export/{id}")
    public void exportData(@PathVariable("id") String id, @RequestParam("fileName") String fileName, @RequestParam("ifShow") String ifShow,
                           @RequestParam("headerContext") String headerContext, HttpServletResponse response) throws IOException {
        //获取导出数据
        List<Map<String, Object>> exportData = rawService.getExportData(id);
        // 创建一个excel文件
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
        try (
                SXSSFWorkbook sxssfWorkbook = new SXSSFWorkbook(xssfWorkbook, 100, true);
                ServletOutputStream out = response.getOutputStream()
        ) {
            SXSSFSheet sheet = sxssfWorkbook.createSheet(fileName);
            sheet.trackAllColumnsForAutoSizing();
            //构建Excel
            rawService.createExcel(sheet, exportData, Boolean.valueOf(ifShow), headerContext);
            //返回导出文件
            //response为HttpServletResponse对象
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((StringUtils.isBlank(fileName) ? "未命名查询" : fileName).getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1) + ".xlsx");
            sxssfWorkbook.write(out);
        } catch (IOException e) {
            logger.error("导出Excel异常：{}", ExceptionUtil.stacktraceToString(e));
        }
    }


    /**
     * 台账数据查询
     *
     * @param param 查询条件
     * @return ResponseEntity
     */
    @ApiLogPointCut(name = "筛选,排序,查询台账", description = "筛选,排序,查询台账")
    @TopicApiLogPointCut()
    @PostMapping("/optional/detail")
    public ResponseEntity<ResponseVo.ResponseBean> optionalDwsListDetail(@RequestBody @Validated QueryDataVo.QueryRequest param) {
        if (!param.getFilters().isEmpty()) {
            for (SqlBuilderVo.Filter filter : param.getFilters()) {
                if (SqlBuilderUtil.sqlValidate(filter.getValue().toString())) {
                    return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.FILTER_PARAM_NOT_ALLOWED);
                }
            }
        }
        Raw raw = rawService.getById(param.getRawId());
        if (null == raw) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        // 查询板块信息
        Plate plate = plateService.getById(raw.getPlateId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_BELONGS_PLATE_NOT_FOUND);
        }
        // 获取数据源信息
        CompanyDremioDTO companyDremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == companyDremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        if (param.getPageNo() == null || param.getPageSize() == null) {
            param.setPageNo(1);
            param.setPageSize(50);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, rawService.queryData(param, raw, plate, companyDremioDTO));
        } catch (QueryFailedException e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e.getMessage());
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.TOPIC_NOT_FOUND);
        }
    }

    /**
     * 快捷查询 -- API
     *
     * @param id    自定义查询ID
     * @param param 分页参数
     * @return T
     */
    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> queryData(@PathVariable("id") String id, @RequestBody QueryDataVo.QueryApiRequest param) {
        QueryTopic topic = queryTopicService.getById(id);
        if (null == topic) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.TOPIC_NOT_FOUND);
        }
        // 获取数据源信息
        CompanyDremioDTO companyDremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == companyDremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        Raw raw = rawService.getById(topic.getRawId());
        if (null == raw) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        // 查询板块信息
        Plate plate = plateService.getById(raw.getPlateId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_BELONGS_PLATE_NOT_FOUND);
        }
        QueryDataVo.QueryRequest request = new QueryDataVo.QueryRequest();
        request.setRawId(topic.getRawId());
        if (param.getPageNo() == null || param.getPageSize() == null) {
            request.setPageNo(1);
            request.setPageSize(50);
        } else {
            request.setPageNo(param.getPageNo());
            request.setPageSize(param.getPageSize());
        }
        if (param.getPageSize() > 200) {
            request.setPageSize(50);
        }
        JSONArray json = topic.getSelectedAttr();
        List<String> selectAttrs = new ArrayList<>();
        if (!json.isEmpty()) {
            List<Map> maps = json.toJavaList(Map.class);
            for (Map map : maps) {
                selectAttrs.add(map.get("id").toString());
            }
        }
        request.setTableAttrIds(selectAttrs);
        JSONArray jsonFilter = topic.getSelectedFilter();
        List<SqlBuilderVo.Filter> filters = jsonFilter.toJavaList(SqlBuilderVo.Filter.class);
        if (!param.getFilters().isEmpty()) {
            for (SqlBuilderVo.Filter filter : param.getFilters()) {
                if (SqlBuilderUtil.sqlValidate(filter.getValue().toString())) {
                    return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.FILTER_PARAM_NOT_ALLOWED);
                }
                if (!filter.getField().contains(StrUtil.DOT)) {
                    return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.QUERY_FILTER_COLUMN_NOT_FOUND + "------->:" + filter.getField());
                }
                String[] field = filter.getField().split("\\.");
                // 校验所选列是否在字段列表中
                if (field.length < 2 || !rawTableService.checkAttrExist(raw.getId(), filter.getField().split("\\.")[1])) {
                    return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.QUERY_FILTER_COLUMN_NOT_FOUND + "------->:" + filter.getField());
                }
                if (!filters.contains(filter)) {
                    filters.add(filter);
                }
            }
        }
        request.setFilters(filters);
        JSONArray jsonOrder = topic.getSelectedOrder();
        request.setOrders(jsonOrder.toJavaList(SqlBuilderVo.Order.class));

        try {
            return ResponseVo.response(MessageCode.SUCCESS, rawService.queryData(request, raw, plate, companyDremioDTO));
        } catch (QueryFailedException e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e.getMessage());
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.TOPIC_NOT_FOUND);
        }
    }

    /**
     * 查询IN列表
     *
     * @param param 查询参数
     * @return T
     */
    @PostMapping("/inList")
    public ResponseEntity getInList(@RequestBody @Validated QueryDataVo.InQueryRequest param) {
        if (StringUtils.isNoneBlank(param.getKeyWord())) {
            if (SqlBuilderUtil.sqlValidate(param.getKeyWord()) || param.getKeyWord().contains("'")) {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.FILTER_PARAM_NOT_ALLOWED);
            }
        }
        Raw raw = rawService.getById(param.getRawId());
        if (null == raw) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        // 查询板块信息
        Plate plate = plateService.getById(raw.getPlateId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_BELONGS_PLATE_NOT_FOUND);
        }
        // 获取数据源信息
        CompanyDremioDTO companyDremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == companyDremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        if (param.getPageNo() == null || param.getPageSize() == null) {
            param.setPageNo(1);
            param.setPageSize(50);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, rawService.queryInData(param, plate, companyDremioDTO));
        } catch (QueryFailedException e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e.getMessage());
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.TOPIC_NOT_FOUND);
        }
    }

    /**
     * 查询首行数据
     *
     * @param sourceType  类型
     * @param sourceId    id
     * @param filterArray 筛选条件
     * @return T
     */
    @GetMapping("/firstData")
    public ResponseEntity firstData(@RequestParam Integer sourceType, @RequestParam String sourceId, @RequestParam(required = false) List<SqlBuilderVo.Filter> filterArray) {
        String rawId;
        List<String> selectAttrs = new ArrayList<>();
        List<SqlBuilderVo.Filter> filters = new ArrayList<>();
        List<SqlBuilderVo.Order> orders = new ArrayList<>();
        if (sourceType == 1) {
            QueryTopic queryTopic = queryTopicService.getById(sourceId);
            if (null == queryTopic) {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.TOPIC_NOT_FOUND);
            }
            rawId = queryTopic.getRawId();
            JSONArray json = queryTopic.getSelectedAttr();
            if (!json.isEmpty()) {
                List<Map> maps = json.toJavaList(Map.class);
                for (Map map : maps) {
                    selectAttrs.add(map.get("id").toString());
                }
            }
            filters = queryTopic.getSelectedFilter().toJavaList(SqlBuilderVo.Filter.class);
            orders = queryTopic.getSelectedOrder().toJavaList(SqlBuilderVo.Order.class);
        } else if (sourceType == 2) {
            rawId = sourceId;
            selectAttrs = rawTableService.showAttrByRawId(rawId);
        } else {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        if (null != filterArray && !filterArray.isEmpty()) {
            for (SqlBuilderVo.Filter filter : filterArray) {
                // 校验所选列是否在字段列表中
                if (!filters.contains(filter)) {
                    filters.add(filter);
                }
            }
        }
        Raw raw = rawService.getById(rawId);
        if (null == raw) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        // 获取数据源信息
        CompanyDremioDTO companyDremioDTO = companyDremioRpcService.getCompanyById(raw.getCompanyId());
        if (null == companyDremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        // 查询板块信息
        Plate plate = plateService.getById(raw.getPlateId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_BELONGS_PLATE_NOT_FOUND);
        }
        QueryDataVo.QueryRequest request = new QueryDataVo.QueryRequest();
        request.setRawId(rawId);
        request.setTableAttrIds(selectAttrs);
        request.setFilters(filters);
        request.setOrders(orders);
        try {
            return ResponseVo.response(MessageCode.SUCCESS, rawService.queryFirstData(CurrentUserUtil.get(), request, raw, plate, companyDremioDTO));
        } catch (QueryFailedException e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e.getMessage());
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.TOPIC_NOT_FOUND);
        }
    }

    /**
     * 聚合数据
     *
     * @param method 聚合函数
     * @param param  参数
     * @return T
     */
    @PostMapping("/calc/{method}")
    public ResponseEntity calcMetric(@PathVariable String method, @RequestBody @Validated QueryDataVo.QueryRequest param) {
        param.setCalcFunc(method);
        if (!param.getFilters().isEmpty()) {
            for (SqlBuilderVo.Filter filter : param.getFilters()) {
                if (SqlBuilderUtil.sqlValidate(filter.getValue().toString())) {
                    return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.FILTER_PARAM_NOT_ALLOWED);
                }
            }
        }
        Raw raw = rawService.getById(param.getRawId());
        if (null == raw) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_DETAIL_NOT_EXIST);
        }
        // 查询板块信息
        Plate plate = plateService.getById(raw.getPlateId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_BELONGS_PLATE_NOT_FOUND);
        }
        // 获取数据源信息
        CompanyDremioDTO companyDremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == companyDremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        if (param.getPageNo() == null || param.getPageSize() == null) {
            param.setPageNo(1);
            param.setPageSize(50);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, null, rawService.calcMetric(param, raw, plate, companyDremioDTO));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.TOPIC_NOT_FOUND);
        }
    }

    /**
     * 维度事实表数据查询
     *
     * @param param 查询条件
     * @return ResponseEntity
     */
    @ApiLogPointCut(name = "筛选,排序,维度事实表查询", description = "筛选,排序,维度事实表查询")
    @PostMapping("/dimension/detail")
    public ResponseEntity dimensionData(@RequestBody QueryDataVo.QueryRequest param) {
        if (!param.getFilters().isEmpty()) {
            for (SqlBuilderVo.Filter filter : param.getFilters()) {
                if (SqlBuilderUtil.sqlValidate(filter.getValue().toString())) {
                    return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.FILTER_PARAM_NOT_ALLOWED);
                }
            }
        }
        Dimension dimension = dimensionService.getById(param.getRawId());
        if (null == dimension) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DIMENSION_DETAIL_NOT_EXIST);
        }
        // 查询板块信息
        Plate plate = plateService.getById(dimension.getPlateId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_BELONGS_PLATE_NOT_FOUND);
        }
        // 获取数据源信息
        CompanyDremioDTO companyDremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == companyDremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        if (param.getPageNo() == null || param.getPageSize() == null) {
            param.setPageNo(1);
            param.setPageSize(50);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.queryData(param, dimension, plate, companyDremioDTO));
        } catch (QueryFailedException e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e.getMessage());
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.TOPIC_NOT_FOUND);
        }
    }

    /**
     * 查询IN列表
     *
     * @param param 查询参数
     * @return T
     */
    @PostMapping("/dimension/inList")
    public ResponseEntity getDimensionInData(@RequestBody @Validated QueryDataVo.InQueryRequest param) {
        if (StringUtils.isNoneBlank(param.getKeyWord())) {
            if (SqlBuilderUtil.sqlValidate(param.getKeyWord()) || param.getKeyWord().contains("'")) {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.FILTER_PARAM_NOT_ALLOWED);
            }
        }
        Dimension dimension = dimensionService.getById(param.getRawId());
        if (null == dimension) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DIMENSION_DETAIL_NOT_EXIST);
        }
        // 查询板块信息
        Plate plate = plateService.getById(dimension.getPlateId());
        if (null == plate) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.RAW_BELONGS_PLATE_NOT_FOUND);
        }
        // 获取数据源信息
        CompanyDremioDTO companyDremioDTO = companyDremioRpcService.getCompanyById(CurrentUserUtil.get().getCompanyId());
        if (null == companyDremioDTO) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.DATA_SOURCE_NOT_FOUND);
        }
        if (param.getPageNo() == null || param.getPageSize() == null) {
            param.setPageNo(1);
            param.setPageSize(50);
        }
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dimensionService.queryInData(param, plate, companyDremioDTO));
        } catch (QueryFailedException e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e.getMessage());
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.TOPIC_NOT_FOUND);
        }
    }
}
