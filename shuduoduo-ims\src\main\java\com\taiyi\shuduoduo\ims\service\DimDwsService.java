package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.DimDwsDao;
import com.taiyi.shuduoduo.ims.entity.DimAttribute;
import com.taiyi.shuduoduo.ims.entity.DimDws;
import com.taiyi.shuduoduo.ims.vo.DwsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class DimDwsService extends CommonMysqlService<DimDwsDao, DimDws> {
    @Override
    public Class<DimDws> getEntityClass() {
        return DimDws.class;
    }

    @Autowired
    private DimAttributeService dimAttributeService;

    /**
     * 根据指标ID 删除指标维度关联关系
     *
     * @param dwsId 指标ID
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deletedByDwsId(String companyId, String dwsId) {
        boolean f = false;
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId);
        List<DimDws> dimDwsList = super.list(wrapper);
        if (dimDwsList.isEmpty()) {
            return true;
        }
        for (DimDws dimDws : dimDwsList) {
            f = super.deleteById(dimDws.getId());
        }
        if (f) {
            // 更新维度表指标字段
            f = dimAttributeService.deletedByDwsId(companyId, dwsId);
        }
        return f;
    }

    /**
     * 根据指标表批量更新
     *
     * @param dwsId     指标ID
     * @param unionList 维度表ID与维度表字段ID
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchByDwsIdWithUnionList(String dwsId, List<DwsVo.DimensionUnion> unionList) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        // 删除关联关系字段
        if (!this.deletedByDwsId(companyId, dwsId)) {
            logger.error("删除指标维度关联关系失败");
            return false;
        }
        List<DimDws> dimDwsList = new ArrayList<>();
        for (DwsVo.DimensionUnion union : unionList) {
            DimDws dimDws = BeanUtil.copy(union, DimDws.class);
            dimDws.setDwsId(dwsId);
            dimDws.setOrderBy(getMaxOrder(dwsId));
            dimDwsList.add(dimDws);
        }
        if (!this.saveBatch(dimDwsList)){
            logger.error("批量保存指标维度关联关系失败");
            return false;
        }
       // 更新维度表指标字段
        if (!dimAttributeService.updateByDwsIdAndUnionAttrIds(companyId, dwsId, unionList)){
            logger.error("更新维度表指标字段失败");
            return false;
        }
        return true;
    }

    /**
     * 根据维度表批量更新
     *
     * @param unionList 指标ID与维度表字段ID
     * @return bool
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchByDimIdWithUnionList(List<DwsVo.DimensionUnion> unionList) {
        if (unionList == null || unionList.isEmpty()) {
            return true;
        }

        List<DimDws> dimDwsList = unionList.stream()
                .map(union -> BeanUtil.copy(union, DimDws.class))
                .collect(Collectors.toList());

        return saveBatch(dimDwsList);
    }

    public Long getMaxOrder(String dwsId) {
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId).orderByDesc("order_by").last("LIMIT 1");
        DimDws dws = super.getOne(wrapper);
        return null == dws ? 1L : dws.getOrderBy() + 1;
    }

    /**
     * 根据指标ID获取关联列表
     *
     * @param dwsId 指标ID
     * @return list
     */
    public List<DimDws> getListByDwsId(String dwsId) {
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId).orderByAsc("order_by");
        return list(wrapper);
    }

    /**
     * 根据指标ID集合获取关联列表
     *
     * @param dwsIds 指标ID
     * @return list
     */
    public List<DimDws> getListByDwsIds(List<String> dwsIds) {
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.in("dws_id", dwsIds).orderByAsc("order_by");
        return list(wrapper);
    }

    /**
     * 根据指标ID获取关联数量
     *
     * @param dwsId 指标ID
     * @return count
     */
    public Integer getCountByDwsId(String dwsId) {
        Integer count = 0;
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId);
        List<DimDws> list = super.list(wrapper);
        for (DimDws dimDws : list) {
            DimAttribute attribute = dimAttributeService.getById(dimDws.getDimAttrId());
            if (null != attribute && !attribute.getIfDeleted()) {
                count++;
            }
        }
        return count;
    }

    /**
     * 根据维度ID和字段ID获取关联信息（只获取第一条）
     *
     * @param dimId     维度ID
     * @param dimAttrId 字段ID
     * @return t
     */
    public DimDws getByDimIdAndDimAttrId(String dimId, String dimAttrId) {
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.eq("dim_id", dimId).eq("dim_attr_id", dimAttrId).orderByAsc("order_by").last("LIMIT 1");
        return getOne(wrapper);
    }

    /**
     * 删除指标维度关联信息
     *
     * @param dimId     维度表ID
     * @param dimAttrId 维度字段ID
     * @param dwsId     指标ID
     * @return bool
     */
    public boolean deletedByDimIdAndAttrIdAndDwsId(String dimId, String dimAttrId, String dwsId) {
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.eq("dim_id", dimId)
                .eq("dim_attr_id", dimAttrId)
                .eq("dws_id", dwsId).last("LIMIT 1");
        DimDws dimDws = super.getOne(wrapper);
        if (null == dimDws) {
            return true;
        }
        return super.deleteById(dimDws.getId());
    }

    public List<DimDws> getListByDimId(String dimId) {
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.eq("dim_id", dimId).orderByAsc("order_by");
        return list(wrapper);
    }

    /**
     * 根据统计维度ID 获取指标关联列表
     *
     * @param dimId 统计维度ID
     * @return 指标关联列表
     */
    public List<DimDws> getListByDimensionId(String dimId) {
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.eq("dimension", dimId).orderByAsc("order_by");
        return list(wrapper);
    }

    public DimDws getOneByDwsIdAndPeriodId(String dwsId, String periodId, String dimId) {
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId)
                .eq("dimension", dimId)
                .eq("period_value", periodId)
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }

    public List<DimDws> getListByDwsIdAndDimId(String dwsId, String dimId) {
        QueryWrapper<DimDws> wrapper = new QueryWrapper<>();
        wrapper.eq("dws_id", dwsId)
                .eq("dimension", dimId)
                .orderByAsc("order_by");
        return list(wrapper);
    }
}