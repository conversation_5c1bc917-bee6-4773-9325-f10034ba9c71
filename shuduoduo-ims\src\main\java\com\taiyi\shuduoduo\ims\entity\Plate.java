package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * 业务板块实体
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_plate")
public class Plate extends CommonMySqlEntity {
    /**
     * 企业id
     */
    private String companyId;

    /**
     * 板块描述
     */
    private String description;

    /**
     * 板块编码
     */
    private String plateCode;

    private String name;

    private Integer ifDeleted;

    /**
     * 类型 1、新建 2、开通
     */
    private Integer type;

    /**
     * 状态 1、未同步 2、已同步
     */
    private Integer status;

    /**
     * dremio 空间ID
     */
    private String spaceId;

}