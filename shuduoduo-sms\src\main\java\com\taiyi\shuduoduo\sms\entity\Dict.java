package com.taiyi.shuduoduo.sms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "sms_dict")
public class Dict extends CommonMySqlEntity {


    /**
     * 删除标记 0:未删除 1:已删除
     */
    private Boolean ifDeleted;


    /**
     * 字典名称
     */
    private String dictName;


    private String dictEnName;

    /**
     * 字典值
     */
    private Integer dictValue;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 排序
     */
    private Long orderBy;

}