package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.DimUserDao;
import com.taiyi.shuduoduo.ims.entity.DimUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class DimUserService extends CommonMysqlService<DimUserDao, DimUser> {
    @Override
    public Class<DimUser> getEntityClass() {
        return DimUser.class;
    }

    @Autowired
    private DimensionService dimensionService;

    /**
     * 获取维度列表
     *
     * @return T
     */
    public DimUser getDimList(String topicId) {
        String companyId = CurrentUserUtil.get().getCompanyId();
        return getDimListByUserId(companyId, topicId);
    }

    public DimUser getDimListByUserId(String companyId, String topicId) {
        QueryWrapper<DimUser> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false)
                .eq("company_id", companyId)
                .eq("topic_id", topicId)
                .last("LIMIT 1");
        return super.getOne(wrapper);
    }
}