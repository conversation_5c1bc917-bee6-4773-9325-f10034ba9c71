package com.taiyi.shuduoduo.ims.service;

import com.taiyi.shuduoduo.ims.vo.DimensionVo;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DimensionServiceTest {
    @Autowired
    private DimensionService dimensionService;

    @Test
    void syncData() {
        Assertions.assertTrue(dimensionService.syncDimension("2a924e5e9f3d4bee94b5300aa5074810", ""));
    }


    @Order(0)
    @Test
    void selectSearchDimensionList() {
        DimensionVo.SearchParam param = new DimensionVo.SearchParam();
        param.setKeyWord("测试");
        param.setDataType(2);
        Assertions.assertNotNull(dimensionService.getSearchDimensionList(param, "7172070a1c264c2bb6bb201e17bf8ee7"));
    }

}