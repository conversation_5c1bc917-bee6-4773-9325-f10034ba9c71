package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * 维度权限关联表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_dimension_auth_foreign")
public class DimensionAuthForeign extends CommonMySqlEntity {
    /**
     * 维度权限表ID
     */
    private String dimAuthId;

    /**
     * 关联账户字段
     */
    private Integer authAgent;

    /**
     * 权限表账户字段
     */
    private String authColumn;

    /**
     * 关联字段
     */
    private String rawColumn;

    /**
     * 权限表关联字段
     */
    private String tableColumn;

    private Boolean ifDeleted;

}