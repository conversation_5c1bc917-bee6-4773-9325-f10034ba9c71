package com.taiyi.common.entity;

/**
 * <AUTHOR>
 */

public enum MessageCode {
    //返回正常
    SUCCESS("A000", "返回正常"),

    //用户请求参数错误（1.请求必填参数为空；2.参数格式不匹配；3.无效的用户输入）
    PARAMETER_ERROR("B100", "用户请求参数错误"),

    //用户访问权限异常（1.访问未授权；2.黑名单用户；3.用户签名异常；4.账号被冻结；5.黑名单IP地址）
    NO_AUTHORITY("B200", "用户访问权限异常"),

    //Token 访问过期   1.授权已过期；
    TOKEN_EXPIRE("B201", "Token过期"),

    //用户请求异常（1.用户重复请求；2.请求次数超过限制）
    REQUEST_ERROR("B300", "用户请求异常"),

    //用户资源异常（用户资源不足）
    RESOURCE_ERROR("B400", "用户资源异常"),

    //用户上传文件异常（1.文件格式非法；2.文件过大）
    FILE_ERROR("B500", "用户上传文件异常"),

    //用户注册异常（1.用户名已存在；2.用户名校验失败；3.密码强度不够；4.手机格式校验失败）
    USER_REGISTER_ERROR("B600", "用户注册异常"),

    //用户登录异常（1.用户账户不存在；2.用户账户被冻结；3.用户密码错误）
    USER_LOGIN_ERROR("B700", "用户登录异常"),

    //验证码输入错误（1.验证码输入错误；）
    MESSAGE_CODE_ERROR("B800", "验证码输入错误"),

    //系统服务异常（1.系统服务异常；2.系统服务超时；3.服务器资源异常）
    SYSTEM_SERVICE_ERROR("C100", "系统服务异常"),

    // 第三方服务失败（1.数据库服务出错；2.中间件服务出错；3.其他第三方服务出错）
    THIRD_PARTY_SERVICE_ERROR("C200", "第三方服务失败"),

    // 通知服务出错（1.短信服务失败；2.邮件服务失败；3.其他通知失败）
    NOTIFY_SERVICE_ERROR("C300", "通知服务出错");

    private String code;
    private String message;

    MessageCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
