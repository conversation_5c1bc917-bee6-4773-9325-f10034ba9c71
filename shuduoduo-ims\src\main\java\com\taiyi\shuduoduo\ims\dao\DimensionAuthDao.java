package com.taiyi.shuduoduo.ims.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.DimensionAuth;
import com.taiyi.shuduoduo.ims.vo.DimAuthVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface DimensionAuthDao extends CommonMysqlMapper<DimensionAuth> {

    /**
     * 分页获取维度权限表
     *
     * @param page    分页
     * @param wrapper 筛选
     * @return page
     */
    @Select("select a.id,a.auth_table_id,a.status,a.operator,b.auth_table as authTableName from ims_dimension_auth as a left join ims_auth_table as b " +
            "on a.auth_table_id = b.id ${ew.customSqlSegment}")
    IPage<DimAuthVo.PageResult> getPage(Page<DimAuthVo.PageResult> page, @Param("ew") QueryWrapper<DimAuthVo.PageResult> wrapper);
}