package com.taiyi.shuduoduo.ums.api.service;

import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDremioDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoUms.SERVER_NAME)
public interface CompanyDremioRpcService {

    /**
     * 获取公司dremio信息通过公司ID
     *
     * @param comId 公司id
     * @return 公司
     */
    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/dremio/{comId}")
    CompanyDremioDTO getCompanyById(@PathVariable("comId") String comId);
}
