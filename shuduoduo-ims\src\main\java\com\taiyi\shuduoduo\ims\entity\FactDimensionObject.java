package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_fact_dimension_object")
public class FactDimensionObject extends CommonMySqlEntity {
    private String companyId;

    /**
     * 事实表ID集合
     */
    private String factIds;

    /**
     * 维度表ID集合
     */
    private String dimIds;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

}