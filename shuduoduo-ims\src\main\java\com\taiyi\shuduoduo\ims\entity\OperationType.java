package com.taiyi.shuduoduo.ims.entity;

public enum OperationType {
    NEW(1, "新建"),
    EDIT(2, "编辑"),
    PUBLISHED(3, "发布"),
    OFFLINE(4, "下线");

    private int value;
    private String description;

    OperationType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static String getDesc(Integer type) {
        for (OperationType operationType : values()) {
            if (operationType.getValue() == type) {
                return operationType.description;
            }
        }
        return "";
    }
}
