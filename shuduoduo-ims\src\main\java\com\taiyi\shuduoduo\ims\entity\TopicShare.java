package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_topic_share")
public class TopicShare extends CommonMySqlEntity {
    /**
     * 分享人
     */
    private String fromUser;

    /**
     * 被分享人
     */
    private String toUser;

    /**
     * 分享内容
     */
    private String topicId;

    /**
     * 是否已读
     */
    private Boolean ifRead;

    /**
     * 最后一次使用时间
     */
    private Date lastTime;

    private Boolean ifDeleted;

}