package com.taiyi.shuduoduo.ums.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 多次接口尝试进行账户封锁
 *
 * @author: Settimo
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface MethodResponseException {
    /**
     * 失敗次数
     */
    int exceptionCount() default 5;

    /**
     * 限制时间 (秒)
     */
    long redisExpired() default 60 * 5;

}