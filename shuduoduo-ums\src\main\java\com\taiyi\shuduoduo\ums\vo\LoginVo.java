package com.taiyi.shuduoduo.ums.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LoginVo {
    @Data
    public static class AdminVo {
        @NotBlank
        public String name;
        @NotBlank
        public String pwd;
        @NotBlank
        public String companyId;
    }

    @Data
    public static class UserVo {
        private String userId;
        private String companyId;
        private String realName;
        private String nickName;
        private String token;
        private Long expiresIn;
        private String avatarThumb;
        private String avatarMiddle;
        private String avatarBig;
        /**
         * 用户头像
         */
        private String avatarUrl;
        private Boolean ifAdmin;
        private String thirdUserId;
        private List<String> permissions;
    }

    @Data
    public static class AdminUserVo {
        private String userId;
        private String companyId;
        private String realName;
        private Boolean ifAdmin;
        private String thirdUserId;
        private String token;
        private Long expiresIn;
        private List<String> permissions;
    }
}
