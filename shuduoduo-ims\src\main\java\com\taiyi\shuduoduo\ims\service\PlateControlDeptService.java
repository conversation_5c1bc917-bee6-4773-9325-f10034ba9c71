package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.PlateControlDeptDao;
import com.taiyi.shuduoduo.ims.entity.PlateControlDept;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PlateControlDeptService extends CommonMysqlService<PlateControlDeptDao, PlateControlDept> {
    @Override
    public Class<PlateControlDept> getEntityClass() {
        return PlateControlDept.class;
    }

    /**
     * 获取板块下管理部门列表
     *
     * @param plateId 板块ID
     * @return List
     */
    public List<PlateControlDept> getList(String plateId) {
        QueryWrapper<PlateControlDept> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId()).eq("if_deleted", false).eq(StrUtil.isNotBlank(plateId), "plate_id", plateId).orderByAsc("order_by");
        return super.list(wrapper);
    }

    @Override
    public boolean isNameDuplicate(PlateControlDept t, String name) {
        QueryWrapper<PlateControlDept> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("if_deleted", 0);
        if (t.id != null) {
            queryWrapper.ne("id", t.id);
        }
        queryWrapper.eq("dept_name", name);
        queryWrapper.last("LIMIT 1");
        PlateControlDept one = super.getOne(queryWrapper);
        return one != null;
    }

    public PlateControlDept getByPlateIdAndName(String plateId, String name) {
        QueryWrapper<PlateControlDept> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("if_deleted", 0).eq("plate_id", plateId)
                .eq("dept_name", name)
                .last("LIMIT 1");
        return super.getOne(queryWrapper);
    }

    public List<PlateControlDept> getListByIds(List<String> deptIds, String companyId) {
        // 检查输入参数
        if (deptIds == null || deptIds.isEmpty()) {
            logger.warn("deptIds is null or empty");
            return super.list(new QueryWrapper<PlateControlDept>().eq("company_id", companyId).eq("if_deleted", false));
        }
        // 构建查询条件
        QueryWrapper<PlateControlDept> wrapper = new QueryWrapper<>();
        wrapper.in("id", deptIds).eq("company_id", companyId).eq("if_deleted", false);
        return super.list(wrapper);
    }
}