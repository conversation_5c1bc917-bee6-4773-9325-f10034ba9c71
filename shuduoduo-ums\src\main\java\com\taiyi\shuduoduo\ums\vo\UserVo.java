package com.taiyi.shuduoduo.ums.vo;

import com.taiyi.common.data.mysql.entity.BaseList;
import com.taiyi.shuduoduo.ums.entity.User;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class UserVo {


    @Data
    public static class QueryIdsVo {
        private List<String> ids;
    }

    @Data
    public static class ListResponse {
        private String id;
        private Boolean ifAdmin;
        private String name;
        private String thirdUserId;
        private String realName;
    }


    //添加用户实体类
    @Data
    public static class AddUserVo {
        //关联表user_role需要的内容
        private Boolean role_ifLock;
        private String role_roleId;
        private String role_userId;
        //关联表user_company需要的内容
        private String companyId;
        private String depId;
        private String userId;
        //user实体原有内容
        private Date birthday;
        private Boolean gender;
        private String idCard;
        private Boolean ifAdmin;
        private Boolean ifBackend;
        private Boolean ifLock;
        private String mobile;
        private String nickname;
        private String pwdDigest;
        private String realName;
        private String thirdUserId;
    }

    /**
     * AddUserVo的重写，以后可能会吧AddUserVo覆盖
     */
    @Data
    public static class AddUser {

    }

    @Data
    public static class lockstatus {
        private Boolean ifLock;
    }

    @Data
    public static class changepwd {
        private String pwdDigest;
    }

    @Data
    public static class GetUserInfo {
        /**
         * 用户ID
         */
        private String openId;

        private String thirdUserId;

        /**
         * 大图
         */
        private String avatarBig;

        /**
         * 中图
         */
        private String avatarMiddle;

        /**
         * 小图
         */
        private String avatarThumb;

        /**
         * 用户头像
         */
        private String avatarUrl;

        /**
         * 授权token
         */
        private String accessToken;

        /**
         * 刷新token
         */
        private String refreshToken;

        /**
         * token 类型
         */
        private String tokenType;//Bearer

        /**
         * token过期时间
         */
        private Long expiresIn;//3600

        /**
         * 刷新token 过期时间
         */
        private Long refreshExpiresIn;//2592000

        /**
         * 用户名
         */
        private String username;

        /**
         * 企业key
         */
        private String tenantKey;

        /**
         * 公司唯一标识
         */
        private String companyId;

        /**
         * 用户的角色列表
         */
        private List<String> roles;

        private String token;
    }

    @Data
    public static class UserList extends BaseList<User> {

//        public MyQuery getPage() {
//            if (user == null) {
//                user = new AddUser();
//            }
//            return super.getPage("name", BeanUtil.copy(user, User.class));
//        }
    }

}
