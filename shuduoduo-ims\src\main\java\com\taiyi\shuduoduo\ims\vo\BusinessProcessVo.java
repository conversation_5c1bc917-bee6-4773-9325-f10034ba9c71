package com.taiyi.shuduoduo.ims.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 业务过程
 *
 * <AUTHOR>
 */
public class BusinessProcessVo {

    @Data
    public static class DetailResponse {
        private String id;
        private String name;
        private String code;
        private String description;
        private String dataFieldId;
        private boolean ifDeleted;

        /**
         * 类型 1、新建 2、开通
         */
        private Integer type;

        private String plateId;
    }

    @Data
    public static class AddBusinessProcess {
        private String id;
        @NotBlank
        private String dataFieldId;

        private String dataFieldName;
        @NotBlank
        private String name;
        @NotBlank
        private String code;
        private String description;
        private String plateName;

        private Long orderBy;

        private Integer type;
    }

    /**
     * 分页查询筛选类
     */
    @Data
    public static class PageVo extends CommonMySqlPageVo {

        /**
         * 数据域ID
         */
        private String dataFieldId;
    }
}
