package com.taiyi.shuduoduo.ims.dao;

import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.RawTableAttr;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public interface RawTableAttrDao extends CommonMysqlMapper<RawTableAttr> {

    @Select("SELECT\n" +
            "\tb.* \n" +
            "FROM\n" +
            "\tims_raw_table_group_attr AS a\n" +
            "\tLEFT JOIN ims_raw_table_attr AS b ON a.raw_table_attr_id = b.id \n" +
            "\tAND b.if_deleted = FALSE AND b.if_show = TRUE \n" +
            "\tAND b.raw_table_id = #{rawTableId} \n" +
            "WHERE\n" +
            "\ta.if_deleted = FALSE \n" +
            "\tAND b.id is not NULL\n" +
            "\tAND a.raw_table_group_id = #{tableGroupId}")
    List<RawTableAttr> listByRawTableIdAndGroupId(@Param("rawTableId") String rawTableId, @Param("tableGroupId") String tableGroupId);

    @Select("SELECT\n" +
            "\t* \n" +
            "FROM\n" +
            "\tims_raw_table_attr \n" +
            "WHERE\n" +
            "\tif_deleted = FALSE\n" +
            "\tAND if_show = TRUE \n" +
            "\tAND raw_table_id =#{rawTableId} \n" +
            "\tAND id NOT IN (\n" +
            "\tSELECT\n" +
            "\t\tattr.raw_table_attr_id \n" +
            "\tFROM\n" +
            "\t\tims_raw_table_group_attr AS attr \n" +
            "\tWHERE\n" +
            "\t\tattr.raw_table_group_id IN ( SELECT table_group.id FROM ims_raw_table_group AS table_group WHERE table_group.raw_table_id = #{rawTableId} ) \n" +
            "\t)\n" +
            "\tORDER BY order_by")
    List<RawTableAttr> showOptionListByRawTableId(@Param("rawTableId") String rawTableId);

}