package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.taiyi.common.data.mysql.entity.MyQuery;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.entity.RawForeignKey;
import com.taiyi.shuduoduo.ims.service.RawForeignKeyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 台账字段关联
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/raw/foreign/key")
@Validated
public class RawForeignKeyController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private RawForeignKeyService rawForeignKeyService;

    /**
     *
     * @param t
     * @return
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated RawForeignKey t) {
        boolean f;
        try {
            f = rawForeignKeyService.save(t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> getById(@PathVariable("id") String id) {
        try {
            RawForeignKey t = rawForeignKeyService.getById(id);
            return ResponseVo.response(MessageCode.SUCCESS, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }


    @PutMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateById(@PathVariable("id") String id, @RequestBody @Validated RawForeignKey t) {
        boolean f;
        try {
            f = rawForeignKeyService.updateWithNull(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        boolean f;
        try {
            f = rawForeignKeyService.deleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @PostMapping("/page")
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody(required = false) MyQuery query) {
        try {
            PageResult page = rawForeignKeyService.page(query);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


    @PostMapping("/_exists")
    public ResponseEntity<ResponseVo.ResponseBean> exists(@RequestBody @Validated RawForeignKey t) {
        boolean exists;
        try {
            exists = rawForeignKeyService.exists(t);
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (exists) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @PostMapping("{id}/_exists")
    public ResponseEntity<ResponseVo.ResponseBean> exists(@PathVariable String id, @RequestBody @Validated RawForeignKey t) {
        try {
            rawForeignKeyService.exists(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        return ResponseVo.response(MessageCode.SUCCESS);
    }

    /**
     * ���²����ֶ���Ϣ
     *
     * @param t
     * @return
     */
    @PatchMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> patch(@PathVariable String id, @RequestBody @Validated RawForeignKey t) {
        boolean f;
        try {
            f = rawForeignKeyService.updateById(id, t);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
