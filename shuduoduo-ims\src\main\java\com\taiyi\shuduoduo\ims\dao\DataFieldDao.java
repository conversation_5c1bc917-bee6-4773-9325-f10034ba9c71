package com.taiyi.shuduoduo.ims.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.DataField;
import com.taiyi.shuduoduo.ims.vo.DataFieldVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface DataFieldDao extends CommonMysqlMapper<DataField> {


    /**
     * 分页查询
     *
     * @param page    分页参数
     * @param wrapper 筛选条件
     * @return page
     */
    @Select("SELECT\n" +
            "\td.id,\n" +
            "\td.`name`,\n" +
            "\td.`code`,\n" +
            "\td.description,\n" +
            "\td.plate_id,\n" +
            "\td.type,\n" +
            "\td.order_by,\n" +
            "\td.create_time,\n" +
            "\tp.`name` AS plateName \n" +
            "FROM `ims_data_field` AS d\n" +
            "\tLEFT JOIN ims_plate AS p ON p.id = d.plate_id AND p.if_deleted = FALSE \n" +
            "${ew.customSqlSegment}")
    IPage<DataFieldVo.AddDataFieldList> selectPage(Page<DataField> page, @Param("ew") Wrapper<DataField> wrapper);

}