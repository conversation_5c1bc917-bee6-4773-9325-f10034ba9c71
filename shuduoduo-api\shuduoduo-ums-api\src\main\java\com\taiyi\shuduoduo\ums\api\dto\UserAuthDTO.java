package com.taiyi.shuduoduo.ums.api.dto;

import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class UserAuthDTO {

    private String id;

    private String mobile;

    private String pwdDigest;

    private String nickname;

    /**
     * 后台账号 ， 适用于 后台管理系统
     */
    private Boolean ifBackend;

    private Boolean ifLock;

    /**
     * 用户的所有角色
     * 内部判断权限使用
     */
    private Set<String> roles;

}
