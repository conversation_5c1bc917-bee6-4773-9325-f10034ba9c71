package com.taiyi.shuduoduo.ims.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.LabelDao;
import com.taiyi.shuduoduo.ims.entity.Dws;
import com.taiyi.shuduoduo.ims.entity.Label;
import com.taiyi.shuduoduo.ims.entity.LabelUnion;
import com.taiyi.shuduoduo.ims.vo.LabelVo;
import com.taiyi.shuduoduo.ums.api.dto.UserDTO;
import com.taiyi.shuduoduo.ums.api.service.UserRpcService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class LabelService extends CommonMysqlService<LabelDao, Label> {
    @Override
    public Class<Label> getEntityClass() {
        return Label.class;
    }

    @Autowired
    private LabelUnionService labelUnionService;

    @Autowired
    private DwsService dwsService;

    @Autowired
    private UserRpcService userRpcService;

    /**
     * 获取最大排序
     *
     * @return long
     */
    public Long getMaxOrder() {
        QueryWrapper<Label> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).orderByDesc("order_by").last("LIMIT 1");
        Label t = super.getOne(wrapper);
        return null == t ? 1L : t.getOrderBy() + 1;
    }

    public LabelVo getDetailById(String id) {
        Label label = super.getById(id);
        if (null != label) {
            LabelVo labelVo = BeanUtil.copy(label, LabelVo.class);
            if (StringUtils.isNotBlank(label.getCreatedBy())) {
                UserDTO userDTO = userRpcService.getUserInfoById(label.getCreatedBy());
                labelVo.setCreateUserName(null == userDTO ? "" : userDTO.getRealName());
            }
            if (StringUtils.isNotBlank(label.getUpdatedBy())) {
                UserDTO userDTO1 = userRpcService.getUserInfoById(label.getUpdatedBy());
                labelVo.setUpdateUserName(null == userDTO1 ? "" : userDTO1.getRealName());
            }
            return labelVo;
        }
        return null;
    }

    /**
     * 标签删除
     *
     * @param id id
     * @return bool
     */
    @Override
    public boolean logicDeleteById(String id) {
        boolean f = labelUnionService.deleteByUnionId(id, 1);
        if (f) {
            return super.logicDeleteById(id);
        }
        return false;
    }

    /**
     * 分页查询
     *
     * @param pageVo pageVo
     * @return PageResult
     */
    public PageResult getPage(LabelVo.LabelPageVo pageVo) {
        Page<Label> page = new Page<>(pageVo.getPageNo(), pageVo.getPageSize());
        page.setOptimizeCountSql(false);
        QueryWrapper<Label> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId());
        wrapper.eq("if_deleted", false);
        wrapper.eq("type", pageVo.getType());
        if (StringUtils.isNotBlank(pageVo.getKeyWord())) {
            wrapper.and(queryWrapper -> queryWrapper.like("name", pageVo.getKeyWord())
                    .or()
                    .like("description", pageVo.getKeyWord()));
        }
        if (StringUtils.isNotBlank(pageVo.getColumn()) && StringUtils.isNotBlank(pageVo.getSortType())) {
            wrapper.last("order by " + StrUtil.toSymbolCase(pageVo.getColumn(), '_') + " " + pageVo.getSortType());
        } else {
            wrapper.orderByAsc("order_by");
        }
        IPage<Label> iPage = super.page(page, wrapper);
        List<LabelVo.PageResponseVo> res = BeanUtil.copyList(page.getRecords(), LabelVo.PageResponseVo.class);
        for (LabelVo.PageResponseVo vo : res) {
            if (StringUtils.isNotBlank(vo.getCreatedBy())) {
                UserDTO userDTO = userRpcService.getUserInfoById(vo.getCreatedBy());
                vo.setCreateUserName(null == userDTO ? "" : userDTO.getRealName());
            }
            if (StringUtils.isNotBlank(vo.getUpdatedBy())) {
                UserDTO userDTO1 = userRpcService.getUserInfoById(vo.getUpdatedBy());
                vo.setUpdateUserName(null == userDTO1 ? "" : userDTO1.getRealName());
            }
        }
        PageResult result = new PageResult<>();
        result.setPageNo(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setList(res);
        result.setTotal(iPage.getTotal());
        return result;
    }

    /**
     * 获取标签列表
     *
     * @param query query
     * @return List
     */
    public List<LabelVo.ListResponse> getList(LabelVo.LabelListVo query) {
        QueryWrapper<Label> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", CurrentUserUtil.get().getCompanyId())
                .eq("if_deleted", false)
                .eq(null != query.getType(), "type", query.getType())
                .like(StringUtils.isNotBlank(query.getKeyWord()), "name", query.getKeyWord())
                .orderByDesc("order_by");
        return BeanUtil.copyList(super.list(wrapper), LabelVo.ListResponse.class);
    }

    /**
     * 获取指标列表根据标签ID
     *
     * @param id 标签ID
     * @return 指标列表
     */
    public List<LabelVo.DwsDetail> getDwsListByLabelId(String id) {
        List<LabelVo.DwsDetail> res = new ArrayList<>();
        Label label = super.getById(id);
        if (null != label) {
            List<LabelUnion> unions = labelUnionService.getListByUnionId(id, 1);
            for (LabelUnion union : unions) {
                LabelVo.DwsDetail detail = new LabelVo.DwsDetail();
                Dws dws = dwsService.getById(union.getDwsId());
                if (null != dws) {
                    detail.setName(dws.getName());
                    detail.setDwsId(dws.getId());
                    detail.setLabelId(id);
                    detail.setType(label.getType());
                    detail.setUnionId(union.getId());
                    detail.setCreateTime(union.getCreateTime());
                    if (StringUtils.isNotBlank(union.getCreatedBy())) {
                        UserDTO userDTO = userRpcService.getUserInfoById(union.getCreatedBy());
                        detail.setUserName(null == userDTO ? "" : userDTO.getRealName());
                    }
                    res.add(detail);
                }
            }
        }
        return res;
    }
}