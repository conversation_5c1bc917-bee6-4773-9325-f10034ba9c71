package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.taiyi.common.aspect.ApiLogPointCut;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.DataField;
import com.taiyi.shuduoduo.ims.service.*;
import com.taiyi.shuduoduo.ims.vo.DataFieldVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 数据域控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/data/field")
@Validated
public class DataFieldController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private DataFieldService dataFieldService;

    @Autowired
    private BusinessProcessService businessProcessService;

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private RawService rawService;

    @Autowired
    private DwsService dwsService;

    /**
     * 新增数据
     *
     * @param addDataField 新增的vo
     * @return 返回请求
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated DataFieldVo.AddDataField addDataField) {
        DataField dataField = BeanUtil.copy(addDataField, DataField.class);
        //名称去重
        if (dataFieldService.isNameExists(dataField)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.NAME_DUPLICATE);
        }
        // 编码去重
        if (dataFieldService.isCodeExists(dataField)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.CODE_DUPLICATE);
        }
        boolean f;
        try {
            f = dataFieldService.addDataField(dataField);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 数据域-修改
     *
     * @param addDataField
     * @return ResponseEntity
     */
    @PutMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateById(@PathVariable("id") String id, @RequestBody @Validated DataFieldVo.AddDataField addDataField) {
        DataField dataField = BeanUtil.copy(addDataField, DataField.class);
        dataField.setId(id);
        boolean f;
        try {
            f = dataFieldService.addDataField(dataField);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 删除数据域
     *
     * @param id 数据域ID
     * @return bool
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        // 校验数据域下是否有业务过程
        if (ObjectUtil.isNotEmpty(businessProcessService.getListByDataFieldId(id))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_BUSINESS_PROCESS_LIST_IN_THIS_DATA_FIELD);
        }
        // 校验板块下是否有维度
        if (ObjectUtil.isNotEmpty(dimensionService.getDimListByUnionId(id, 1, null))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_DIMENSION_LIST_IN_THIS_PLATE);
        }
        // 校验板块下是否有台账
        if (ObjectUtil.isNotEmpty(rawService.getListByUnionIdAndType(id, 1, null))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_RAW_LIST_IN_THIS_PLATE);
        }
        // 校验板块下是否有指标
        if (ObjectUtil.isNotEmpty(dwsService.getList(id, 1, null))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_METRIC_LIST_IN_THIS_PLATE);
        }
        boolean f;
        try {
            f = dataFieldService.logicDeleteById(id);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 分页查询
     *
     * @param pageVo 分页参数
     * @return T
     */
    @ApiLogPointCut(name = "分页查询数据域列表", description = "数据域点击")
    @PostMapping("/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody @Validated DataFieldVo.DataFieldPageVo pageVo) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dataFieldService.myPage(pageVo));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 联动查询业务过程-数据域
     */
    @GetMapping("/queryDataField")
    public ResponseEntity<ResponseVo.ResponseBean> queryDataField(@RequestParam String plateId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dataFieldService.getListByPlatedId(plateId, CurrentUserUtil.get().getCompanyId()));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    @GetMapping("/dws/queryDataField")
    public ResponseEntity<ResponseVo.ResponseBean> queryDataFieldByDws(@RequestParam String plateId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dataFieldService.getListAndDwsCountByPlatedId(plateId));
        } catch (Exception e) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 详情
     *
     * @param id id
     * @return T
     */
    @GetMapping("{id}")
    public ResponseEntity<ResponseVo.ResponseBean> detail(@PathVariable("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, dataFieldService.getById(id));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 上移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/up/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> up(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (dataFieldService.sequence(id, orderBy, DataField.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 下移
     *
     * @param id      id
     * @param orderBy orderBy
     * @return T
     */
    @PatchMapping("/down/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> down(@PathVariable("id") String id, @RequestParam("orderBy") Integer orderBy) {
        try {
            if (dataFieldService.sequence(id, orderBy, DataField.class)) {
                return ResponseVo.response(MessageCode.SUCCESS);
            } else {
                return ResponseVo.response(MessageCode.REQUEST_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }
}
