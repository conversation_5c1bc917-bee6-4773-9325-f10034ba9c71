package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_topic_api")
public class TopicApi extends CommonMySqlEntity {
    private String companyId;

    /**
     * 自定义报表ID
     */
    private String topicId;

    /**
     * API名称
     */
    private String name;

    /**
     * 目录ID
     */
    private String categoryId;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建人
     */
    private String createBy;

    private Boolean ifDeleted;

}