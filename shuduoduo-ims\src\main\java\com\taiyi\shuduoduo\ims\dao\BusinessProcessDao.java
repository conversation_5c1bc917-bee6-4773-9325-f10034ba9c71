package com.taiyi.shuduoduo.ims.dao;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.dao.CommonMysqlMapper;
import com.taiyi.shuduoduo.ims.entity.BusinessProcess;
import com.taiyi.shuduoduo.ims.vo.BusinessProcessVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public interface BusinessProcessDao extends CommonMysqlMapper<BusinessProcess> {

    /**
     * 分页查询业务过程
     *
     * @param page    分页类
     * @param wrapper 筛选条件
     * @return page
     */
    @Select("SELECT\n" +
            "\tbus.id,\n" +
            "\tbus.`name`,\n" +
            "\tbus.`code`,\n" +
            "\tbus.data_field_id,\n" +
            "\tbus.type,\n" +
            "\tbus.order_by,\n" +
            "\td.`name` AS dataFieldName,\n" +
            "\tbus.description,bus.create_time \n" +
            "FROM\n" +
            "\tims_business_process AS bus\n" +
            "\tLEFT JOIN ims_data_field AS d ON d.id = bus.data_field_id AND d.if_deleted = false \n" +
            "${ew.customSqlSegment}")
    IPage<BusinessProcessVo.AddBusinessProcess> selectBusPage(Page<BusinessProcess> page, @Param("ew") Wrapper<BusinessProcess> wrapper);
}