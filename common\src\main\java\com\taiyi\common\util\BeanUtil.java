package com.taiyi.common.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class BeanUtil {

    static final Logger logger = LoggerFactory.getLogger(BeanUtil.class);

    static final List<Class> basicTypeList = CollectionUtil.newArrayList(
            String.class,
            Integer.class,
            Long.class,
            Double.class,
            Float.class,
            BigDecimal.class,
            Boolean.class,
            Character.class,
            Date.class,
            JSONArray.class,
            JSON.class
    );

    /**
     * 实现PoJo 类之间的传值
     * <p>
     * 当且仅当 属性名称 与 属性的数据类型一致 时候进行复制对应的值
     *
     * @param from
     * @param toClass
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> T copy(F from, Class<T> toClass) {


        if (from == null || toClass == null) {
            return null;
        }

        if (basicTypeList.contains(from.getClass())) {
            if (from.getClass().equals(toClass)) {
                return (T) from;
            }
        }

        Field[] fromFields = ReflectUtil.getFields(from.getClass());
        Field[] toFields = ReflectUtil.getFields(toClass);
        try {
            T to = toClass.newInstance();
            for (Field toField : toFields) {
                for (Field fromField : fromFields) {
                    if (!toField.getName().equals(fromField.getName())) {
                        continue;
                    }
                    if (!toField.getType().equals(fromField.getType())) {
                        continue;
                    }
                    if (!basicTypeList.contains(toField.getType())) {
                        continue;
                    }
                    toField.setAccessible(true);
                    fromField.setAccessible(true);
                    try {
                        toField.set(to, fromField.get(from));
                        break;
                    } catch (Exception e) {
                        logger.warn(ExceptionUtil.stacktraceToString(e));
                    }
                }
            }
            return to;
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
        }
        return null;
    }

    /**
     * 实现PoJo 类之间的传值
     * <p>
     * 当且仅当 属性名称 与 属性的数据类型一致 时候进行复制对应的值
     *
     * @param from
     * @param toClass
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> F copy(F from, F toClass) {


        if (from == null || toClass == null) {
            return null;
        }

        Field[] fromFields = ReflectUtil.getFields(from.getClass());
        Field[] toFields = ReflectUtil.getFields(toClass.getClass());
        try {

            for (Field toField : toFields) {
                for (Field fromField : fromFields) {
                    if (!toField.getName().equals(fromField.getName())) {
                        continue;
                    }
                    if (!toField.getType().equals(fromField.getType())) {
                        continue;
                    }
                    if (!basicTypeList.contains(toField.getType())) {
                        continue;
                    }
                    toField.setAccessible(true);
                    fromField.setAccessible(true);
                    try {
                        toField.set(toClass, fromField.get(from));
                        break;
                    } catch (Exception e) {
                        logger.warn(ExceptionUtil.stacktraceToString(e));
                    }
                }
            }
            return toClass;
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
        }
        return null;
    }

    /**
     * 批量copy转换复制
     *
     * @param fromList
     * @param toClass
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> List<T> copyList(List<F> fromList, Class<T> toClass) {
        if (fromList == null || toClass == null) {
            return null;
        }
        List<T> result = new ArrayList<>();
        for (F from : fromList) {
            result.add(copy(from, toClass));
        }
        return result;
    }
}
