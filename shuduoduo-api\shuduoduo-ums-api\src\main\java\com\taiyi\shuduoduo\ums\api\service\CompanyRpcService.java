package com.taiyi.shuduoduo.ums.api.service;

import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.ums.api.dto.CompanyDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 公司层RPC
 *
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoUms.SERVER_NAME)
public interface CompanyRpcService {

    /**
     * 获取公司通过id
     *
     * @param id 公司id
     * @return 公司
     */
    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/company/{id}")
    CompanyDTO getCompanyById(@PathVariable String id);

    /**
     * 获取所有公司
     *
     * @return 公司列表
     */
    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/company/list")
    List<CompanyDTO> getCompanyList();

    /**
     * 根据公司ID和用户ID获取用户所在部门字段串
     *
     * @param companyId 公司ID
     * @param userId    用户ID
     * @return 用户所在部门字段串
     */
    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/company/depts")
    List<String> getUserInDeptStr(@RequestParam("companyId") String companyId, @RequestParam("userId") String userId);

    /**
     * 根据公司ID和部门ID获取用户列表
     *
     * @param companyId 公司ID
     * @param deptId    部门ID
     * @return 用户ID列表
     */
    @GetMapping(MicroServer.ShuduoduoUms.SERVER_PREFIX + "/rpc/company/dept/users")
    List<String> getUsersByDeptId(@RequestParam("companyId") String companyId, @RequestParam("deptId") String deptId);
}
