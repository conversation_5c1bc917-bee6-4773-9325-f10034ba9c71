package com.taiyi.shuduoduo.ums.rpc;


import com.taiyi.shuduoduo.ums.service.RoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/rpc/role")
public class RoleRpcController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private RoleService roleService;

    /**
     * 根据 用户ID 获取角色名称列表
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/listByUserId/{userId}")
    public List<String> getRoleListByUserId(@PathVariable("userId") String userId) {
        return roleService.getRoleStrByUserId(userId);
    }

    /**
     * 根据 用户ID 获取角色ID列表
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/listIdsByUserId/{userId}")
    public List<String> getRoleIdListByUserId(@PathVariable("userId") String userId) {
        return roleService.getRoleIdsByUserId(userId);
    }

    /**
     * 根据 用户ID 获取角色列表
     *
     * @param roleId 角色ID
     * @return 用户信息
     */
    @GetMapping("/users/{roleId}")
    public List<String> getUsersByRoleId(@PathVariable("roleId") String roleId, @RequestParam("companyId") String companyId) {
        return roleService.getUsersByRoleId(roleId, companyId);
    }
}