package com.taiyi.shuduoduo.ims.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.taiyi.common.data.mysql.entity.PageResult;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.aspect.RequestException;
import com.taiyi.shuduoduo.ims.entity.Plate;
import com.taiyi.shuduoduo.ims.exceptions.DremioException;
import com.taiyi.shuduoduo.ims.service.*;
import com.taiyi.shuduoduo.ims.vo.PlateVo;
import com.taiyi.shuduoduo.ims.vo.TipsConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 业务板块
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/plate")
@Validated
public class PlateController {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private PlateService plateService;

    @Autowired
    private DataFieldService dataFieldService;

    @Autowired
    private DimensionService dimensionService;

    @Autowired
    private RawService rawService;

    @Autowired
    private DwsService dwsService;

    /**
     * 板块-新增
     *
     * @param addPlate 板块信息
     * @return ResponseEntity
     */
    @PostMapping
    public ResponseEntity<ResponseVo.ResponseBean> save(@RequestBody @Validated PlateVo.AddPlate addPlate) {
        //查询名称重复
        Plate plate = BeanUtil.copy(addPlate, Plate.class);
        if (plateService.isNameExists(plate)) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.NAME_DUPLICATE);
        }
        //检查编码重复
        if (plateService.isCodeExists(plate)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.CODE_DUPLICATE);
        }
        try {
            String id = plateService.addPlate(addPlate);
            return ResponseVo.response(MessageCode.SUCCESS, null, id);
        } catch (Exception e) {
            logger.error("保存板块信息失败{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
    }

    /**
     * 板块-修改
     *
     * @param addPlate
     * @return ResponseEntity
     */
    @PutMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> updateById(@PathVariable("id") String id, @RequestBody @Validated PlateVo.EditPlate addPlate) {
        //从token获取公司id
        String companyId = CurrentUserUtil.get().getCompanyId();
        Plate plate = new Plate();
        plate.setName(addPlate.getName());
        plate.setCompanyId(companyId);
        if (plateService.exists(id, plate)) {
            return ResponseVo.response(MessageCode.REQUEST_ERROR, TipsConstant.NAME_DUPLICATE);
        }
        boolean f;
        try {
            f = plateService.updatePlateById(id, addPlate);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR, ExceptionUtil.stacktraceToString(e));
        }
        if (f) {
            return ResponseVo.response(MessageCode.SUCCESS);
        } else {
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 详情
     *
     * @param id id
     * @return 详情
     */
    @GetMapping("{id}")
    public ResponseEntity<ResponseVo.ResponseBean> detail(@PathVariable("id") String id) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, plateService.getDetail(id));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 板块-分页查询
     *
     * @param platePageVo 查询参数
     * @return ResponseEntity
     */
    @PostMapping("/page")
    @RequestException
    public ResponseEntity<ResponseVo.ResponseBean> page(@RequestBody @Validated PlateVo.PlatePageVo platePageVo) {
        try {
            PageResult page = plateService.queryPage(platePageVo);
            return ResponseVo.response(MessageCode.SUCCESS, page);
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 删除板块信息
     *
     * @param id 板块ID
     * @return bool
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseVo.ResponseBean> deleteById(@PathVariable("id") String id) {
        Plate plate = plateService.getById(id);
        if (ObjectUtil.isEmpty(plate)) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.PLATE_NOT_FOUND);
        }
        // 校验板块下是否有数据域
        if (ObjectUtil.isNotEmpty(dataFieldService.getDataFieldListByPlateId(id))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_DATA_FIELD_LIST_IN_THIS_PLATE);
        }
        // 校验板块下是否有维度
        if (ObjectUtil.isNotEmpty(dimensionService.getDimListByUnionId(id, 1, null))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_DIMENSION_LIST_IN_THIS_PLATE);
        }
        // 校验板块下是否有台账
        if (ObjectUtil.isNotEmpty(rawService.getListByUnionIdAndType(id, 1, null))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_RAW_LIST_IN_THIS_PLATE);
        }
        // 校验板块下是否有指标
        if (ObjectUtil.isNotEmpty(dwsService.getList(id, 1, null))) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, TipsConstant.HAS_METRIC_LIST_IN_THIS_PLATE);
        }
        try {
            // 校验数据湖中是否有空间
            if (plateService.hasDremioSpace(plate)) {
                return ResponseVo.response(MessageCode.PARAMETER_ERROR, "该板块在数据湖有空间，请先删除空间");
            }
            if (plateService.logicDeleteById(id)) {
                return ResponseVo.response(MessageCode.SUCCESS, "删除成功");
            } else {
                return ResponseVo.response(MessageCode.SYSTEM_SERVICE_ERROR, "删除失败");
            }
        } catch (Exception e) {
            logger.error(ExceptionUtil.stacktraceToString(e));
        }
        return ResponseVo.response(MessageCode.REQUEST_ERROR);
    }

    /**
     * 联动查询业务过程-查询所有板块
     */
    @GetMapping("/queryPlate")
    public ResponseEntity<ResponseVo.ResponseBean> queryProcess(@RequestParam String keyWord) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, plateService.queryPlate(keyWord, CurrentUserUtil.get().getCompanyId()));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }


    @GetMapping("/dws/queryPlate")
    public ResponseEntity<ResponseVo.ResponseBean> queryPlate(@RequestParam String keyWord) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, plateService.queryPlateByDws(keyWord));
        } catch (Exception e) {
            logger.warn(ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

    /**
     * 板块同步
     *
     * @param plateId 板块ID
     * @return T
     */
    @PostMapping("/syncData/{plateId}")
    public ResponseEntity<ResponseVo.ResponseBean> syncData(@PathVariable("plateId") String plateId) {
        try {
            return ResponseVo.response(MessageCode.SUCCESS, plateService.syncPlate(plateId));
        } catch (DremioException e) {
            logger.error("板块同步失败:{}", e.getMessage());
            return ResponseVo.response(MessageCode.REQUEST_ERROR, e.getMessage());
        } catch (Exception e) {
            logger.error("板块同步失败:{}", ExceptionUtil.stacktraceToString(e));
            return ResponseVo.response(MessageCode.REQUEST_ERROR);
        }
    }

}
