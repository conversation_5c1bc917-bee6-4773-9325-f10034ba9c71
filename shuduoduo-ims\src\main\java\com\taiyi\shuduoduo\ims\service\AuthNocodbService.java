package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.ims.dao.AuthNocodbDao;
import com.taiyi.shuduoduo.ims.entity.AuthNocodb;
import com.taiyi.shuduoduo.ims.nocodb.service.NocoDbApiService;
import com.taiyi.shuduoduo.nocodb.api.dto.SqliteMasterDTO;
import com.taiyi.shuduoduo.nocodb.api.servie.NcProjectRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AuthNocodbService extends CommonMysqlService<AuthNocodbDao, AuthNocodb> {
    @Override
    public Class<AuthNocodb> getEntityClass() {
        return AuthNocodb.class;
    }

    @Autowired
    private NcProjectRpcService ncProjectRpcService;

    @Autowired
    private NocoDbApiService nocoDbApiService;


    public AuthNocodb getAuthInfoByCompanyId(String companyId) {
        QueryWrapper<AuthNocodb> wrapper = new QueryWrapper<>();
        wrapper.eq("company_id", companyId).eq("if_deleted", false).last("LIMIT 1");
        return super.getOne(wrapper);
    }

    /**
     * 获取权限表
     *
     * @param comId 公司ID
     * @return 权限表
     */
    public List<String> getAuthTableList(String comId) {
        List<String> res = new ArrayList<>();
        AuthNocodb authNocodb = this.getAuthInfoByCompanyId(comId);
        if (null != authNocodb) {
            List<SqliteMasterDTO> tables = ncProjectRpcService.showTablesWithCompanyPrefix(authNocodb.getProjectPrefix());
            tables.forEach(sqliteMasterDTO -> res.add(sqliteMasterDTO.getName().replace(authNocodb.getProjectPrefix(), "")));
        }
        return res;
    }

    /**
     * 获取权限表
     *
     * @param table 公司ID
     * @return 权限表
     */
    public List<String> getColumns(String table) {
        List<String> res = new ArrayList<>();
        AuthNocodb authNocodb = this.getAuthInfoByCompanyId(CurrentUserUtil.get().getCompanyId());
        if (null != authNocodb) {
            res = ncProjectRpcService.showColumns(authNocodb.getProjectPrefix() + table);
        }
        return res;
    }

    public Boolean insertData(String projectName, String tableName, String body) {
        return nocoDbApiService.insertData(projectName, tableName, body);
    }
}