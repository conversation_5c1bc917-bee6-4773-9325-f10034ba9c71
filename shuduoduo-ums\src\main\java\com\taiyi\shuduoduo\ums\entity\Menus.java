package com.taiyi.shuduoduo.ums.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ums_menus")
public class Menus extends CommonMySqlEntity {
    private Boolean ifDeleted;

    private Boolean disabled;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 排序
     */
    private Long orderBy;

    /**
     * 父级ID
     */
    private String pid;

    /**
     * 菜单路由
     */
    private String routeUrl;

    /**
     * 前端ID
     */
    private String webId;

}