package com.taiyi.shuduoduo.ims.rpc;

import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ims.api.dto.DimDTO;
import com.taiyi.shuduoduo.ims.entity.Dimension;
import com.taiyi.shuduoduo.ims.service.DimensionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rpc/dim")
public class DimRpcController {
    @Autowired
    DimensionService dimensionService;

    /**
     * 根据板块ID获取维度列表
     *
     * @return 维度列表
     */
    @GetMapping("/getDimListByPlateId")
    public List<DimDTO> getDimListByPlateId(@RequestParam("plateId") String plateId) {
        List<Dimension> dimensionListByPlateId = dimensionService.getDimListByUnionId(plateId, 1, null);
        return BeanUtil.copyList(dimensionListByPlateId, DimDTO.class);
    }
}
