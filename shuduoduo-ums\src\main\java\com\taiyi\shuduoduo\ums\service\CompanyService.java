package com.taiyi.shuduoduo.ums.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.common.util.BeanUtil;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.shuduoduo.nocodb.api.dto.NcProjectDTO;
import com.taiyi.shuduoduo.nocodb.api.dto.SqliteMasterDTO;
import com.taiyi.shuduoduo.nocodb.api.servie.NcProjectRpcService;
import com.taiyi.shuduoduo.ums.dao.CompanyDao;
import com.taiyi.shuduoduo.ums.entity.Company;
import com.taiyi.shuduoduo.ums.vo.CompanyVo;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class CompanyService extends CommonMysqlService<CompanyDao, Company> {


    @Override
    public Class<Company> getEntityClass() {
        return Company.class;
    }

    @Autowired
    public CompanyDao companyDao;

    @Autowired
    private UserCompanyService userCompanyService;

    @Autowired
    private NcProjectRpcService ncProjectRpcService;


    /**
     * 返回公司列表
     *
     * @param keyWord 关键字
     * @return 公司列表
     */
    public List<CompanyVo.CompanyList> companyList(String keyWord) {
        QueryWrapper<Company> companyQueryWrapper = new QueryWrapper<>();
        companyQueryWrapper.eq("if_deleted", 0)
                .like(StringUtils.isNotBlank(keyWord), "name", keyWord);
        List<Company> companyList = companyDao.selectList(companyQueryWrapper);
        return BeanUtil.copyList(companyList, CompanyVo.CompanyList.class);
    }

    /**
     * 添加公司
     *
     * @param company 添加公司
     * @return 结果
     */
    public boolean addCompany(Company company) {

        if (1 == company.getType()) {
            //企微
        } else if (2 == company.getType()) {
            //钉钉
        } else if (3 == company.getType()) {
            //飞书


        } else {
            //其他
        }


        return true;
    }

    /**
     * 获取公司列表
     *
     * @param keyWord 模糊搜索
     * @return 公司列表
     */
    public List<Company> getCompanyList(String keyWord) {
        QueryWrapper<Company> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("if_deleted", 0).like(StringUtils.isNotBlank(keyWord), "name", keyWord);
        return companyDao.selectList(queryWrapper);
    }

    /**
     * 根据 CorpID获取公司信息
     *
     * @param corpid 公司ID
     * @return 公司信息
     */
    public Company getByCorpId(String corpid) {
        QueryWrapper<Company> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("if_deleted", 0).eq("corp_id", corpid).last("limit 1");
        return getOne(queryWrapper);
    }

    /**
     * 更新公司信息
     *
     * @param agentId 应用ID
     * @param corpId  企业ID
     */
    public void updateInfo(String agentId, String corpId) {
        // 查询是否有该企业信息
        Company company = getByCorpId(corpId);
        if (null != company) {
            if (StringUtils.isBlank(company.getAgentId())) {
                company.setAgentId(agentId);
                super.updateById(company.getId(), company);
            }
        }
    }

    public NcProjectDTO getUserNocoInfo(String comId) {
        Company company = this.getById(comId);
        return ncProjectRpcService.getNcProjectInfo(company.getName());
    }

    /**
     * 获取权限表
     *
     * @param comId 公司ID
     * @return 权限表
     */
    public List<String> getAuthTableList(String comId) {
        List<String> res = new ArrayList<>();
        Company company = this.getById(comId);
        NcProjectDTO projects = ncProjectRpcService.getNcProjectInfo(company.getName());
        if (null != projects) {
            List<SqliteMasterDTO> tables = ncProjectRpcService.showTablesWithCompanyPrefix(projects.getPrefix());
            tables.forEach(sqliteMasterDTO -> res.add(sqliteMasterDTO.getName()));
        }
        return res;
    }

    /**
     * 获取权限表
     *
     * @param table 公司ID
     * @return 权限表
     */
    public List<String> getColumns(String table) {
        List<String> res = new ArrayList<>();
        Company company = this.getById(CurrentUserUtil.get().getCompanyId());
        NcProjectDTO projects = ncProjectRpcService.getNcProjectInfo(company.getName());
        if (null != projects) {
            res = ncProjectRpcService.showColumns(table);
        }
        return res;
    }

    public List<Company> getList() {
        QueryWrapper<Company> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("if_deleted", 0);
        return super.list(queryWrapper);
    }

    public List<String> getDeptList(String companyId, String userId) {
        return userCompanyService.getDeptName(companyId, userId);
    }

    public List<String> getUserListByDeptId(String companyId, String deptId) {
        return userCompanyService.getUserListByDeptId(companyId, deptId);
    }

    public boolean updateDwsAuthed(String id, Boolean ifAuthed) {
        UpdateWrapper<Company> wrapper = new UpdateWrapper<>();
        wrapper.eq("id", id);
        wrapper.set("if_dws_authed", ifAuthed);
        return super.update(wrapper);
    }
}