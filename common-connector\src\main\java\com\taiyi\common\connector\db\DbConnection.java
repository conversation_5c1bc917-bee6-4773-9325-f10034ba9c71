package com.taiyi.common.connector.db;

import com.taiyi.common.connector.entity.DbEntity;

import java.sql.Connection;

/**
 * <AUTHOR>
 */
public interface DbConnection<E extends DbEntity> {

    /**
     * 初始化
     *
     * @param e e
     */
    public void init(E e);

    /**
     * 获取连接
     *
     * @param e e
     * @return Connection
     */
    public Connection getConnection(E e);

    /**
     * 关闭连接
     *
     * @param connection connection
     */
    public void close(Connection connection);
}
