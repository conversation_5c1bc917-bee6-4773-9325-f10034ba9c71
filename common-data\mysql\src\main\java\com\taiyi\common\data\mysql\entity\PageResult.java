package com.taiyi.common.data.mysql.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 返回前端列表页需要的封装Page实体
 */
public class PageResult<T> {

    private long total = 0;

    private long pageSize = 10;

    private long pageNo = 1;

    List<T> list = new ArrayList<>();

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public long getPageSize() {
        return pageSize;
    }

    public void setPageSize(long pageSize) {
        this.pageSize = pageSize;
    }

    public long getPageNo() {
        return pageNo;
    }

    public void setPageNo(long pageNo) {
        this.pageNo = pageNo;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}
