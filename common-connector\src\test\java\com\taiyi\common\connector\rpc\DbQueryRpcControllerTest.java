package com.taiyi.common.connector.rpc;

import com.alibaba.fastjson.JSON;
import com.taiyi.shuduoduo.common.connector.api.dto.DbDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.junit.jupiter.api.Assertions.*;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DbQueryRpcControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;
    private static final String json_utf8 = "application/json;charset=UTF-8";


    @BeforeEach
    public void env() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    void queryBySql() {
        DbDto.DbQueryBySql dbDto = new DbDto.DbQueryBySql();
        dbDto.setHost("**************");
        dbDto.setPort(19048);
        dbDto.setDatabase("zao_001");
        dbDto.setType("DREMIO");
        dbDto.setUsername("");
        dbDto.setPassword("!");
        dbDto.setSql("SELECT DISTINCT dws_kuanb_xm.JFSJNAME AS \"dws_kuanb_xm.JFSJNAME\" FROM dws.dws_kuanb_xm LIMIT 100 OFFSET 0");
        try {
            String r = mockMvc.perform(MockMvcRequestBuilders.post("/rpc/engine/db/queryBySql")
                    .characterEncoding("utf8")
                    .content(JSON.toJSONString(dbDto))
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .accept(json_utf8))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andDo(MockMvcResultHandlers.print())
                    .andReturn().getResponse().getContentAsString();
            System.out.println(r);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}