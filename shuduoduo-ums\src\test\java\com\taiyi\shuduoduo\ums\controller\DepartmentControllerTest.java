package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.taiyi.common.data.mysql.entity.MyQuery;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DepartmentControllerTest {
    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;
    private static String json_utf8 = "application/json;charset=UTF-8";

    private static String uuid = IdUtil.simpleUUID();

    @BeforeEach
    public void env() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    @Order(10)
    void userDepartmentPage() throws Exception {
        MyQuery myQuery=new MyQuery();
        myQuery.setWrapper(new QueryWrapper());
        Page page = new Page();
        page.setSize(10);
        page.setTotal(0);
        page.setCurrent(0);
        myQuery.setPage(page);
        System.out.println(JSON.toJSONString(myQuery));
        String r = mockMvc.perform(MockMvcRequestBuilders.post("/department-user/page/"+2)
                .content(JSON.toJSONString(myQuery))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    void companyUserPage() throws Exception {
        MyQuery myQuery=new MyQuery();
        myQuery.setWrapper(new QueryWrapper());
        Page page = new Page();
        page.setSize(10);
        page.setTotal(0);
        page.setCurrent(0);
        myQuery.setPage(page);
        System.out.println(JSON.toJSONString(myQuery));
        String r = mockMvc.perform(MockMvcRequestBuilders.post("/company-user/page/"+1)
                .content(JSON.toJSONString(myQuery))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(30)
    void departmentList() throws Exception {
        String r = mockMvc.perform(MockMvcRequestBuilders.get("/department/list/"+"7172070a1c264c2bb6bb201e17bf8ee7")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
        System.out.println(r);
    }
}