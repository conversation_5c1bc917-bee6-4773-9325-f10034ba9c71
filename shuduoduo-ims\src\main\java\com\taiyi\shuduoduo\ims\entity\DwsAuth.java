package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_dws_auth")
public class DwsAuth extends CommonMySqlEntity {

    private String companyId;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 指标ID
     */
    private String dwsId;

    /**
     * 是否开启权限
     */
    private Boolean ifAuthed;

    private Boolean ifDeleted;

}