package com.taiyi.shuduoduo.ums.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ums.dao.CompanyDremioDao;
import com.taiyi.shuduoduo.ums.entity.CompanyDremio;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class CompanyDremioService extends CommonMysqlService<CompanyDremioDao, CompanyDremio> {
    @Override
    public Class<CompanyDremio> getEntityClass() {
        return CompanyDremio.class;
    }

    public CompanyDremio getDremioInfoByComId(String companyId) {
        QueryWrapper<CompanyDremio> wrapper = new QueryWrapper<>();
        wrapper.eq("com_id", companyId).last("LIMIT 1");
        return super.getOne(wrapper);
    }
}