package com.taiyi.shuduoduo.ums.vo;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import lombok.Data;

/**
 * 角色类
 *
 * <AUTHOR>
 */
@Data
public class RoleVo {

    private String id;

    /**
     * 是否内置角色，0: 否, 1:是
     */
    private Boolean ifBuiltIn;

    /**
     * 名称
     */
    private String name;

    /**
     * 角色说明
     */
    private String remark;

    /**
     * 是否删除，0: 否, 1:是
     */
    private Boolean ifDeleted;

    /**
     * 分页查询参数
     */
    @Data
    public static class PageParam extends CommonMySqlPageVo {


    }

}
