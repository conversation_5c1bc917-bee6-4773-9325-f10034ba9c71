package com.taiyi.shuduoduo.ims.aspect;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.taiyi.common.aspect.ApiLogPointCut;
import com.taiyi.common.util.CurrentUserUtil;
import com.taiyi.common.util.IpUtil;
import com.taiyi.shuduoduo.sms.api.dto.LogDTO;
import com.taiyi.shuduoduo.sms.api.service.LogRpcService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.concurrent.Executor;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * api日志切面
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class ApiLogAspect {

    private Logger logger = LoggerFactory.getLogger(ApiLogAspect.class);

    @Autowired
    private LogRpcService logRpcService;

    @Autowired
    private Executor taskExecutor;


    @Pointcut("execution(public * com.taiyi.shuduoduo.ims.controller.*.*(..))")
    public void apiLogPointCutIms() {
    }


    @Before("apiLogPointCutIms()")
    public void before(JoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            logger.debug("非 Web 请求，跳过日志记录");
            return;
        }
        HttpServletRequest request = attributes.getRequest();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取目标类上的目标注解
        Method method = signature.getMethod();
        ApiLogPointCut annotation = method.getAnnotation(ApiLogPointCut.class);
        if (annotation == null) {
            return;
        }
        // 构建日志对象
        LogDTO logDTO = new LogDTO();
        CurrentUserUtil.CurrentUser currentUser = CurrentUserUtil.get();
        logDTO.setCompanyId(currentUser.getCompanyId());
        logDTO.setUsername(currentUser.getRealName());
        logDTO.setCompanyId(CurrentUserUtil.get().getCompanyId());
        logDTO.setUsername(CurrentUserUtil.get().getRealName());
        logDTO.setPath(request.getContextPath() + request.getServletPath());
        logDTO.setIp(IpUtil.getRealIP(request));
        logDTO.setDoAction(annotation.name());
        logDTO.setActionDescription(annotation.description());
        logDTO.setParameters(JSON.toJSONString(joinPoint.getArgs()));
        try {
            taskExecutor.execute(() -> logRpcService.save(logDTO));
        } catch (Exception e) {
            logger.error("保存日志失败,{}", ExceptionUtil.stacktraceToString(e));
        }
    }

}
