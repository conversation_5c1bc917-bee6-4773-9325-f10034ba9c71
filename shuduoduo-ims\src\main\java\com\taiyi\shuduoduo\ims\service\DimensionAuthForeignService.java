package com.taiyi.shuduoduo.ims.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.taiyi.common.data.mysql.service.CommonMysqlService;
import com.taiyi.shuduoduo.ims.dao.DimensionAuthForeignDao;
import com.taiyi.shuduoduo.ims.entity.DimensionAuthForeign;
import com.taiyi.shuduoduo.ims.vo.DimAuthForeignVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class DimensionAuthForeignService extends CommonMysqlService<DimensionAuthForeignDao, DimensionAuthForeign> {
    @Override
    public Class<DimensionAuthForeign> getEntityClass() {
        return DimensionAuthForeign.class;
    }

    @Autowired
    private DimensionAuthService dimensionAuthService;

    /**
     * 编辑权限关联关系
     *
     * @param t 关联参数
     * @return bool
     */
    public boolean saveDimAuth(DimAuthForeignVo.InsertVo t) {
        boolean f = false;
        // 查询是否已绑定关联关系
        DimAuthForeignVo.DetailVo detail = this.getDetail(t.getDimAuthId());
        if (detail != null) {
            // 删除以前的关联关系
            this.deleteByDimAuthId(t.getDimAuthId());
        }
        for (DimAuthForeignVo.ForeignVo foreign : t.getForeigns()) {
            DimensionAuthForeign dimAuthForeign = new DimensionAuthForeign();
            dimAuthForeign.setDimAuthId(t.getDimAuthId());
            dimAuthForeign.setAuthAgent(t.getAuthAgent());
            dimAuthForeign.setAuthColumn(t.getAuthColumn());
            dimAuthForeign.setRawColumn(foreign.getRawColumn());
            dimAuthForeign.setTableColumn(foreign.getTableColumn());
            f = super.save(dimAuthForeign);
        }
        if (f) {
            // 修改状态
            return dimensionAuthService.updateBySetting(t.getDimAuthId(), t.getStatus(), t.getOperator());
        }
        return f;
    }

    private void deleteByDimAuthId(String dimAuthId) {
        QueryWrapper<DimensionAuthForeign> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("dim_auth_id", dimAuthId);
        List<DimensionAuthForeign> list = super.list(wrapper);
        list.forEach(dimAuthForeign -> {
            super.logicDeleteById(dimAuthForeign.getId());
        });
    }

    public DimAuthForeignVo.DetailVo getDetail(String dimAuthId) {
        DimAuthForeignVo.DetailVo res = new DimAuthForeignVo.DetailVo();
        QueryWrapper<DimensionAuthForeign> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("dim_auth_id", dimAuthId);
        List<DimensionAuthForeign> list = super.list(wrapper);
        List<DimAuthForeignVo.ForeignVo> foreignVoList = new ArrayList<>();
        if (list.size() > 0) {
            res.setAuthAgent(list.get(0).getAuthAgent());
            res.setAuthColumn(list.get(0).getAuthColumn());
            for (DimensionAuthForeign dimAuthForeign : list) {
                DimAuthForeignVo.ForeignVo foreignVo = new DimAuthForeignVo.ForeignVo();
                foreignVo.setRawColumn(dimAuthForeign.getRawColumn());
                foreignVo.setTableColumn(dimAuthForeign.getTableColumn());
                foreignVoList.add(foreignVo);
            }
        }
        res.setForeigns(foreignVoList);
        return res;
    }

    public List<DimensionAuthForeign> getListByDimAuthId(String dimAuthId) {
        QueryWrapper<DimensionAuthForeign> wrapper = new QueryWrapper<>();
        wrapper.eq("if_deleted", false).eq("dim_auth_id", dimAuthId);
        return super.list(wrapper);
    }
}