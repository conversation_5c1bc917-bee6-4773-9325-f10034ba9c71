package com.taiyi.shuduoduo.ims;

import com.taiyi.common.entity.MicroServer;

import com.ulisesbocchio.jasyptspringboot.annotation.EnableEncryptableProperties;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.oas.annotations.EnableOpenApi;

/**
 * 指标模块
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
@EnableFeignClients(MicroServer.BASE_PACKAGES)
@EnableEncryptableProperties
@MapperScan(basePackages = {"com.taiyi.shuduoduo.ims.dao"})
@EnableOpenApi
public class ShuduoduoImsApplication {
    public static void main(String[] args) {
        SpringApplication.run(ShuduoduoImsApplication.class);
    }
}
