package com.taiyi.common.aspect;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.taiyi.common.entity.HttpHeaderConstant;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;

/**
 * RPC切面
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class WebRpcLogAspect {

    private Logger logger = LoggerFactory.getLogger(WebRpcLogAspect.class);


    @Pointcut("execution(public * com.taiyi..rpc.*.*(..))")
    public void rpcLogPointCut() {
    }


    @Before("rpcLogPointCut()")
    public void before(JoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        String requestId = request.getHeader(HttpHeaderConstant.REQUEST_ID);
        String device = request.getHeader(HttpHeaderConstant.DEVICE);

        String requestJson = null;
        try {
            requestJson = JSON.toJSONString(joinPoint.getArgs());
        } catch (Exception e) {
            logger.error("rpc request json error{}", ExceptionUtil.stacktraceToString(e));
        }

        logger.debug("rpc request :{}, device: {}, path: {}, method: {}, request params :{}, request json: {}",
                requestId, device, request.getContextPath() + request.getServletPath(),
                request.getMethod(), Arrays.toString(joinPoint.getArgs()), requestJson);

    }

    @AfterReturning(returning = "ret", pointcut = "rpcLogPointCut()")
    public void afterReturn(Object ret) throws Throwable {

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        String requestId = request.getHeader(HttpHeaderConstant.REQUEST_ID);
        String device = request.getHeader(HttpHeaderConstant.DEVICE);
        logger.debug("rpc request :{}, device: {}, response data: {}", requestId, device, JSON.toJSONString(ret).length() > 220 ? JSON.toJSONString(ret).substring(0, 200) : JSON.toJSONString(ret));
    }

}
