package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_raw_plate_source")
public class RawPlateSource extends CommonMySqlEntity {
    private String companyId;

    private String rawId;

    private String sourceId;

    private Boolean ifDeleted;

}