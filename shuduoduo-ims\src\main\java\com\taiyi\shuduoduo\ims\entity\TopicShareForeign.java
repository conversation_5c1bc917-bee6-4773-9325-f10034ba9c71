package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_topic_share_foreign")
public class TopicShareForeign extends CommonMySqlEntity {
    /**
     * 分享者
     */
    private String fromUser;

    /**
     * 1、用户2、部门、3、角色
     */
    private Integer userType;

    /**
     * 用户ID/部门ID/角色ID
     */
    private String toUser;

    private Boolean ifDeleted;

}