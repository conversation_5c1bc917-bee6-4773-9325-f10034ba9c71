package com.taiyi.shuduoduo.ums.init;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 初始化数据
 *
 * <AUTHOR>
 */
@Component
public class DataInit implements ApplicationRunner {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Value("${spring.profiles.active}")
    private String springProfile;


    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("UMS 启动成功，初始化数据开始。");
    }
}
