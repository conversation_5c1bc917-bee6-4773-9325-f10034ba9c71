package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * 指标信息
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_dws")
public class Dws extends CommonMySqlEntity {
    /**
     * 指标别名，以“，”分割
     */
    private String alias;

    /**
     * 锚定值
     */
    private String anchorValue;

    /**
     * 应用系统
     */
    private String applicationSystem;

    /**
     * 基础指标：以“，”分割
     */
    private String basicDws;

    /**
     * 脑图口径
     */
    private String brainMap;

    /**
     * 业务过程ID
     */
    private String busProcessId;

    /**
     * 业务负责人：以‘，’分割
     */
    private String businessLeaders;

    /**
     * 业务流向图在线文档地址
     */
    private String businessOnlineUri;

    /**
     * 业务规则
     */
    private String businessRole;

    /**
     * 指标编码
     */
    private String code;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 数据域ID
     */
    private String dataFieldId;

    /**
     * 数据格式
     */
    private String dataFormat;

    /**
     * 数据来源页面地址
     */
    private String dataSourcePage;

    /**
     * 数据来源表名称
     */
    private String dataSourceTable;

    /**
     * 数据来源表字段名称
     */
    private String dataSourceTableField;

    /**
     * 单位
     */
    private String dataUnit;

    /**
     * 指标描述
     */
    private String description;

    /**
     * 统计维度：以“，”分割
     */
    private String dimIds;

    /**
     * 指标文档
     */
    private String document;

    /**
     * ETL 加載説明
     */
    private String etlDesc;

    /**
     * 英文简称
     */
    private String enAbbr;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 表达式
     */
    private String expressions;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

    /**
     * 是否发布
     */
    private Boolean ifPublished;

    /**
     * 指标级别
     */
    private String level;

    /**
     * 指标名称
     */
    private String name;

    private Long orderBy;

    /**
     * 统计周期维度，以“，”分割
     */
    private String periodValue;

    /**
     * 业务负责部门：以“，”分割
     */
    private String plateDeptIds;

    /**
     * 板块ID
     */
    private String plateId;

    /**
     * 数据来源系统：以“，”分割
     */
    private String plateSourceIds;

    /**
     * 参考标准
     */
    private String referStandard;

    /**
     * 参考标准名称
     */
    private String referStandardName;

    /**
     * 相关调度
     */
    private String relatedDispatch;

    /**
     * 指标唯一标识
     */
    private String sno;

    /**
     * 统计频度
     */
    private String statistics;

    /**
     * 技术口径
     */
    private String technicalCaliber;

    /**
     * 技术负责部门，以‘，’分割
     */
    private String technicalDepts;

    /**
     * 技术负责人，以‘，’分割
     */
    private String technicalLeaders;

    /**
     * 指标类型：1、基础指标2、衍生指标3、复合指标
     */
    private Integer type;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 可用分析
     */
    private String usableAnalysis;

    /**
     * 版本号
     */
    private Long versionNum;

}