package com.taiyi.shuduoduo.ims.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.taiyi.common.data.mysql.entity.CommonMySqlEntity;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "ims_plate_dim_folder")
public class PlateDimFolder extends CommonMySqlEntity {
    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 板块ID
     */
    private String plateId;

    /**
     * 维度文件夹名称
     */
    private String name;

    /**
     * 是否删除
     */
    private Boolean ifDeleted;

}