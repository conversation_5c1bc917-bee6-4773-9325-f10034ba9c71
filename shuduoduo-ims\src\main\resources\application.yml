server:
  port: 8880
  servlet:
    context-path: /shuduoduo/ims
  compression:
    enabled: true
    mime-types: application/json,text/html,text/plain,application/javascript,text/css,text/javascript

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: shuduoduo-ims
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  cloud:
    nacos:
      discovery:
        metadata:
          management:
            context-path: /shuduoduo/ims/actuator

  # 选择开发环境或者生产环境
  profiles:
    active: local

feign:
  sentinel:
    enabled: true #打开sentinel对feign的支持

# 设置RPC的默认超时时间 30S
ribbon:
  ReadTimeout: 300000
  ConnectTimeout: 300000

management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always

logging:
  config: classpath:logback-spring.xml