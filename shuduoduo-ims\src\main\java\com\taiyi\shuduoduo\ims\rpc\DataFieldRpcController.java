package com.taiyi.shuduoduo.ims.rpc;

import com.taiyi.common.util.BeanUtil;
import com.taiyi.shuduoduo.ims.api.dto.DataFieldDTO;
import com.taiyi.shuduoduo.ims.entity.DataField;
import com.taiyi.shuduoduo.ims.service.DataFieldService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/rpc/dataField")
public class DataFieldRpcController {

    @Autowired
    private DataFieldService dataFieldService;
    /**
     * 根据板块ID获取数据域列表
     *
     * @return 数据域列表
     */
    @GetMapping("/getDataFieldListByPlateId")
    public List<DataFieldDTO> getDataFieldListByPlateId(@RequestParam("plateId") String plateId){
        List<DataField> dataFieldListByPlateId = dataFieldService.getDataFieldListByPlateId(plateId);
        return BeanUtil.copyList(dataFieldListByPlateId,DataFieldDTO.class);
    }
}
