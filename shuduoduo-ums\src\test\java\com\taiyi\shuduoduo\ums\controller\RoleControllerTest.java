package com.taiyi.shuduoduo.ums.controller;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taiyi.shuduoduo.ums.entity.Role;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

@AutoConfigureMockMvc
@SpringBootTest
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class RoleControllerTest {
    @Autowired
    private WebApplicationContext webApplicationContext;
    private MockMvc mockMvc;
    private static String json_utf8 = "application/json;charset=UTF-8";

    private static String uuid = IdUtil.simpleUUID();

    @BeforeEach
    public void env() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    @Test
    @Order(10)
    void save() throws Exception {
        Role role=new Role();
        role.setId(uuid);
        role.setIfBuiltIn(true);
        role.setName("h"+uuid.substring(0,5));
        role.setRemark("111111111111111111111");
        String r = mockMvc.perform(MockMvcRequestBuilders.post("/role")
                .content(JSON.toJSONString(role))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(20)
    void updateById() throws Exception {
        Role role=new Role();
        role.setIfBuiltIn(true);
        role.setName("hh"+uuid.substring(0,5));
        role.setRemark("2222222222");
        String r = mockMvc.perform(MockMvcRequestBuilders.put("/role/" + uuid)
                .content(JSON.toJSONString(role))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(30)
    void deleteById() throws Exception {
        String r = mockMvc.perform(MockMvcRequestBuilders.delete("/role/" + uuid)
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }

    @Test
    @Order(40)
    void page() throws Exception {
        JSONObject jo = JSONObject.parseObject(new String("{'pageNo':0,'pageSize':10}"));
        String r = mockMvc.perform(MockMvcRequestBuilders.post("/user/backend/page")
                .content(JSON.toJSONString(jo))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(json_utf8))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andDo(MockMvcResultHandlers.print())
                .andReturn().getResponse().getContentAsString();
    }
}