package com.taiyi.shuduoduo.sms.api.service;

import com.taiyi.common.entity.MicroServer;
import com.taiyi.shuduoduo.sms.api.dto.DictDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 数据字典RPC
 *
 * <AUTHOR>
 */
@FeignClient(value = MicroServer.ShuduoduoSms.SERVER_NAME)
public interface DictRpcService {

    /**
     * 根据类型获取字典列表
     *
     * @param dictType 字典类型
     * @return 字典列表
     */
    @GetMapping(MicroServer.ShuduoduoSms.SERVER_PREFIX + "/rpc/dict/list")
    List<DictDTO> list(@RequestParam("dictType") String dictType);

    /**
     * 根据类型获取最大值
     *
     * @param dictType 字典类型
     * @return 最大值
     */
    @GetMapping(MicroServer.ShuduoduoSms.SERVER_PREFIX + "/rpc/dict/maxValue")
    Integer maxValue(@RequestParam("dictType") String dictType);

    /**
     * 根据字典类型和字典值获取字典信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @return 字典信息
     */
    @PostMapping(MicroServer.ShuduoduoSms.SERVER_PREFIX + "/rpc/dict/detail/value/{dictType}")
    DictDTO getByTypeAndValue(@PathVariable("dictType") String dictType, @RequestBody String dictValue);

    /**
     * 根据字典类型和字典名称获取字典信息
     *
     * @param dictType 字典类型
     * @param dictName 字典名称
     * @return 字典信息
     */
    @PostMapping(MicroServer.ShuduoduoSms.SERVER_PREFIX + "/rpc/dict/detail/name/{dictType}")
    DictDTO getByTypeAndName(@PathVariable("dictType") String dictType, @RequestBody String dictName);

    /**
     * 根据字典ID获取字典信息
     *
     * @param id id
     * @return 字典信息
     */
    @GetMapping(MicroServer.ShuduoduoSms.SERVER_PREFIX + "/rpc/dict/{id}")
    DictDTO detail(@PathVariable String id);


    /**
     * 设置字典信息 // FIXME: 2020/10/15 修改时传ID
     *
     * @param dict dict
     * @return boolean
     */
    @PostMapping(MicroServer.ShuduoduoSms.SERVER_PREFIX + "/rpc/dict")
    boolean save(@RequestBody @Validated DictDTO dict);

    /**
     * 删除字典信息
     *
     * @param id 字典ID
     * @return boolean
     */
    @DeleteMapping(MicroServer.ShuduoduoSms.SERVER_PREFIX + "/rpc/dict/{id}")
    Boolean delete(@PathVariable String id);
}
