package com.taiyi.shuduoduo.ims.aspect;

import com.taiyi.common.data.mysql.vo.CommonMySqlPageVo;
import com.taiyi.common.entity.MessageCode;
import com.taiyi.common.entity.ResponseVo;
import com.taiyi.shuduoduo.ims.util.SqlBuilderUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 请求参数校验
 *
 * <AUTHOR>
 */
@Aspect
@Component
public class ApiRequestAspect {

    private final Logger logger = LoggerFactory.getLogger(ApiRequestAspect.class);


    @Pointcut("execution(public * com.taiyi.shuduoduo.ims.controller.*.page(..))")
    public void apiRequestPointCut() {

    }

    @Before("apiRequestPointCut()")
    public void before(JoinPoint joinPoint) throws Throwable {
        // 接收到请求，记录请求内容
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String methodUrl = request.getRequestURL().toString();
        /**
         * 获取注解自定义参数
         */
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        RequestException methodResponseException = method.getAnnotation(RequestException.class);


        CommonMySqlPageVo pageVo = (CommonMySqlPageVo) joinPoint.getArgs()[0];
        String keyWord = pageVo.getKeyWord();
        if (null != keyWord) {
            if (SqlBuilderUtil.sqlValidate(keyWord) || keyWord.contains("'")) {
                throw new Exception("请求参数中有违反安全规则元素存在,拒绝访问!");
            }
        }
    }


    @Around("apiRequestPointCut()")
    public Object doAround(ProceedingJoinPoint pjp) {
        long startTime = System.currentTimeMillis();
        Object ob = null;
        // ob 为方法的返回值
        try {
            ob = pjp.proceed();
        } catch (Throwable throwable) {
            return ResponseVo.response(MessageCode.PARAMETER_ERROR, throwable.getCause().getMessage());
        }
        logger.info("性能监控（耗时） : " + (System.currentTimeMillis() - startTime) + "毫秒");
        return ob;
    }
}
