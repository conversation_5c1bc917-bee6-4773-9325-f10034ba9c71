package com.taiyi.shuduoduo.ims.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class RawObjectVo {

    @ApiModel("台账对象参数")
    @Data
    public static class InsertParam {

        /**
         * 台账ID
         */
        @ApiModelProperty("台账ID")
        private String rawId;

        /**
         * 对象名称
         */
        @ApiModelProperty("对象名称")
        private String name;

        private Long orderBy;
    }

    @Data
    public static class DetailResponse {

        private String name;

    }
}
